package cn.huolala.van.api.facade.model.enums;

import java.util.Arrays;

public enum ProjectCmdbReginEnum {
	DOMESTIC("cn"),
	OVERSEAS("over_sea");

	public final String golangEnumStr;

	ProjectCmdbReginEnum(String golangEnumStr) {
		this.golangEnumStr = golangEnumStr;
	}

	public static ProjectCmdbReginEnum fromGolangEnumStr(String str) {
		return Arrays.stream(ProjectCmdbReginEnum.values())
				.filter(i -> i.golangEnumStr.equals(str)).findFirst().orElse(null);
	}
}
