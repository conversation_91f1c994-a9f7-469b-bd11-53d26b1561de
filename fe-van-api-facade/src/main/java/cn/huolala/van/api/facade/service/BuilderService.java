package cn.huolala.van.api.facade.service;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.facade.model.BuildTaskDTO;
import cn.huolala.van.api.facade.model.GetBuildTasksParams;

import java.util.List;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;


@SuppressWarnings("unused")
@HermesService("/van/builder")
public interface BuilderService {
    long createBuildTask(BuildTaskDTO task);

    @Nullable
    BuildTaskDTO getBuildTask(long projectId, long taskId);

    /**
     * @param prevStatus Verify the previous status. If it does not match, throw a CONFLICT exception.
     *                   NOTE: If a null value is provided, that indicates nothing would be verified.
     */
    void updateBuildTaskStatus(long projectId,
                               long taskId,
                               @NonNull BuildTaskStatus status,
                               @Nullable BuildTaskStatus prevStatus);

    void updateBuildTaskBuildId(long projectId, long taskId, String buildId);

    void updateBuildTaskBranch(long projectId, long taskId, String branch);

    List<BuildTaskDTO> getBuildTasksByIds(GetBuildTasksParams params);

}
