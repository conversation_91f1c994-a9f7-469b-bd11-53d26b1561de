package cn.huolala.van.api.facade.service;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.model.ActiveTaskIds;
import cn.huolala.api.constants.model.WatchDogAllConfigView;
import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.facade.model.CanaryHistoryDTO;
import cn.huolala.van.api.facade.model.DeployDevParams;
import cn.huolala.van.api.facade.model.LegacyDeployConfigDTO;
import cn.huolala.van.api.facade.model.MetaDTO;
import cn.huolala.van.api.facade.model.ProjectConfigDTO;
import cn.huolala.van.api.facade.model.ProjectDomainInfoDTO;
import cn.huolala.van.api.facade.model.ProjectIntroDTO;
import cn.huolala.van.api.facade.model.ProjectQueryDTO;
import cn.huolala.van.api.facade.model.SubscribeGroupParams;
import cn.huolala.van.api.facade.model.TaskIntroDTO;
import cn.huolala.van.api.facade.model.UpdateWindowParams;
import cn.huolala.van.api.facade.model.UserInfoDTO;
import cn.huolala.van.api.facade.model.UserRoleInfoDTO;
import cn.huolala.van.api.facade.model.WindowParams;

import org.springframework.context.annotation.Description;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;


@HermesService("/van/misc")
public interface MiscService {
    @NonNull
    TaskIntroDTO getTaskIntro(String projectName, Long taskId);

    @NonNull
    List<String> getImportantProjects();

    @NonNull
    ProjectIntroDTO getProjectIntro(@NonNull Long projectId);

    @NonNull
    ProjectIntroDTO getProjectIntroByName(@NonNull String projectName);

    @NonNull
    ProjectConfigDTO getProjectConfig(@NonNull Long projectId);

    @NonNull
    ProjectConfigDTO getProjectConfigByName(@NonNull String projectName);

    @NonNull
    List<UserRoleInfoDTO> getProjectRoles(@NonNull Long projectId);

    @NonNull
    List<UserRoleInfoDTO> getProjectRolesByName(@NonNull String projectName);

    @NonNull
    Map<String, Long> getAllProjects();

    @NonNull
    Boolean getOffwebStatus(@NonNull Long projectId);

    /**
        * NOTE: This method is called from WatchDog collector (Golang).
        */
    @Nullable
    @Description("NOTE: This method may return a null, that indicates the value is not modified.")
    WatchDogAllConfigView getAllWatchDogConfig(@NonNull Env env,
                    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @Nullable OffsetDateTime ifModifiedSince);

    @Nullable
    @Description("Find task IDs where the project is currently in the canaries.\n" +
                    "NOTE: This method may return a null, that indicates the value is not modified.")
    ActiveTaskIds findActiveTaskIds(
                    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @Nullable OffsetDateTime ifModifiedSince);

    @Nullable
    ProjectQueryDTO getProjectQueryInfo(@NonNull String idOrName);

    @Nullable
    UserInfoDTO getUserByUniqId(@NonNull String uniqId);

    Long addWindow(@NonNull WindowParams params);

    void updateWindow(@NonNull UpdateWindowParams params);

    void setDomainForProject(@NonNull String projectName, @NonNull String domain);

    void subscribeGroup(@NonNull SubscribeGroupParams params);

    void unsubscribeGroup(@NonNull SubscribeGroupParams params);

    void refreshProjectDomainCache();

    List<ProjectDomainInfoDTO> listAllProjectDomain();

    MetaDTO getMeta(@NonNull long metaId, @NonNull int type);

    void updateMeta(@NonNull long metaId, @NonNull int type, @NonNull String meta);

    void deployDev(DeployDevParams params);

    CanaryHistoryDTO getProjectCanaryHistory(long projectId, long canaryId);

    LegacyDeployConfigDTO getProjectCanary(long projectId, String region);

    boolean isSuperAdmin(String uniqId);
}
