package cn.huolala.van.api.facade.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ProjectDomainInfoDTO {

    private List<String> domain;

    @JsonProperty("app_id")
    private String appId;

    private String name;


    public ProjectDomainInfoDTO(String name, String appId, List<String> domain) {
        this.name = name;
        this.appId = appId;
        this.domain = domain;
    }

    public List<String> getDomain() {
        return domain;
    }

    public void setDomain(List<String> domain) {
        this.domain = domain;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
