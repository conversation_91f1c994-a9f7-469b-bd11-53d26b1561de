package cn.huolala.van.api.facade.model.gitlab;

import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import cn.huolala.van.api.facade.model.gitlab.GitlabMember.Role;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GitlabProtectedBranch {
    private long id;
    private String name;
    private List<AccessLevel> pushAccessLevels;
    private List<AccessLevel> mergeAccessLevels;
    private boolean allowForcePush;
    private boolean codeOwnerApprovalRequired;

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class AccessLevel {
        private long id;
        private Role accessLevel;
        private String accessLevelDescription;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public Role getAccessLevel() {
            return accessLevel;
        }

        public void setAccessLevel(Role accessLevel) {
            this.accessLevel = accessLevel;
        }

        public String getAccessLevelDescription() {
            return accessLevelDescription;
        }

        public void setAccessLevelDescription(String accessLevelDescription) {
            this.accessLevelDescription = accessLevelDescription;
        }
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<AccessLevel> getPushAccessLevels() {
        return pushAccessLevels;
    }

    public void setPushAccessLevels(List<AccessLevel> pushAccessLevels) {
        this.pushAccessLevels = pushAccessLevels;
    }

    public List<AccessLevel> getMergeAccessLevels() {
        return mergeAccessLevels;
    }

    public void setMergeAccessLevels(List<AccessLevel> mergeAccessLevels) {
        this.mergeAccessLevels = mergeAccessLevels;
    }

    public boolean isAllowForcePush() {
        return allowForcePush;
    }

    public void setAllowForcePush(boolean allowForcePush) {
        this.allowForcePush = allowForcePush;
    }

    public boolean isCodeOwnerApprovalRequired() {
        return codeOwnerApprovalRequired;
    }

    public void setCodeOwnerApprovalRequired(boolean codeOwnerApprovalRequired) {
        this.codeOwnerApprovalRequired = codeOwnerApprovalRequired;
    }
}
