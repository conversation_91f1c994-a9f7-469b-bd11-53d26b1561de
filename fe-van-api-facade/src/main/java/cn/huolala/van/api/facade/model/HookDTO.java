package cn.huolala.van.api.facade.model;

import java.util.List;

import org.springframework.lang.Nullable;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;

public class HookDTO {

    private String name;

    @Nullable
    private List<String> branch;

    @Nullable
    private Integer timeout;
    @Nullable
    private List<Timing> timing;
    @Nullable
    private String version;

    @Nullable
    private Boolean throwError;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum Timing {
        @JsonEnumDefaultValue
        unknown, before, after
    }

    public HookDTO() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getBranch() {
        return branch;
    }

    public void setBranch(List<String> branch) {
        this.branch = branch;
    }

    public Integer getTimeout() {
        return timeout;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    public List<Timing> getTiming() {
        return timing;
    }

    public void setTiming(List<Timing> timing) {
        this.timing = timing;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Boolean getThrowError() {
        return throwError;
    }

    public void setThrowError(Boolean throwError) {
        this.throwError = throwError;
    }

}
