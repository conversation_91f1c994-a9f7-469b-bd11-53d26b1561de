package cn.huolala.van.api.facade.model;

import org.springframework.lang.Nullable;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DeployDevParams {

    @JsonProperty("project_id")
    private Long projectId;

    @JsonProperty("task_id")
    private Long taskId;

    @JsonProperty("env")
    private String env;

    @JsonProperty("stable_env")
    private String stableEnv;

    @JsonProperty("uniq_id")
    private String uniqId;

    @Nullable
    private String from;

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getStableEnv() {
        return stableEnv;
    }

    public void setStableEnv(String stableEnv) {
        this.stableEnv = stableEnv;
    }

    public String getUniqId() {
        return uniqId;
    }

    public void setUniqId(String uniqId) {
        this.uniqId = uniqId;
    }

}
