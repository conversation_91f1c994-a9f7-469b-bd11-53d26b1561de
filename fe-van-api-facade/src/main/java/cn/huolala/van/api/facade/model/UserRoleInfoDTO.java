package cn.huolala.van.api.facade.model;

import cn.huolala.van.api.facade.model.enums.ProjectCmdbReginEnum;
import cn.huolala.van.api.facade.model.enums.VanUserRoleEnum;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public class UserRoleInfoDTO extends UserInfoDTO {
	@Nullable
	private ProjectCmdbReginEnum region;

	@NonNull
	private VanUserRoleEnum role;

	public void setRegion(@Nullable ProjectCmdbReginEnum region) {
		this.region = region;
	}

	public void setRole(@Nullable VanUserRoleEnum role) {
		this.role = role;
	}

	public ProjectCmdbReginEnum getRegion() {
		return region;
	}

	public VanUserRoleEnum getRole() {
		return role;
	}

}
