package cn.huolala.van.api.facade.model.gitlab;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.lang.NonNull;

public class GitlabBranch {
    @NonNull
    private String name;
    private Boolean merged;
    @JsonProperty("protected")
    private Boolean theProtected;
    @JsonProperty("default")
    private Boolean theDefault;
    @JsonProperty("developers_can_push")
    private Boolean developersCanPush;
    @JsonProperty("developers_can_merge")
    private Boolean developersCanMerge;
    @JsonProperty("can_push")
    private Boolean canPush;
    @JsonProperty("web_url")
    private String webUrl;
    private GitlabCommit commit;

    public GitlabBranch() {
        name = "";
    }

    @NonNull
    public String getName() {
        return name;
    }

    public void setName(@NonNull String name) {
        this.name = name;
    }

    public Boolean getMerged() {
        return merged;
    }

    public void setMerged(Boolean merged) {
        this.merged = merged;
    }

    public Boolean getProtected() {
        return theProtected;
    }

    public void setProtected(<PERSON><PERSON><PERSON> theProtected) {
        this.theProtected = theProtected;
    }

    public Boolean getDefault() {
        return theDefault;
    }

    public void setDefault(Boolean theDefault) {
        this.theDefault = theDefault;
    }

    public Boolean getDevelopersCanPush() {
        return developersCanPush;
    }

    public void setDevelopersCanPush(Boolean developersCanPush) {
        this.developersCanPush = developersCanPush;
    }

    public Boolean getDevelopersCanMerge() {
        return developersCanMerge;
    }

    public void setDevelopersCanMerge(Boolean developersCanMerge) {
        this.developersCanMerge = developersCanMerge;
    }

    public Boolean getCanPush() {
        return canPush;
    }

    public void setCanPush(Boolean canPush) {
        this.canPush = canPush;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    @NonNull
    public GitlabCommit getCommit() {
        if (commit == null) commit = new GitlabCommit();
        return commit;
    }

    public void setCommit(GitlabCommit commit) {
        this.commit = commit;
    }
}
