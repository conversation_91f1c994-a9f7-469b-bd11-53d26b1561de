package cn.huolala.van.api.facade.service.feishu.dto;

import cn.huolala.van.api.facade.model.UserInfoDTO;

import java.io.Serializable;
import java.time.LocalDateTime;

public class FeishuSubscriptionDTO implements Serializable {
    private static final long serialVersionUID = -6752013467962636322L;

    private Long id;
    private UserInfoDTO user;

    private FeishuChatDTO feishuChat;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public UserInfoDTO getUser() {
        return user;
    }

    public void setUser(UserInfoDTO user) {
        this.user = user;
    }

    public FeishuChatDTO getFeishuChat() {
        return feishuChat;
    }

    public void setFeishuChat(FeishuChatDTO feishuChat) {
        this.feishuChat = feishuChat;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
}
