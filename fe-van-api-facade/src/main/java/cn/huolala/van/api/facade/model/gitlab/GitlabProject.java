package cn.huolala.van.api.facade.model.gitlab;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GitlabProject {
    private long id;
    private String name;
    private String path;
    private String description;
    private String nameWithNamespace;
    private String pathWithNamespace;
    private String createdAt;
    private String defaultBranch;
    private String sshUrlToRepo;
    private String httpUrlToRepo;
    private String webUrl;
    private String readmeUrl;
    private String avatarUrl;
    private Integer forksCount;
    private Integer starCount;
    private String lastActivityAt;
    private Namespace namespace;
    private Boolean packagesEnabled;
    private Boolean emptyRepo;
    private Boolean archived;
    private String visibility;
    private Boolean resolveOutdatedDiffDiscussions;
    private Boolean containerRegistryEnabled;
    private ContainerExpirationPolicy containerExpirationPolicy;
    private Boolean issuesEnabled;
    private Boolean mergeRequestsEnabled;
    private Boolean jobsEnabled;
    private Boolean serviceDeskEnabled;
    private Object serviceDeskAddress;
    private Boolean canCreateMergeRequestIn;
    private String issuesAccessLevel;
    private String repositoryAccessLevel;
    private String mergeRequestsAccessLevel;
    private String forkingAccessLevel;
    private String wikiAccessLevel;
    private String buildsAccessLevel;
    private String snippetsAccessLevel;
    private String pagesAccessLevel;
    private String operationsAccessLevel;
    private String analyticsAccessLevel;
    private Boolean sharedRunnersEnabled;
    private Boolean lfsEnabled;
    private Long creatorId;
    private String importStatus;
    private Integer openIssuesCount;
    private String runnersToken;
    private Integer ciDefaultGitDepth;
    private Boolean ciForwardDeploymentEnabled;
    private Boolean publicJobs;
    private String buildGitStrategy;
    private Integer buildTimeout;
    private String autoCancelPendingPipelines;
    private String ciConfigPath;
    private List<Object> sharedWithGroups;
    private Boolean onlyAllowMergeIfPipelineSucceeds;
    private Boolean allowMergeOnSkippedPipeline;
    private Boolean restrictUserDefinedVariables;
    private Boolean requestAccessEnabled;
    private Boolean onlyAllowMergeIfAllDiscussionsAreResolved;
    private Boolean removeSourceBranchAfterMerge;
    private Boolean printingMergeRequestLinkEnabled;
    private String mergeMethod;
    private String suggestionCommitMessage;
    private Boolean autoDevopsEnabled;
    private String autoDevopsDeployStrategy;
    private Boolean autocloseReferencedIssues;
    private String repositoryStorage;
    private List<String> tagList;
    private List<String> topics;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNameWithNamespace() {
        return nameWithNamespace;
    }

    public void setNameWithNamespace(String nameWithNamespace) {
        this.nameWithNamespace = nameWithNamespace;
    }

    public String getPathWithNamespace() {
        return pathWithNamespace;
    }

    public void setPathWithNamespace(String pathWithNamespace) {
        this.pathWithNamespace = pathWithNamespace;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getDefaultBranch() {
        return defaultBranch;
    }

    public void setDefaultBranch(String defaultBranch) {
        this.defaultBranch = defaultBranch;
    }

    public String getSshUrlToRepo() {
        return sshUrlToRepo;
    }

    public void setSshUrlToRepo(String sshUrlToRepo) {
        this.sshUrlToRepo = sshUrlToRepo;
    }

    public String getHttpUrlToRepo() {
        return httpUrlToRepo;
    }

    public void setHttpUrlToRepo(String httpUrlToRepo) {
        this.httpUrlToRepo = httpUrlToRepo;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public String getReadmeUrl() {
        return readmeUrl;
    }

    public void setReadmeUrl(String readmeUrl) {
        this.readmeUrl = readmeUrl;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Integer getForksCount() {
        return forksCount;
    }

    public void setForksCount(Integer forksCount) {
        this.forksCount = forksCount;
    }

    public Integer getStarCount() {
        return starCount;
    }

    public void setStarCount(Integer starCount) {
        this.starCount = starCount;
    }

    public String getLastActivityAt() {
        return lastActivityAt;
    }

    public void setLastActivityAt(String lastActivityAt) {
        this.lastActivityAt = lastActivityAt;
    }

    public Namespace getNamespace() {
        return namespace;
    }

    public void setNamespace(Namespace namespace) {
        this.namespace = namespace;
    }

    public Boolean getPackagesEnabled() {
        return packagesEnabled;
    }

    public void setPackagesEnabled(Boolean packagesEnabled) {
        this.packagesEnabled = packagesEnabled;
    }

    public Boolean getEmptyRepo() {
        return emptyRepo;
    }

    public void setEmptyRepo(Boolean emptyRepo) {
        this.emptyRepo = emptyRepo;
    }

    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    public String getVisibility() {
        return visibility;
    }

    public void setVisibility(String visibility) {
        this.visibility = visibility;
    }

    public Boolean getResolveOutdatedDiffDiscussions() {
        return resolveOutdatedDiffDiscussions;
    }

    public void setResolveOutdatedDiffDiscussions(Boolean resolveOutdatedDiffDiscussions) {
        this.resolveOutdatedDiffDiscussions = resolveOutdatedDiffDiscussions;
    }

    public Boolean getContainerRegistryEnabled() {
        return containerRegistryEnabled;
    }

    public void setContainerRegistryEnabled(Boolean containerRegistryEnabled) {
        this.containerRegistryEnabled = containerRegistryEnabled;
    }

    public ContainerExpirationPolicy getContainerExpirationPolicy() {
        return containerExpirationPolicy;
    }

    public void setContainerExpirationPolicy(ContainerExpirationPolicy containerExpirationPolicy) {
        this.containerExpirationPolicy = containerExpirationPolicy;
    }

    public Boolean getIssuesEnabled() {
        return issuesEnabled;
    }

    public void setIssuesEnabled(Boolean issuesEnabled) {
        this.issuesEnabled = issuesEnabled;
    }

    public Boolean getMergeRequestsEnabled() {
        return mergeRequestsEnabled;
    }

    public void setMergeRequestsEnabled(Boolean mergeRequestsEnabled) {
        this.mergeRequestsEnabled = mergeRequestsEnabled;
    }

    public Boolean getJobsEnabled() {
        return jobsEnabled;
    }

    public void setJobsEnabled(Boolean jobsEnabled) {
        this.jobsEnabled = jobsEnabled;
    }

    public Boolean getServiceDeskEnabled() {
        return serviceDeskEnabled;
    }

    public void setServiceDeskEnabled(Boolean serviceDeskEnabled) {
        this.serviceDeskEnabled = serviceDeskEnabled;
    }

    public Object getServiceDeskAddress() {
        return serviceDeskAddress;
    }

    public void setServiceDeskAddress(Object serviceDeskAddress) {
        this.serviceDeskAddress = serviceDeskAddress;
    }

    public Boolean getCanCreateMergeRequestIn() {
        return canCreateMergeRequestIn;
    }

    public void setCanCreateMergeRequestIn(Boolean canCreateMergeRequestIn) {
        this.canCreateMergeRequestIn = canCreateMergeRequestIn;
    }

    public String getIssuesAccessLevel() {
        return issuesAccessLevel;
    }

    public void setIssuesAccessLevel(String issuesAccessLevel) {
        this.issuesAccessLevel = issuesAccessLevel;
    }

    public String getRepositoryAccessLevel() {
        return repositoryAccessLevel;
    }

    public void setRepositoryAccessLevel(String repositoryAccessLevel) {
        this.repositoryAccessLevel = repositoryAccessLevel;
    }

    public String getMergeRequestsAccessLevel() {
        return mergeRequestsAccessLevel;
    }

    public void setMergeRequestsAccessLevel(String mergeRequestsAccessLevel) {
        this.mergeRequestsAccessLevel = mergeRequestsAccessLevel;
    }

    public String getForkingAccessLevel() {
        return forkingAccessLevel;
    }

    public void setForkingAccessLevel(String forkingAccessLevel) {
        this.forkingAccessLevel = forkingAccessLevel;
    }

    public String getWikiAccessLevel() {
        return wikiAccessLevel;
    }

    public void setWikiAccessLevel(String wikiAccessLevel) {
        this.wikiAccessLevel = wikiAccessLevel;
    }

    public String getBuildsAccessLevel() {
        return buildsAccessLevel;
    }

    public void setBuildsAccessLevel(String buildsAccessLevel) {
        this.buildsAccessLevel = buildsAccessLevel;
    }

    public String getSnippetsAccessLevel() {
        return snippetsAccessLevel;
    }

    public void setSnippetsAccessLevel(String snippetsAccessLevel) {
        this.snippetsAccessLevel = snippetsAccessLevel;
    }

    public String getPagesAccessLevel() {
        return pagesAccessLevel;
    }

    public void setPagesAccessLevel(String pagesAccessLevel) {
        this.pagesAccessLevel = pagesAccessLevel;
    }

    public String getOperationsAccessLevel() {
        return operationsAccessLevel;
    }

    public void setOperationsAccessLevel(String operationsAccessLevel) {
        this.operationsAccessLevel = operationsAccessLevel;
    }

    public String getAnalyticsAccessLevel() {
        return analyticsAccessLevel;
    }

    public void setAnalyticsAccessLevel(String analyticsAccessLevel) {
        this.analyticsAccessLevel = analyticsAccessLevel;
    }

    public Boolean getSharedRunnersEnabled() {
        return sharedRunnersEnabled;
    }

    public void setSharedRunnersEnabled(Boolean sharedRunnersEnabled) {
        this.sharedRunnersEnabled = sharedRunnersEnabled;
    }

    public Boolean getLfsEnabled() {
        return lfsEnabled;
    }

    public void setLfsEnabled(Boolean lfsEnabled) {
        this.lfsEnabled = lfsEnabled;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getImportStatus() {
        return importStatus;
    }

    public void setImportStatus(String importStatus) {
        this.importStatus = importStatus;
    }

    public Integer getOpenIssuesCount() {
        return openIssuesCount;
    }

    public void setOpenIssuesCount(Integer openIssuesCount) {
        this.openIssuesCount = openIssuesCount;
    }

    public String getRunnersToken() {
        return runnersToken;
    }

    public void setRunnersToken(String runnersToken) {
        this.runnersToken = runnersToken;
    }

    public Integer getCiDefaultGitDepth() {
        return ciDefaultGitDepth;
    }

    public void setCiDefaultGitDepth(Integer ciDefaultGitDepth) {
        this.ciDefaultGitDepth = ciDefaultGitDepth;
    }

    public Boolean getCiForwardDeploymentEnabled() {
        return ciForwardDeploymentEnabled;
    }

    public void setCiForwardDeploymentEnabled(Boolean ciForwardDeploymentEnabled) {
        this.ciForwardDeploymentEnabled = ciForwardDeploymentEnabled;
    }

    public Boolean getPublicJobs() {
        return publicJobs;
    }

    public void setPublicJobs(Boolean publicJobs) {
        this.publicJobs = publicJobs;
    }

    public String getBuildGitStrategy() {
        return buildGitStrategy;
    }

    public void setBuildGitStrategy(String buildGitStrategy) {
        this.buildGitStrategy = buildGitStrategy;
    }

    public Integer getBuildTimeout() {
        return buildTimeout;
    }

    public void setBuildTimeout(Integer buildTimeout) {
        this.buildTimeout = buildTimeout;
    }

    public String getAutoCancelPendingPipelines() {
        return autoCancelPendingPipelines;
    }

    public void setAutoCancelPendingPipelines(String autoCancelPendingPipelines) {
        this.autoCancelPendingPipelines = autoCancelPendingPipelines;
    }

    public String getCiConfigPath() {
        return ciConfigPath;
    }

    public void setCiConfigPath(String ciConfigPath) {
        this.ciConfigPath = ciConfigPath;
    }

    public List<Object> getSharedWithGroups() {
        return sharedWithGroups;
    }

    public void setSharedWithGroups(List<Object> sharedWithGroups) {
        this.sharedWithGroups = sharedWithGroups;
    }

    public Boolean getOnlyAllowMergeIfPipelineSucceeds() {
        return onlyAllowMergeIfPipelineSucceeds;
    }

    public void setOnlyAllowMergeIfPipelineSucceeds(Boolean onlyAllowMergeIfPipelineSucceeds) {
        this.onlyAllowMergeIfPipelineSucceeds = onlyAllowMergeIfPipelineSucceeds;
    }

    public Boolean getAllowMergeOnSkippedPipeline() {
        return allowMergeOnSkippedPipeline;
    }

    public void setAllowMergeOnSkippedPipeline(Boolean allowMergeOnSkippedPipeline) {
        this.allowMergeOnSkippedPipeline = allowMergeOnSkippedPipeline;
    }

    public Boolean getRestrictUserDefinedVariables() {
        return restrictUserDefinedVariables;
    }

    public void setRestrictUserDefinedVariables(Boolean restrictUserDefinedVariables) {
        this.restrictUserDefinedVariables = restrictUserDefinedVariables;
    }

    public Boolean getRequestAccessEnabled() {
        return requestAccessEnabled;
    }

    public void setRequestAccessEnabled(Boolean requestAccessEnabled) {
        this.requestAccessEnabled = requestAccessEnabled;
    }

    public Boolean getOnlyAllowMergeIfAllDiscussionsAreResolved() {
        return onlyAllowMergeIfAllDiscussionsAreResolved;
    }

    public void setOnlyAllowMergeIfAllDiscussionsAreResolved(Boolean onlyAllowMergeIfAllDiscussionsAreResolved) {
        this.onlyAllowMergeIfAllDiscussionsAreResolved = onlyAllowMergeIfAllDiscussionsAreResolved;
    }

    public Boolean getRemoveSourceBranchAfterMerge() {
        return removeSourceBranchAfterMerge;
    }

    public void setRemoveSourceBranchAfterMerge(Boolean removeSourceBranchAfterMerge) {
        this.removeSourceBranchAfterMerge = removeSourceBranchAfterMerge;
    }

    public Boolean getPrintingMergeRequestLinkEnabled() {
        return printingMergeRequestLinkEnabled;
    }

    public void setPrintingMergeRequestLinkEnabled(Boolean printingMergeRequestLinkEnabled) {
        this.printingMergeRequestLinkEnabled = printingMergeRequestLinkEnabled;
    }

    public String getMergeMethod() {
        return mergeMethod;
    }

    public void setMergeMethod(String mergeMethod) {
        this.mergeMethod = mergeMethod;
    }

    public String getSuggestionCommitMessage() {
        return suggestionCommitMessage;
    }

    public void setSuggestionCommitMessage(String suggestionCommitMessage) {
        this.suggestionCommitMessage = suggestionCommitMessage;
    }

    public Boolean getAutoDevopsEnabled() {
        return autoDevopsEnabled;
    }

    public void setAutoDevopsEnabled(Boolean autoDevopsEnabled) {
        this.autoDevopsEnabled = autoDevopsEnabled;
    }

    public String getAutoDevopsDeployStrategy() {
        return autoDevopsDeployStrategy;
    }

    public void setAutoDevopsDeployStrategy(String autoDevopsDeployStrategy) {
        this.autoDevopsDeployStrategy = autoDevopsDeployStrategy;
    }

    public Boolean getAutocloseReferencedIssues() {
        return autocloseReferencedIssues;
    }

    public void setAutocloseReferencedIssues(Boolean autocloseReferencedIssues) {
        this.autocloseReferencedIssues = autocloseReferencedIssues;
    }

    public String getRepositoryStorage() {
        return repositoryStorage;
    }

    public void setRepositoryStorage(String repositoryStorage) {
        this.repositoryStorage = repositoryStorage;
    }

    public List<String> getTagList() {
        return tagList;
    }

    public void setTagList(List<String> tagList) {
        this.tagList = tagList;
    }

    public List<String> getTopics() {
        return topics;
    }

    public void setTopics(List<String> topics) {
        this.topics = topics;
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Namespace {
        private long id;
        private long parentId;
        private String name;
        private String path;
        private String kind;
        private String fullPath;
        private String avatarUrl;
        private String webUrl;

        public long getId() {
            return id;
        }

        public void setId(long id) {
            this.id = id;
        }

        public long getParentId() {
            return parentId;
        }

        public void setParentId(long parentId) {
            this.parentId = parentId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getKind() {
            return kind;
        }

        public void setKind(String kind) {
            this.kind = kind;
        }

        public String getFullPath() {
            return fullPath;
        }

        public void setFullPath(String fullPath) {
            this.fullPath = fullPath;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getWebUrl() {
            return webUrl;
        }

        public void setWebUrl(String webUrl) {
            this.webUrl = webUrl;
        }
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ContainerExpirationPolicy {
        private String cadence;
        private Boolean enabled;
        private Integer keepN;
        private String olderThan;
        private String nameRegex;
        private String nameRegexKeep;
        private String nextRunAt;

        public String getCadence() {
            return cadence;
        }

        public void setCadence(String cadence) {
            this.cadence = cadence;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public Integer getKeepN() {
            return keepN;
        }

        public void setKeepN(Integer keepN) {
            this.keepN = keepN;
        }

        public String getOlderThan() {
            return olderThan;
        }

        public void setOlderThan(String olderThan) {
            this.olderThan = olderThan;
        }

        public String getNameRegex() {
            return nameRegex;
        }

        public void setNameRegex(String nameRegex) {
            this.nameRegex = nameRegex;
        }

        public String getNameRegexKeep() {
            return nameRegexKeep;
        }

        public void setNameRegexKeep(String nameRegexKeep) {
            this.nameRegexKeep = nameRegexKeep;
        }

        public String getNextRunAt() {
            return nextRunAt;
        }

        public void setNextRunAt(String nextRunAt) {
            this.nextRunAt = nextRunAt;
        }
    }
}
