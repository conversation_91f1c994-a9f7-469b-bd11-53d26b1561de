package cn.huolala.van.api.facade.model.gitlab;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.Arrays;
import java.util.Objects;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GitlabCommitAction {

    private Action action;
    private String filePath;
    private String previousPath;
    private String content;
    private String encoding;
    private String lastCommitId;
    private boolean executeFilemode;

    public GitlabCommitAction() {}

    public GitlabCommitAction(Action action, String filePath) {
        this.action = action;
        this.filePath = filePath;
    }

    public enum Action {

        Create("create"),
        Delete("delete"),
        Move("move"),
        Update("update"),
        Chmod("chmod");

        private final String value;

        Action(String value) {
            this.value = value;
        }

        @JsonCreator
        public static Action create(String value) {
            return Arrays.stream(values()).filter(i -> Objects.equals(i.value, value)).findFirst().orElse(null);
        }

        @JsonValue
        public String toValue() {
            return value;
        }
    }

    public Action getAction() {
        return action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getPreviousPath() {
        return previousPath;
    }

    public void setPreviousPath(String previousPath) {
        this.previousPath = previousPath;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public String getLastCommitId() {
        return lastCommitId;
    }

    public void setLastCommitId(String lastCommitId) {
        this.lastCommitId = lastCommitId;
    }

    public boolean isExecuteFilemode() {
        return executeFilemode;
    }

    public void setExecuteFilemode(boolean executeFilemode) {
        this.executeFilemode = executeFilemode;
    }
}
