package cn.huolala.van.api.facade.model.gitlab;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GitlabCompare {
    private GitlabCommit commit;
    private List<GitlabCommit> commits;
    private List<Diff> diffs;
    private Boolean compareTimeout;
    private Boolean compareSameRef;
    private String webUrl;

    public GitlabCommit getCommit() {
        return commit;
    }

    public void setCommit(GitlabCommit commit) {
        this.commit = commit;
    }

    public List<GitlabCommit> getCommits() {
        return commits;
    }

    public void setCommits(List<GitlabCommit> commits) {
        this.commits = commits;
    }

    public List<Diff> getDiffs() {
        return diffs;
    }

    public void setDiffs(List<Diff> diffs) {
        this.diffs = diffs;
    }

    public Boolean getCompareTimeout() {
        return compareTimeout;
    }

    public void setCompareTimeout(Boolean compareTimeout) {
        this.compareTimeout = compareTimeout;
    }

    public Boolean getCompareSameRef() {
        return compareSameRef;
    }

    public void setCompareSameRef(Boolean compareSameRef) {
        this.compareSameRef = compareSameRef;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Diff {
        private String oldPath;
        private String newPath;
        private String aMode;
        private String bMode;
        private String diff;
        private Boolean newFile;
        private Boolean renamedFile;
        private Boolean deletedFile;

        public String getOldPath() {
            return oldPath;
        }

        public void setOldPath(String oldPath) {
            this.oldPath = oldPath;
        }

        public String getNewPath() {
            return newPath;
        }

        public void setNewPath(String newPath) {
            this.newPath = newPath;
        }

        public String getAMode() {
            return aMode;
        }

        public void setAMode(String aMode) {
            this.aMode = aMode;
        }

        public String getBMode() {
            return bMode;
        }

        public void setBMode(String bMode) {
            this.bMode = bMode;
        }

        public String getDiff() {
            return diff;
        }

        public void setDiff(String diff) {
            this.diff = diff;
        }

        public Boolean getNewFile() {
            return newFile;
        }

        public void setNewFile(Boolean newFile) {
            this.newFile = newFile;
        }

        public Boolean getRenamedFile() {
            return renamedFile;
        }

        public void setRenamedFile(Boolean renamedFile) {
            this.renamedFile = renamedFile;
        }

        public Boolean getDeletedFile() {
            return deletedFile;
        }

        public void setDeletedFile(Boolean deletedFile) {
            this.deletedFile = deletedFile;
        }
    }
}
