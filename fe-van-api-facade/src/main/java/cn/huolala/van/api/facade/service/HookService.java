package cn.huolala.van.api.facade.service;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.facade.model.HookDTO;
import cn.huolala.van.api.facade.model.MiniprogramDeployHistoryDTO;
import cn.huolala.van.api.facade.model.MiniprogramInfoDTO;


@HermesService("/van/hook")
public interface HookService {

    MiniprogramInfoDTO getMiniprogramInfo(Long projectId);

    MiniprogramDeployHistoryDTO getMiniprogramInfoDeployHistory(Long projectId, Long deployTaskId);

    void miniprogramDeployResultNotifyLark(Long projectId, Long deployTaskId, String imageBaseStr, String devPluginId,
            String hookStatus);

    Long createMiniprogramDeployHistory(Long projectId, Long taskId, Long deployTaskId, Long creatorId, int type,
            int robot, String version, String description);

    Long deployMiniprogram(Long projectId, Long taskId, Long creatorId, int deployType, int robot, String version,
            String description);

    void reportTaskMetaInfo(String projectName, Long taskId, String filename, String content);

    HookDTO[] getGlobalHooks();

    HookDTO[] getProjectHooks(Long projectId);

    void reportHookException(String projectName, Long taskId, String hookName, String message);
}
