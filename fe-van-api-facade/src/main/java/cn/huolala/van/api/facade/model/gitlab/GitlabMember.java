package cn.huolala.van.api.facade.model.gitlab;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GitlabMember {
    private Long id;
    private String name;
    private String username;
    private String state;
    private String avatarUrl;
    private String webUrl;
    private Role accessLevel;
    private String createdAt;
    private String expiresAt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public Role getAccessLevel() {
        return accessLevel;
    }

    public void setAccessLevel(Role accessLevel) {
        this.accessLevel = accessLevel;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(String expiresAt) {
        this.expiresAt = expiresAt;
    }

    @JsonIgnore
    public boolean hasSufficientPermission(@NonNull Role role) {
        if (accessLevel == null) return false;
        return accessLevel.toValue() >= role.toValue();
    }

    public enum Role {
        No(0),

        Minimal(5),

        Guest(10),

        Reporter(20),

        Developer(30),

        Maintainer(40),

        Owner(50);

        private final int value;

        Role(int value) {
            this.value = value;
        }

        @JsonCreator
        @Nullable
        public static Role create(int value) {
            return Arrays.stream(values()).filter(i -> i.value == value).findFirst().orElse(null);
        }

        public static Role compare(Role a, Role b) {
            return a.toValue() < b.toValue() ? b : a;
        }

        @JsonValue
        public int toValue() {
            return value;
        }
    }
}
