package cn.huolala.van.api.facade.model.enums;

import java.util.Arrays;

public enum VanUserRoleEnum {
    None("NoRole"),

    Test("TestRole"),

    Dev("DevRole"),

    Admin("AdminRole");

    public final String golangEnumStr;

    VanUserRoleEnum(String golangEnumStr) {
        this.golangEnumStr = golangEnumStr;
    }

    public static VanUserRoleEnum fromGolangEnumStr(String str) {
        return Arrays.stream(VanUserRoleEnum.values())
                .filter(i -> i.golangEnumStr.equals(str)).findFirst().orElse(null);
    }

    public static VanUserRoleEnum fromBitValue(int bitValue) {
        switch (bitValue) {
            case 0:
                return VanUserRoleEnum.None;

            case 1:
                return VanUserRoleEnum.Test;

            case 2:
                return VanUserRoleEnum.Dev;

            case 4:
                return VanUserRoleEnum.Admin;
        }

        return VanUserRoleEnum.None;
    }

    @Override
    public String toString() {
        return golangEnumStr;
    }
}
