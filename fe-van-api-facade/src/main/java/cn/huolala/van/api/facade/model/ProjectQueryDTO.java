package cn.huolala.van.api.facade.model;

import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

public class ProjectQueryDTO implements Serializable {

    private ProjectIntroDTO project;

    private List<UserRoleInfoDTO> projectUsers;

    private ProdDeployDTO prodDeploy;

    private DevDeployDTO devDeploy;

    public ProjectQueryDTO(ProjectIntroDTO project) {
        this.project = project;
    }

    public static class DevDeployDTO {
        private DevDeploy stg;
        private DevDeploy pre;

        public DevDeploy getStg() {
            return stg;
        }

        public void setStg(DevDeploy stg) {
            this.stg = stg;
        }

        public DevDeploy getPre() {
            return pre;
        }

        public void setPre(DevDeploy pre) {
            this.pre = pre;
        }
    }

    public static class DevDeploy {
        private UserInfoDTO user;
        private BuildTaskDTO task;
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        private OffsetDateTime createdAt;

        public UserInfoDTO getUser() {
            return user;
        }

        public void setUser(UserInfoDTO user) {
            this.user = user;
        }

        public BuildTaskDTO getTask() {
            return task;
        }

        public void setTask(BuildTaskDTO task) {
            this.task = task;
        }

        public OffsetDateTime getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(OffsetDateTime createdAt) {
            this.createdAt = createdAt;
        }
    }

    public static class ProdDeployDTO {

        private List<String> domains;

        private List<BuildTaskDTO> ossTask;

        private List<BuildTaskDTO> dbTask;

        private UserInfoDTO dbUser;

        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        private OffsetDateTime createdAt;

        public ProdDeployDTO() {}

        public List<String> getDomains() {
            return domains;
        }

        public void setDomains(List<String> domains) {
            this.domains = domains;
        }

        public List<BuildTaskDTO> getOssTask() {
            return ossTask;
        }

        public void setOssTask(List<BuildTaskDTO> ossTask) {
            this.ossTask = ossTask;
        }

        public List<BuildTaskDTO> getDbTask() {
            return dbTask;
        }

        public void setDbTask(List<BuildTaskDTO> dbTask) {
            this.dbTask = dbTask;
        }

        public UserInfoDTO getDbUser() {
            return dbUser;
        }

        public void setDbUser(UserInfoDTO dbUser) {
            this.dbUser = dbUser;
        }

        public OffsetDateTime getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(OffsetDateTime createdAt) {
            this.createdAt = createdAt;
        }
    }

    public ProjectIntroDTO getProject() {
        return project;
    }

    public void setProject(ProjectIntroDTO project) {
        this.project = project;
    }

    public ProdDeployDTO getProdDeploy() {
        return prodDeploy;
    }

    public void setProdDeploy(ProdDeployDTO prodDeploy) {
        this.prodDeploy = prodDeploy;
    }

    public DevDeployDTO getDevDeploy() {
        return devDeploy;
    }

    public void setDevDeploy(DevDeployDTO devDeploy) {
        this.devDeploy = devDeploy;
    }

    public List<UserRoleInfoDTO> getProjectUsers() {
        return projectUsers;
    }

    public void setProjectUsers(List<UserRoleInfoDTO> projectUsers) {
        this.projectUsers = projectUsers;
    }
}
