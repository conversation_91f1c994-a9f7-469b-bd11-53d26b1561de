package cn.huolala.van.api.facade.model;


import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;


public class LegacyDeployConfigDTO {

    @JsonProperty("default_task_id")
    private long defaultTaskId;

    private List<Rule> canary;

    public LegacyDeployConfigDTO(long defaultTaskId) {
        this.defaultTaskId = defaultTaskId;
    }

    public static class Rule {
        @JsonProperty("task_id")
        private Long taskId;
        @JsonProperty("canary_id")
        private String canaryId;
        @JsonProperty("operator_chain")
        private List<Operator> operatorChain;
        private String description;

        public Rule() {}

        public Long getTaskId() {
            return taskId;
        }
        public void setTaskId(Long taskId) {
            this.taskId = taskId;
        }
        public String getCanaryId() {
            return canaryId;
        }
        public void setCanaryId(String canaryId) {
            this.canaryId = canaryId;
        }
        public List<Operator> getOperatorChain() {
            return operatorChain;
        }
        public void setOperatorChain(List<Operator> operatorChain) {
            this.operatorChain = operatorChain;
        }
        public String getDescription() {
            return description;
        }
        public void setDescription(String description) {
            this.description = description;
        }
    }

    public static class Operator {
        private String left;
        private String op;
        private Object right;
        private String type;

        public Operator() {}

        public String getLeft() {
            return left;
        }
        public void setLeft(String left) {
            this.left = left;
        }
        public String getOp() {
            return op;
        }
        public void setOp(String op) {
            this.op = op;
        }
        public Object getRight() {
            return right;
        }
        public void setRight(Object right) {
            this.right = right;
        }
        public String getType() {
            return type;
        }
        public void setType(String type) {
            this.type = type;
        }
    }

    public long getDefaultTaskId() {
        return defaultTaskId;
    }

    public void setDefaultTaskId(long defaultTaskId) {
        this.defaultTaskId = defaultTaskId;
    }

    public List<Rule> getCanary() {
        return canary;
    }

    public void setCanary(List<Rule> canary) {
        this.canary = canary;
    }
}
