
package cn.huolala.van.api.facade.model;

public class MiniprogramDeployHistoryDTO implements java.io.Serializable {
    private Long id;
    private Long projectId;
    private Long taskId;
    private Long deployTaskId;
    private Long creatorId;
    private Integer type;
    private Integer robot;
    private String version;
    private String description;


    public MiniprogramDeployHistoryDTO(Long id, Long projectId, Long taskId, Long deployTaskId, Long creatorId, Integer type, Integer robot, String version, String description) {
        this.id = id;
        this.projectId = projectId;
        this.taskId = taskId;
        this.deployTaskId = deployTaskId;
        this.creatorId = creatorId;
        this.type = type;
        this.robot = robot;
        this.version = version;
        this.description = description;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getDeployTaskId() {
        return deployTaskId;
    }

    public void setDeployTaskId(Long deployTaskId) {
        this.deployTaskId = deployTaskId;
    }

    public Long getCreatorId() {
      return creatorId;
    }

    public void setCreatorId(Long creatorId) {
      this.creatorId = creatorId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getRobot() {
        return robot;
    }

    public void setRobot(Integer robot) {
        this.robot = robot;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
