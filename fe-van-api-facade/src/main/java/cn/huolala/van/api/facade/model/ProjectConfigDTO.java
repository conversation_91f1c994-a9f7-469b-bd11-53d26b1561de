package cn.huolala.van.api.facade.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;

public class ProjectConfigDTO {
	@JsonProperty(value = "allowed_host", defaultValue = "[]")
	private List<String> allowedHosts;

	@JsonProperty(value = "dev_public", defaultValue = "false")
	private boolean devPublic;

	@JsonPropertyDescription("Indicates whether this project is an international project")
	@JsonProperty(defaultValue = "false")
	private boolean international;

	@JsonProperty(defaultValue = "false")
	private boolean office;

	@JsonPropertyDescription("LOne AppID")
	private String appid;

	@JsonProperty(value = "off_web", defaultValue = "false")
	@JsonPropertyDescription("Indicates whether this project supports the offline package solution")
	private boolean offWeb;

	@JsonProperty(defaultValue = "false")
	@JsonPropertyDescription("Indicates whether this project is Workers")
	private boolean workers;

	public List<String> getAllowedHosts() {
		return allowedHosts;
	}

	public void setAllowedHosts(List<String> allowedHosts) {
		this.allowedHosts = allowedHosts;
	}

	public boolean isDevPublic() {
		return devPublic;
	}

	public void setDevPublic(boolean devPublic) {
		this.devPublic = devPublic;
	}

	public boolean isInternational() {
		return international;
	}

	public void setInternational(boolean international) {
		this.international = international;
	}

	public boolean isOffice() {
		return office;
	}

	public void setOffice(boolean office) {
		this.office = office;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public boolean isOffWeb() {
		return offWeb;
	}

	public void setOffWeb(boolean offWeb) {
		this.offWeb = offWeb;
	}

	public boolean isWorkers() {
		return workers;
	}

	public void setWorkers(boolean workers) {
		this.workers = workers;
	}

}
