package cn.huolala.van.api.facade.model.gitlab;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import org.springframework.lang.NonNull;

import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GitlabMergeRequest {
    @NonNull
    private Long id;
    private Long iid;
    private Long projectId;
    private String title;
    private String description;
    private State state = State.unknown;
    private String createdAt;
    private String updatedAt;
    private GitlabUserRef mergedBy;
    private String mergedAt;
    private GitlabUserRef closedBy;
    private String closedAt;
    @NonNull
    private String targetBranch = "";
    @NonNull
    private String sourceBranch = "";
    private Integer userNotesCount;
    private Integer upvotes;
    private Integer downvotes;
    private GitlabUserRef author;
    private List<Object> assignees;
    private Object assignee;
    private List<Object> reviewers;
    private Long sourceProjectId;
    private Long targetProjectId;
    private List<Object> labels;
    private Boolean workInProgress;
    private Object milestone;
    private Boolean mergeWhenPipelineSucceeds;
    private String mergeStatus;
    @NonNull
    private String sha = "";
    private String mergeCommitSha;
    private String squashCommitSha;
    private Object discussionLocked;
    private Object shouldRemoveSourceBranch;
    private Boolean forceRemoveSourceBranch;
    private String reference;
    private References references;
    private String webUrl;
    private TimeStats timeStats;
    private Boolean squash;
    private TaskCompletionStatus taskCompletionStatus;
    private Boolean hasConflicts;
    private Boolean blockingDiscussionsResolved;

    public GitlabMergeRequest() {
        this.id = 0L;
    }

    @NonNull
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id == null ? 0L : id;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public GitlabUserRef getMergedBy() {
        return mergedBy;
    }

    public void setMergedBy(GitlabUserRef mergedBy) {
        this.mergedBy = mergedBy;
    }

    public String getMergedAt() {
        return mergedAt;
    }

    public void setMergedAt(String mergedAt) {
        this.mergedAt = mergedAt;
    }

    public GitlabUserRef getClosedBy() {
        return closedBy;
    }

    public void setClosedBy(GitlabUserRef closedBy) {
        this.closedBy = closedBy;
    }

    public String getClosedAt() {
        return closedAt;
    }

    public void setClosedAt(String closedAt) {
        this.closedAt = closedAt;
    }

    @NonNull
    public String getTargetBranch() {
        return targetBranch;
    }

    public void setTargetBranch(@NonNull String targetBranch) {
        this.targetBranch = targetBranch;
    }

    @NonNull
    public String getSourceBranch() {
        return sourceBranch;
    }

    public void setSourceBranch(@NonNull String sourceBranch) {
        this.sourceBranch = sourceBranch;
    }

    public Integer getUserNotesCount() {
        return userNotesCount;
    }

    public void setUserNotesCount(Integer userNotesCount) {
        this.userNotesCount = userNotesCount;
    }

    public Integer getUpvotes() {
        return upvotes;
    }

    public void setUpvotes(Integer upvotes) {
        this.upvotes = upvotes;
    }

    public Integer getDownvotes() {
        return downvotes;
    }

    public void setDownvotes(Integer downvotes) {
        this.downvotes = downvotes;
    }

    public GitlabUserRef getAuthor() {
        return author;
    }

    public void setAuthor(GitlabUserRef author) {
        this.author = author;
    }

    public List<Object> getAssignees() {
        return assignees;
    }

    public void setAssignees(List<Object> assignees) {
        this.assignees = assignees;
    }

    public Object getAssignee() {
        return assignee;
    }

    public void setAssignee(Object assignee) {
        this.assignee = assignee;
    }

    public List<Object> getReviewers() {
        return reviewers;
    }

    public void setReviewers(List<Object> reviewers) {
        this.reviewers = reviewers;
    }

    public Long getSourceProjectId() {
        return sourceProjectId;
    }

    public void setSourceProjectId(Long sourceProjectId) {
        this.sourceProjectId = sourceProjectId;
    }

    public Long getTargetProjectId() {
        return targetProjectId;
    }

    public void setTargetProjectId(Long targetProjectId) {
        this.targetProjectId = targetProjectId;
    }

    public List<Object> getLabels() {
        return labels;
    }

    public void setLabels(List<Object> labels) {
        this.labels = labels;
    }

    public Boolean getWorkInProgress() {
        return workInProgress;
    }

    public void setWorkInProgress(Boolean workInProgress) {
        this.workInProgress = workInProgress;
    }

    public Object getMilestone() {
        return milestone;
    }

    public void setMilestone(Object milestone) {
        this.milestone = milestone;
    }

    public Boolean getMergeWhenPipelineSucceeds() {
        return mergeWhenPipelineSucceeds;
    }

    public void setMergeWhenPipelineSucceeds(Boolean mergeWhenPipelineSucceeds) {
        this.mergeWhenPipelineSucceeds = mergeWhenPipelineSucceeds;
    }

    public String getMergeStatus() {
        return mergeStatus;
    }

    public void setMergeStatus(String mergeStatus) {
        this.mergeStatus = mergeStatus;
    }

    @NonNull
    public String getSha() {
        return sha;
    }

    public void setSha(@NonNull String sha) {
        this.sha = sha;
    }

    public String getMergeCommitSha() {
        return mergeCommitSha;
    }

    public void setMergeCommitSha(String mergeCommitSha) {
        this.mergeCommitSha = mergeCommitSha;
    }

    public String getSquashCommitSha() {
        return squashCommitSha;
    }

    public void setSquashCommitSha(String squashCommitSha) {
        this.squashCommitSha = squashCommitSha;
    }

    public Object getDiscussionLocked() {
        return discussionLocked;
    }

    public void setDiscussionLocked(Object discussionLocked) {
        this.discussionLocked = discussionLocked;
    }

    public Object getShouldRemoveSourceBranch() {
        return shouldRemoveSourceBranch;
    }

    public void setShouldRemoveSourceBranch(Object shouldRemoveSourceBranch) {
        this.shouldRemoveSourceBranch = shouldRemoveSourceBranch;
    }

    public Boolean getForceRemoveSourceBranch() {
        return forceRemoveSourceBranch;
    }

    public void setForceRemoveSourceBranch(Boolean forceRemoveSourceBranch) {
        this.forceRemoveSourceBranch = forceRemoveSourceBranch;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public References getReferences() {
        return references;
    }

    public void setReferences(References references) {
        this.references = references;
    }

    public String getWebUrl() {
        return webUrl;
    }

    public void setWebUrl(String webUrl) {
        this.webUrl = webUrl;
    }

    public TimeStats getTimeStats() {
        return timeStats;
    }

    public void setTimeStats(TimeStats timeStats) {
        this.timeStats = timeStats;
    }

    public Boolean getSquash() {
        return squash;
    }

    public void setSquash(Boolean squash) {
        this.squash = squash;
    }

    public TaskCompletionStatus getTaskCompletionStatus() {
        return taskCompletionStatus;
    }

    public void setTaskCompletionStatus(TaskCompletionStatus taskCompletionStatus) {
        this.taskCompletionStatus = taskCompletionStatus;
    }

    public Boolean getHasConflicts() {
        return hasConflicts;
    }

    public void setHasConflicts(Boolean hasConflicts) {
        this.hasConflicts = hasConflicts;
    }

    public Boolean getBlockingDiscussionsResolved() {
        return blockingDiscussionsResolved;
    }

    public void setBlockingDiscussionsResolved(Boolean blockingDiscussionsResolved) {
        this.blockingDiscussionsResolved = blockingDiscussionsResolved;
    }

    @NonNull
    public Long getIid() {
        return iid == null ? 0L : iid;
    }

    public void setIid(Long iid) {
        this.iid = iid;
    }

    @NonNull
    public String getTitle() {
        return title == null ? "" : title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @NonNull
    public State getState() {
        return state == null ? State.unknown : state;
    }

    public void setState(State state) {
        this.state = state;
    }

    @NonNull
    public String getDescription() {
        return description == null ? "" : description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum State {
        @JsonEnumDefaultValue
        unknown,
        opened, closed, merged, locked
    }

    public static class References {
        @JsonProperty("short")
        private String shortName;
        private String relative;
        private String full;

        public String getShortName() {
            return shortName;
        }

        public void setShortName(String shortName) {
            this.shortName = shortName;
        }

        public String getRelative() {
            return relative;
        }

        public void setRelative(String relative) {
            this.relative = relative;
        }

        public String getFull() {
            return full;
        }

        public void setFull(String full) {
            this.full = full;
        }
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class TaskCompletionStatus {
        private Integer count;
        private Integer completedCount;

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }

        public Integer getCompletedCount() {
            return completedCount;
        }

        public void setCompletedCount(Integer completedCount) {
            this.completedCount = completedCount;
        }
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class TimeStats {
        private String timeEstimate;
        private String totalTimeSpent;
        private String humanTimeEstimate;
        private String humanTotalTimeSpent;

        public String getTimeEstimate() {
            return timeEstimate;
        }

        public void setTimeEstimate(String timeEstimate) {
            this.timeEstimate = timeEstimate;
        }

        public String getTotalTimeSpent() {
            return totalTimeSpent;
        }

        public void setTotalTimeSpent(String totalTimeSpent) {
            this.totalTimeSpent = totalTimeSpent;
        }

        public String getHumanTimeEstimate() {
            return humanTimeEstimate;
        }

        public void setHumanTimeEstimate(String humanTimeEstimate) {
            this.humanTimeEstimate = humanTimeEstimate;
        }

        public String getHumanTotalTimeSpent() {
            return humanTotalTimeSpent;
        }

        public void setHumanTotalTimeSpent(String humanTotalTimeSpent) {
            this.humanTotalTimeSpent = humanTotalTimeSpent;
        }
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class SearchParams {
        private State state;
        private String sourceBranch;
        private String targetBranch;

        public State getState() {
            return state;
        }

        public void setState(State state) {
            this.state = state;
        }

        public String getSourceBranch() {
            return sourceBranch;
        }

        public void setSourceBranch(String sourceBranch) {
            this.sourceBranch = sourceBranch;
        }

        public String getTargetBranch() {
            return targetBranch;
        }

        public void setTargetBranch(String targetBranch) {
            this.targetBranch = targetBranch;
        }
    }
}
