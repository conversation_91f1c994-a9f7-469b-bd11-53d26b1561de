package cn.huolala.van.api.facade.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WindowParams {

    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("project_name")
    private String projectName;

    private String code;

    @JsonProperty("start_at")
    private long startAt; // timestamp

    @JsonProperty("end_at")
    private long endAt; // timestamp

    private String remark;

    public long getUserId() {
        return userId;
    }
    public void setUserId(long userId) {
        this.userId = userId;
    }
    public String getProjectName() {
        return projectName;
    }
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public long getStartAt() {
        return startAt;
    }
    public void setStartAt(long startAt) {
        this.startAt = startAt;
    }
    public long getEndAt() {
        return endAt;
    }
    public void setEndAt(long endAt) {
        this.endAt = endAt;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static class WindowExtra {
        private int priority;
        private Boolean close;
        private String code;
        @JsonProperty("ignore_time")
        private Boolean ignoreTime;
        private String remark;

        public WindowExtra() {}

        public WindowExtra(int priority, Boolean close, String code, Boolean ignoreTime, String remark) {
            this.priority = priority;
            this.close = close;
            this.code = code;
            this.ignoreTime = ignoreTime;
            this.remark = remark;
        }

        public Boolean getIgnoreTime() {
            return ignoreTime;
        }

        public void setIgnoreTime(Boolean ignoreTime) {
            this.ignoreTime = ignoreTime;
        }


        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public int getPriority() {
            return priority;
        }
        public void setPriority(int priority) {
            this.priority = priority;
        }
        public Boolean getClose() {
            return close;
        }
        public void setClose(Boolean close) {
            this.close = close;
        }
        public String getCode() {
            return code;
        }
        public void setCode(String code) {
            this.code = code;
        }
    }
}
