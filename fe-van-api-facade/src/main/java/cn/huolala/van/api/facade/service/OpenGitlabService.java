package cn.huolala.van.api.facade.service;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.facade.model.gitlab.GitlabBranch;
import cn.huolala.van.api.facade.model.gitlab.GitlabCompare;
import cn.huolala.van.api.facade.model.gitlab.GitlabProject;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@HermesService(value = "/van/gitlab")
public interface OpenGitlabService {
    @NonNull
    GitlabProject getProjectInfo(@NonNull IdOrName projectIdOrName);

    @Nullable
    GitlabBranch getBranchInfo(@NonNull IdOrName projectIdOrName, @NonNull String branchName);

    @NonNull
    GitlabCompare compare(@NonNull IdOrName projectIdOrName,
                          @NonNull String sourceBranch,
                          @NonNull String targetBranch);
}
