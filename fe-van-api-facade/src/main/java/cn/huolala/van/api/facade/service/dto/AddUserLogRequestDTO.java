package cn.huolala.van.api.facade.service.dto;

import cn.huolala.api.constants.enums.UserLogType;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class AddUserLogRequestDTO implements Serializable {
    private static final long serialVersionUID = 400030887764528722L;

    @NotNull
    private Long userId;

    @NotNull
    private UserLogType type;

    @NotNull
    private Long projectId;

    private Object meta;
    private String description;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserLogType getType() {
        return type;
    }

    public void setType(UserLogType type) {
        this.type = type;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Object getMeta() {
        return meta;
    }

    public void setMeta(Object meta) {
        this.meta = meta;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
