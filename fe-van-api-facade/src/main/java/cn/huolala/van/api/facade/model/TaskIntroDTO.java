package cn.huolala.van.api.facade.model;

import java.util.List;

public class TaskIntroDTO {
    private Long projectId;
    private String repository;
    private String commitMessage;
    private String commitHash;
    private List<Member> projectMembers;
    private String taskOwner;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getCommitMessage() {
        return commitMessage;
    }

    public void setCommitMessage(String commitMessage) {
        this.commitMessage = commitMessage;
    }

    public List<Member> getProjectMembers() {
        return projectMembers;
    }

    public void setProjectMembers(List<Member> projectMembers) {
        this.projectMembers = projectMembers;
    }

    public String getTaskOwner() {
        return taskOwner;
    }

    public void setTaskOwner(String taskOwner) {
        this.taskOwner = taskOwner;
    }

    public String getCommitHash() {
        return commitHash;
    }

    public void setCommitHash(String commitHash) {
        this.commitHash = commitHash;
    }

    public String getRepository() {
        return repository;
    }

    public void setRepository(String repository) {
        this.repository = repository;
    }

    public static class Member {
        private String account;
        private Integer role;

        public String getAccount() {
            return account;
        }

        public void setAccount(String account) {
            this.account = account;
        }

        public Integer getRole() {
            return role;
        }

        public void setRole(Integer role) {
            this.role = role;
        }
    }
}
