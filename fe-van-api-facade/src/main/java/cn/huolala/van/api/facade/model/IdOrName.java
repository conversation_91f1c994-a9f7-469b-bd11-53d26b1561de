package cn.huolala.van.api.facade.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public class IdOrName {
    @NonNull
    private final Object value;

    public IdOrName(@NonNull Long id) {
        value = id;
    }

    public IdOrName(@NonNull String name) {
        value = name;
    }

    @JsonCreator
    @Nullable
    public static IdOrName create(Object raw) {
        if (raw instanceof Number) {
            return new IdOrName(((Number) raw).longValue());
        }
        if (raw instanceof String) {
            return new IdOrName((String) raw);
        }
        return null;
    }

    @NonNull
    @JsonValue
    public Object getValue() {
        return value;
    }
}
