package cn.huolala.van.api.facade.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SubscribeGroupParams {

    @JsonProperty("chat_id")
    private String chatId;

    @JsonProperty("user_id")
    private long userId;

    @JsonProperty("project_name")
    private String projectName;

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
}
