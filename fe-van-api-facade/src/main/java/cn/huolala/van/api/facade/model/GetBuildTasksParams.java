package cn.huolala.van.api.facade.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GetBuildTasksParams {

    @JsonProperty("project_name")
    private String projectName;

    @JsonProperty("task_ids")
    private List<Long> taskIds;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public List<Long> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }

}
