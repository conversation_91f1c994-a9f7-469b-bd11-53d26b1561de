<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.huolala</groupId>
    <artifactId>fe-van-api</artifactId>
    <version>1.0.1-SNAPSHOT</version>
  </parent>
  <artifactId>fe-van-api-facade</artifactId>
  <description>interface definition</description>
  <dependencies>
    <dependency>
      <groupId>cn.huolala</groupId>
      <artifactId>fe-van-api-constants</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>cn.huolala.arch.hermes</groupId>
      <artifactId>hermes-api</artifactId>
      <version>2.10.2.RELEASE</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>1.7.0</version>
      </extension>
    </extensions>
    <plugins>
      <plugin>
        <groupId>cn.huolala.arch.hermes.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <version>0.6.1</version>
        <goals>
          <goal>compile</goal>
          <goal>compile-custom</goal>
        </goals>
        <configuration>
          <writeDescriptorSet>true</writeDescriptorSet>
          <outputDirectory>src/main/java</outputDirectory>
          <descriptorSetOutputDirectory>src/main</descriptorSetOutputDirectory>
          <descriptorSetFileName>all.desc</descriptorSetFileName>
          <attachDescriptorSet>true</attachDescriptorSet>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
