package cn.huolala.van.api.dao.entity;

import cn.huolala.van.api.dao.enums.Role;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;

@NoArgsConstructor
@Setter
@Getter
@Entity
@Table(name = "project_users")
public class ProjectUserEntity extends BaseEntity {
    private static final long serialVersionUID = -7021364189113030961L;
    private String uniqStr;
    private Long projectId;
    private Long userId;
    private Role role;
    private String meta;
}
