package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.MonitorRecordProjectEntity;

import org.springframework.boot.autoconfigure.security.SecurityProperties.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

import javax.transaction.Transactional;

public interface MonitorRecordProjectRespository extends JpaRepository<MonitorRecordProjectEntity, Long> {

        @Modifying
        @Transactional
        @Query(value = "INSERT INTO monitor_record (project_id, score, page_view_total, content, record_at)\n"
                        + "VALUES (?, ?, ?, ?, ?)\n"
                        + "ON DUPLICATE KEY UPDATE content = VALUES(content), updated_at = CURRENT_TIMESTAMP()", nativeQuery = true)
        void insert(long projectId, Double score, Long page_view_total, String content, Date record_at);

        // 查找用户某日有监控打分记录的所有项目列表
        @Query(value = "SELECT projects.name, projects.id,monitor_record.score,monitor_record.page_view_total FROM monitor_record inner join project_users on monitor_record.project_id = project_users.project_id left join projects on projects.id = monitor_record.project_id  where monitor_record.record_at = :record_at and monitor_record.score > 0 and monitor_record.page_view_total > 500 and project_users.role in (2,3) and project_users.user_id = :user_id order by monitor_record.page_view_total desc limit 20", nativeQuery = true)
        List<MonitorRecordProjectEntity> findMonitorRecordsByUser(@Param("user_id") Long userId,
                        @Param("record_at") Date recordAt);

        @Query(value = "SELECT projects.name, projects.id,monitor_record.score,monitor_record.page_view_total FROM monitor_record right join projects on projects.id = monitor_record.project_id  where monitor_record.record_at = :record_at and monitor_record.score > 0 and monitor_record.page_view_total > 500 and  projects.id in :project_ids order by monitor_record.page_view_total desc limit 20", nativeQuery = true)
        List<MonitorRecordProjectEntity> findMonitorRecordsByProjectIds(@Param("project_ids") List<Long> projectIds,
                        @Param("record_at") Date recordAt);
}