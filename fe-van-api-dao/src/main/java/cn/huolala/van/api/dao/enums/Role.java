package cn.huolala.van.api.dao.enums;

import cn.huolala.van.api.facade.model.enums.VanUserRoleEnum;
import org.springframework.lang.NonNull;

public enum Role {
    NoRole(0), TestRole(1), DevRole(2), AdminRole(4);

    public final int bitValue;

    Role(int bitValue) {
        this.bitValue = bitValue;
    }

    @NonNull
    public static Role fromLegacy(@NonNull VanUserRoleEnum legacyRole) {
        return values()[legacyRole.ordinal()];
    }
}
