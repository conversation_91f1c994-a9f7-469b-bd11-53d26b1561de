package cn.huolala.van.api.dao.entity;

import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Setter
@Getter
@Entity
@Table(name = "project_dependencies")
public class ProjectDependencyEntity extends BaseEntity {
    @Column(name = "task_id")
    private Long taskId;

    private String name;
    private String version;
    private Type type;

    public enum Type {
        HLL, WORKERS, PUBLIC, INDIRECT;

        @NonNull
        public static Type detect(@NonNull String packageName) {
            if (packageName.startsWith("@hll/")) return HLL;
            else if (packageName.startsWith("@workers-sdk/")) return WORKERS;
            else return PUBLIC;
        }
    }
}
