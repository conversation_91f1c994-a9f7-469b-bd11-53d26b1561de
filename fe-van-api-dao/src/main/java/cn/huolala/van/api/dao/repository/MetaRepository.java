package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.MetaEntity;
import cn.huolala.van.api.dao.model.IdContentPair;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface MetaRepository extends JpaRepository<MetaEntity, Long> {
    @Query(value = "SELECT * FROM metas WHERE type = ?1 AND meta_id = ?2", nativeQuery = true)
    Optional<MetaEntity> get(int type, Long id);

    @Query(value = "SELECT * FROM metas WHERE type = ?1 AND meta_id IN ?2 FOR UPDATE", nativeQuery = true)
    List<MetaEntity> findForUpdate(int type, Collection<Long> ids);

    @Query(value = "/* MetaRepository.findPairByMetaIds */\n" +
            "SELECT meta_id AS id, meta AS content FROM metas WHERE meta IS NOT NULL\n" +
            "AND type = :type AND meta_id IN :metaIds",
            nativeQuery = true)
    List<IdContentPair> findPairByMetaIds(@Param("type") int type, @Param("metaIds") Collection<Long> metaIds);

    @Query(value = "/* MetaRepository.findPairByMetaId */\n" +
            "SELECT meta FROM metas WHERE meta IS NOT NULL\n" +
            "AND type = :type AND meta_id = :metaId",
            nativeQuery = true)
    Optional<String> findPairByMetaId(@Param("type") int type, @Param("metaId") Long metaId);

    @NonNull
    @Query(value = "/* MetaRepository.findAfter */\n" +
            "SELECT * FROM metas WHERE updated_at > ?1\n" +
            "ORDER BY updated_at DESC LIMIT ?2", nativeQuery = true)
    List<MetaEntity> findAfter(LocalDateTime maxUpdatedAt, int limit);

    @Query(value = "SELECT COUNT(1) FROM metas WHERE type = ?1 AND meta_id = ?2 LIMIT 1", nativeQuery = true)
    int exists(int type, long metaId);

    // miniprogram migration use
    @Deprecated
    @Query(value = "SELECT * FROM metas WHERE type = ?1 AND id > ?2 ORDER BY id ASC LIMIT ?3", nativeQuery = true)
    List<MetaEntity> findMiniprogramDeployMetaByType(int type, Long id, int limit);
}
