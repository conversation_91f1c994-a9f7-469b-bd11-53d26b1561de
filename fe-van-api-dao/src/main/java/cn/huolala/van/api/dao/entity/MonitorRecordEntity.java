package cn.huolala.van.api.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "monitor_record")
public class MonitorRecordEntity extends BaseEntity {
    @Column(name = "project_id")
    private Long projectId;

    @Column(name = "page_view_total")
    private Double pageViewTotal;

    private Double score;

    private String content;

    @Column(name = "record_at")
    private LocalDateTime recordAt;
}
