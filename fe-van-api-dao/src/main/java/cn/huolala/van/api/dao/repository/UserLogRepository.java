package cn.huolala.van.api.dao.repository;

import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.dao.entity.UserLogEntity;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface UserLogRepository extends JpaRepository<UserLogEntity, Long> {
    /**
     * 找到 userId 再 updateAt 之前某条操作记录
     *
     * @param userId
     * @param updatedAt
     * @return
     */
    Optional<UserLogEntity> getFirstByUserIdAndUpdatedAtIsBeforeAndTypeOrderByIdDesc(Long userId, LocalDateTime updatedAt, UserLogType type);

    List<UserLogEntity> findByUserIdAndTypeInAndUpdatedAtIsBefore(Long userId, Set<UserLogType> types, LocalDateTime updatedAt);

    int countByUserIdAndTypeIn(Long userId, UserLogType[] types);
}
