package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.MiniprogramDeployHistoryEntity;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

public interface MiniprogramDeployHistoryRepository extends JpaRepository<MiniprogramDeployHistoryEntity, Long> {

    // 查询最近一次的上传操作
    @Query(
        value = "SELECT * FROM miniprogram_deploy_histories WHERE project_id = ?1 AND task_id = ?2 AND type = ?3 ORDER BY created_at DESC LIMIT 1",
        nativeQuery = true)
    Optional<MiniprogramDeployHistoryEntity> findLatestUploadDeploy(@NonNull long projectId, @NonNull long taskId, @NonNull int type);

    // 查询使用每个robot发布的最近一条操作
    @Query(
        value =
            "SELECT mdh.*"
                + " FROM miniprogram_deploy_histories AS mdh"
                + " JOIN(SELECT Max(deploy_task_id) AS latest_deploy_task_id,robot FROM miniprogram_deploy_histories"
                + " WHERE project_id = ?1 AND task_id = ?2 AND type = ?3 GROUP BY robot) latest"
                + " ON mdh.deploy_task_id = latest.latest_deploy_task_id AND mdh.robot = latest.robot",
        nativeQuery = true)
    List<MiniprogramDeployHistoryEntity> findLatestDeployByTypeOfEachRobot(@NonNull long projectId, @NonNull long taskId, @NonNull int type);

    // 根据 deployTaskId 查询记录
    Optional<MiniprogramDeployHistoryEntity> findByProjectIdAndDeployTaskId(@NonNull long projectId, @NonNull long deployTaskId);


    // 迁移数据时更新时间 后可删除
    @Transactional
    @Modifying
    @Query(
        value =
            "UPDATE miniprogram_deploy_histories "
                + "SET created_at=?2, updated_at=?3 "
                + "WHERE id=?1",
        nativeQuery = true)
    void updateCreatedAtAndUpdatedAtById(@NonNull long id, String createdAt, String updatedAt);
}

