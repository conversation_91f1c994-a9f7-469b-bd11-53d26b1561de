package cn.huolala.van.api.dao.entity;

import cn.huolala.api.constants.enums.UserLogType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Entity
@Table(name = "user_logs")
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UserLogEntity extends BaseEntity {

    private static final long serialVersionUID = -2201759208888559581L;

    private Long projectId;

    private Long userId;

    private UserLogType type;

    @Size(max = 1024)
    private String meta;

    @Size(max = 1024)
    private String description;
}
