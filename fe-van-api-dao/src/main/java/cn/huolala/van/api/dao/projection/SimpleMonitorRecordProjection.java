package cn.huolala.van.api.dao.projection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;

import java.time.LocalDateTime;

public interface SimpleMonitorRecordProjection {
    @NonNull
    Long getProjectId();

    @NonNull
    Double getPageViewTotal();

    @NonNull
    Double getScore();

    @NonNull
    LocalDateTime getRecordAt();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Impl implements SimpleMonitorRecordProjection {
        private Long projectId;
        private Double score;
        private Double pageViewTotal;
        private LocalDateTime recordAt;
    }
}
