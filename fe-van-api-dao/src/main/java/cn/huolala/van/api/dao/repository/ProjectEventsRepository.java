package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.ProjectEventsEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;

public interface ProjectEventsRepository extends JpaRepository<ProjectEventsEntity, Long> {
    List<ProjectEventsEntity> findByNameAndProjectOrderByUpdatedAtDesc(String name, Long project, Pageable pageable);

    List<ProjectEventsEntity> findByNameAndCreatorOrderByUpdatedAtDesc(String name, String creator, Pageable pageable);

    List<ProjectEventsEntity> findByCreatorAndProjectOrderByUpdatedAtDesc(String creator, Long project,
                                                                          Pageable pageable);

    ProjectEventsEntity save(ProjectEventsEntity entity);

    @NonNull
    @Query(value = "SELECT MAX(id) FROM project_events", nativeQuery = true)
    Optional<Long> getMaxId();

    @NonNull
    @Query(value = "/* ProjectEventsRepository.findAfter */\n" +
            "SELECT * FROM project_events WHERE id > ?1\n" +
            "ORDER BY id DESC LIMIT ?2", nativeQuery = true)
    List<ProjectEventsEntity> findAfter(long id, int limit);
}
