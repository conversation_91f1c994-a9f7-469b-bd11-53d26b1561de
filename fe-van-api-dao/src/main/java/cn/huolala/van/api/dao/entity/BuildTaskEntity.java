package cn.huolala.van.api.dao.entity;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.BuildTaskType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "build_tasks")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class BuildTaskEntity extends BaseEntity {
    private static final long serialVersionUID = 8642864086744654847L;
    private String hash;
    private String branch;
    private String commitMessage;

    private String email;
    private String username;

    private BuildTaskStatus status;
    private BuildTaskType type;

    private String buildId;
    private Long projectId;

    @Nullable
    public String fetchUserUniq() {
        return StringUtils.substringBefore(email, "@");
    }
}
