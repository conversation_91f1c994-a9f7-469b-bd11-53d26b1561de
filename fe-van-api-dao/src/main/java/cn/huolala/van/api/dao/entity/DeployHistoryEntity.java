package cn.huolala.van.api.dao.entity;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "deploy_histories")
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class DeployHistoryEntity extends BaseEntity {

    private static final long serialVersionUID = 283521388263919407L;

    private Long creatorId;

    private Long taskId;

    private Long projectId;

    private String env;
}
