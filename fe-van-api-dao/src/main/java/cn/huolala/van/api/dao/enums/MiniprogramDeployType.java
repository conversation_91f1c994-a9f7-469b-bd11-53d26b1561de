package cn.huolala.van.api.dao.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.NUMBER)
public enum MiniprogramDeployType {
    Unknown(0),
    Preview(1),
    Upload(2);

    @Getter
    private final int type;

    MiniprogramDeployType(int type) {
        this.type = type;
    }

    @JsonCreator
    public static MiniprogramDeployType fromInt(int value) {
        for(MiniprogramDeployType deployType: MiniprogramDeployType.values()) {
            if (deployType.getType() == value) {
                return deployType;
            }
        }
        return MiniprogramDeployType.Unknown;
    }

    @JsonValue
    public int toValue() {
        return type;
    }
}
