package cn.huolala.van.api.dao.entity;

import cn.huolala.api.constants.enums.MedalEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Entity
@Table(name = "user_medals")
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UserMedalEntity extends BaseEntity {
    private static final long serialVersionUID = -2728417112772518186L;

    private Long userId;

    @Enumerated(EnumType.STRING)
    private MedalEnum uniqName;
    @Size(max = 2000)
    private String remark;
}
