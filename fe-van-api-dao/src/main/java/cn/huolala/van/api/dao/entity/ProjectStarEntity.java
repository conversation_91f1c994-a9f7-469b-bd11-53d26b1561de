package cn.huolala.van.api.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "project_stars")
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectStarEntity extends BaseEntity {
    private static final long serialVersionUID = 7734724362481817971L;
    private Long projectId;
    private Long userId;
}
