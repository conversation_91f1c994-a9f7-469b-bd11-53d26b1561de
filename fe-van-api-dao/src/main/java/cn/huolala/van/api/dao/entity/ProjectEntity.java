package cn.huolala.van.api.dao.entity;

import cn.huolala.api.constants.enums.ProjectType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.springframework.lang.Nullable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Table(name = "projects")
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectEntity extends BaseEntity {

    private static final long serialVersionUID = 7734724362481817971L;

    @Column(unique = true, length = 50)
    private String name;

    private ProjectType type;

    @Column(length = 200)
    private String description;

    @Nullable
    private String config;

    private String repository;

    private LocalDateTime activatedAt;
}
