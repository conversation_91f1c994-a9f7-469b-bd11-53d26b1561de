package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.ProjectEntity;
import cn.huolala.van.api.dao.model.IdContentPair;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ProjectRepository extends JpaRepository<ProjectEntity, Long> {
    @Query(value = "/* ProjectRepository.findIdsByNames */ \n" +
            "SELECT id, name AS content FROM projects " +
            "WHERE name IN :names", nativeQuery = true)
    List<IdContentPair> findIdsByNames(@Param("names") Set<String> names);

    @Query(value = "/* ProjectRepository.findNamesByIds */ \n" +
            "SELECT id, name AS content FROM projects " +
            "WHERE id IN :ids", nativeQuery = true)
    List<IdContentPair> findNamesByIds(@Param("ids") Set<Long> ids);

    List<ProjectEntity> findByIdIn(Set<Long> ids);

    @Query(value = "/* ProjectRepository.countEachTypes */ \n" +
            "SELECT type, count(1) FROM projects GROUP BY type", nativeQuery = true)
    List<Integer[]> countEachTypes();

    @Modifying
    @Transactional
    @CanIgnoreReturnValue
    @Query(value = "UPDATE projects SET config = ?2 WHERE id = ?1", nativeQuery = true)
    int updateConfig(long projectId, String newConfig);

    @Modifying
    @Transactional
    @CanIgnoreReturnValue
    @Query(value = "UPDATE projects SET description = ?2 WHERE id = ?1", nativeQuery = true)
    int updateDescription(long projectId, String description);

    @Query(value = "SELECT name FROM projects WHERE repository != '' AND type IN ?1", nativeQuery = true)
    List<String> findNamesByTypes(Set<Integer> types);

    @NonNull
    @Query(value = "SELECT * FROM projects WHERE id = ?1", nativeQuery = true)
    Optional<ProjectEntity> getById(@NonNull Long id);

    @NonNull
    @Query(value = "SELECT * FROM projects WHERE id IN ?1", nativeQuery = true)
    List<ProjectEntity> findByIds(@NonNull Collection<Long> id);

    @NonNull
    @Query(value = "SELECT id, name AS content FROM projects WHERE repository != ''", nativeQuery = true)
    List<IdContentPair> findAllProjects();

    @NonNull
    Long countByName(@NonNull String name);

    @Modifying
    @Transactional
    @CanIgnoreReturnValue
    @Query(value = "UPDATE projects SET repository = ?2, updated_at = ?3 WHERE id = ?1", nativeQuery = true)
    int updateRepository(@NonNull long projectId, String repository, String updatedAt);

}
