package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.OneTimeWindowEntity;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import com.google.errorprone.annotations.CanIgnoreReturnValue;


public interface OneTimeWindowRepository extends JpaRepository<OneTimeWindowEntity, Long> {

    @Query(value = "SELECT * FROM one_time_window\n" +
            "WHERE ((start_at = :epoch AND end_at = :epoch) OR (start_at >= :startAt AND start_at <= :endAt) OR (end_at >= :startAt AND end_at <= :endAt) OR (start_at <= :startAt AND end_at >= :endAt))"
            +
            " AND (project_id = 0 OR project_id = :projectId)" +
            " AND (status = 0 OR status = :status)\n" +
            "ORDER BY start_at DESC", nativeQuery = true)
    List<OneTimeWindowEntity> listOneTimeWindow(@Param("projectId") long projectId, @Param("epoch") LocalDateTime epoch,
            @Param("startAt") LocalDateTime startAt, @Param("endAt") LocalDateTime endAt, @Param("status") int status);


    @CanIgnoreReturnValue
    @Modifying
    @Transactional
    @Query(value = "UPDATE one_time_window SET status = :status WHERE id = :id AND project_id = :projectId", nativeQuery = true)
    int updateOneTimeWindowStatus(@Param("id") long id, @Param("projectId") long projectId,
            @Param("status") int status);

}
