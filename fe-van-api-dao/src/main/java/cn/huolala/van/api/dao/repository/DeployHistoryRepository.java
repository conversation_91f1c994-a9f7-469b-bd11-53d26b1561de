package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.DeployHistoryEntity;
import cn.huolala.van.api.dao.model.IdContentPair;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.Nullable;

import java.util.List;

public interface DeployHistoryRepository extends JpaRepository<DeployHistoryEntity, Long> {
    @Query(value = "/* DeployHistoryRepository.getLatestTaskIdForEachEnv */\n" +
            "SELECT t1.task_id AS id, t1.env AS content\n" +
            "FROM deploy_histories t1\n" +
            "JOIN (\n" +
            "  SELECT MAX(updated_at) AS updated_at, project_id, env, LEFT(env, 3) AS name\n" +
            "  FROM deploy_histories\n" +
            "  WHERE project_id = :projectId\n" +
            "  GROUP BY env\n" +
            ") t2\n" +
            "ON t1.updated_at = t2.updated_at\n" +
            "AND t1.project_id = t2.project_id\n" +
            "AND t1.env = t2.env\n" +
            "ORDER BY FIELD(t2.name, 'stg', 'pre'), t2.env", nativeQuery = true)
    List<IdContentPair> getLatestTaskIdForEachEnv(@Param("projectId") Long projectId);

    @Modifying
    @Query(value = "/* DeployHistoryRepository.insertOrUpdate */\n" +
            "INSERT INTO deploy_histories (project_id, task_id, env, creator_id)\n" +
            "VALUES (?, ?, ?, ?)\n" +
            "ON DUPLICATE KEY UPDATE env = VALUES(env), updated_at = CURRENT_TIMESTAMP()"
            , nativeQuery = true)
    void insertOrUpdate(long projectId, long taskId, String env, long userId);

    @Nullable
    @Query(value = "SELECT * FROM deploy_histories WHERE project_id = ?1 AND task_id = ?2 AND env = ?3"
            , nativeQuery = true)
    DeployHistoryEntity get(long projectId, long taskId, String env);

    @Query(value = "SELECT COUNT(1) FROM deploy_histories WHERE project_id = ?1 AND task_id = ?2 LIMIT 1",
            nativeQuery = true)
    int isBeingUsedInTestEnv(long projectId, long taskId);

    @Nullable
    @Query(value = "SELECT * FROM deploy_histories WHERE project_id = ?1 AND env = ?2 ORDER BY updated_at DESC LIMIT 1", nativeQuery = true)
    DeployHistoryEntity getLatestDeploy(long projectId, String env);
}
