package cn.huolala.van.api.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "feishu_subscription")
@SuperBuilder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FeishuSubscriptionEntity extends BaseEntity {
    private static final long serialVersionUID = 3578477268930949605L;
    private Long projectId;
    private Long userId;
    private String chatId;
}
