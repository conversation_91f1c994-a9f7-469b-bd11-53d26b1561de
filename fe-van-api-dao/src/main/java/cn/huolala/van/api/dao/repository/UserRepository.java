package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.UserEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface UserRepository extends JpaRepository<UserEntity, Long> {
    @Nullable
    @Query(value = "SELECT * FROM users WHERE id = ?1", nativeQuery = true)
    UserEntity getById(@Nullable Long id);

    @NonNull
    @Query(value = "SELECT * FROM users WHERE id IN ?1", nativeQuery = true)
    List<UserEntity> findByIds(@NonNull Collection<Long> ids);

    @Nullable
    @Query(value = "SELECT * FROM users WHERE uniq_id = ?1", nativeQuery = true)
    UserEntity getByUniqId(@Nullable String uniqId);

    @NonNull
    @Query(value = "SELECT * FROM users WHERE uniq_id IN ?1", nativeQuery = true)
    List<UserEntity> findByUniqIds(@NonNull Collection<String> uniqId);

    // 查找所有某日有监控打分记录的所有项目的用户列表
    @Query(value = "/* UserRepository.findMonitorRecordUsersByRecordAt */ \n" +
            "SELECT users.id, users.uniq_id, users.updated_at, users.created_at, users.name, users.email " +
            "FROM monitor_record INNER JOIN project_users " +
            "ON monitor_record.project_id = project_users.project_id " +
            "LEFT JOIN users ON users.id = project_users.user_id " +
            "WHERE monitor_record.record_at = :record_at " +
            "AND monitor_record.score > 0 " +
            "AND monitor_record.page_view_total > 0 " +
            "AND project_users.role IN (2, 3) " +
            "GROUP BY project_users.user_id", nativeQuery = true)
    List<UserEntity> findMonitorRecordUsersByRecordAt(@Param("record_at") Date recordAt);

    /**
     * 查询出来所有的属于项目的用户
     */
    @Query(value = "\n" +
            "SELECT DISTINCT ue FROM UserEntity ue " +
            "INNER JOIN ProjectUserEntity pue ON ue.id = pue.userId")
    List<UserEntity> findAllProjectUser();

    @Modifying
    @Transactional
    @Query(value = "/* UserRepository.insertOrUpdate */ \n" +
            "INSERT INTO users (uniq_id, name, email) " +
            "VALUES (:uniqId, :name, :email) " +
            "ON DUPLICATE KEY UPDATE name = VALUES(name), email = VALUES(email)", nativeQuery = true)
    void insertOrUpdate(@Param("uniqId") String uniqId, @Param("name") String name, @Param("email") String email);

    @Query(value = "/* UserRepository.searchUser */\n" +
            "SELECT u.uniq_id, u.name FROM users u\n" +

            // Left join the users of the specified project.
            "LEFT JOIN (\n" +
            "  SELECT user_id, MAX(role) AS role, MAX(updated_at) AS updated_at\n" +
            "  FROM project_users WHERE project_id = :projectId GROUP BY user_id\n" +
            ") pu1 ON u.id = pu1.user_id\n" +

            // Left join the friends of searcher.
            // The friends are defined as participants in the same project as the searcher.
            "LEFT JOIN (\n" +
            "  SELECT s1.user_id, MAX(s1.role) AS role, MAX(s1.updated_at) AS updated_at\n" +
            "  FROM project_users s1\n" +
            "  LEFT JOIN project_users s2 ON s1.project_id = s2.project_id AND s2.user_id = :searcherId\n" +
            "  GROUP BY s1.user_id\n" +
            ") pu2 ON u.id = pu2.user_id\n" +

            "WHERE (u.uniq_id LIKE %:keyword% OR name LIKE %:keyword%)\n" +
            "ORDER BY\n" +
            "pu1.role DESC, pu1.updated_at,\n" + // Show specified project users first.
            "pu2.role DESC, pu2.updated_at DESC,\n" + // Secondly, show the friends of the searcher.
            "u.updated_at DESC\n" + // Otherwise, add a default order key.

            "LIMIT :limit", nativeQuery = true)
    List<String[]> searchUser(
            @NonNull @Param("keyword") String keyword,
            @Nullable @Param("searcherId") Long searcherId,
            @Nullable @Param("projectId") Long projectId,
            @Param("limit") int limit);
}
