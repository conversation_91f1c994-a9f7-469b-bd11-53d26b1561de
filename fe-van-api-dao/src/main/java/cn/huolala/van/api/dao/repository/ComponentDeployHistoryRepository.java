package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.ComponentDeployHistoryEntity;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ComponentDeployHistoryRepository extends JpaRepository<ComponentDeployHistoryEntity, Long> {
    int countByCreatorId(Long creatorId);

    int countByProjectIdAndTaskId(Long projectId, Long taskId);

    int countByProjectIdAndTaskIdAndVersion(Long projectId, Long taskId, String version);

}
