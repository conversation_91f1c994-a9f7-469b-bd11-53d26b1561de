package cn.huolala.van.api.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "canary_histories")
@SuperBuilder
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class CanaryHistoryEntity extends BaseEntity {

    private static final long serialVersionUID = 5075325292594394815L;

    private Long creatorId;

    private Long projectId;

    private String message;

    private String canary;

    @Column(name = "change_level")
    private String region;

    @Column(name = "task_id_list")
    private String taskIdList;
}
