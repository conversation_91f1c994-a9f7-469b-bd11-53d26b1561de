package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.ProjectDependencyEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface ProjectDependenciesRepository extends JpaRepository<ProjectDependencyEntity, Long> {
    @Query(value = "/* ProjectDependenciesRepository.findUsesByPackageName */ \n" +
            "SELECT pd.* FROM project_dependencies pd " +
            "JOIN (" +
            "SELECT d.name, t.project_id, MAX(d.created_at) AS created_at " +
            "FROM project_dependencies d " +
            "JOIN build_tasks t ON d.task_id = t.id " +
            "WHERE d.name = :packageName " +
            "GROUP BY t.project_id" +
            ") x " +
            "ON pd.created_at = x.created_at AND pd.name = x.name " +
            "GROUP BY x.project_id " +
            "ORDER BY pd.created_at DESC",
            nativeQuery = true)
    List<ProjectDependencyEntity> findUsesByPackageName(@Param("packageName") String packageName);

    @Query(value = "/* ProjectDependenciesRepository.filterTaskIds */ \n" +
            "SELECT task_id " +
            "FROM project_dependencies " +
            "WHERE task_id IN ?1 " +
            "GROUP BY task_id",
            nativeQuery = true)
    Set<Long> filterTaskIds(Collection<Long> taskIds);
}
