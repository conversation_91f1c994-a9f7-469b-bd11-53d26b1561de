
package cn.huolala.van.api.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import cn.huolala.van.api.dao.enums.MiniprogramDeployType;

@Entity
@Table(name = "miniprogram_deploy_histories")
@Setter
@Getter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class MiniprogramDeployHistoryEntity extends BaseEntity {

    @Column(name = "project_id")
    private Long projectId;

    // 构建任务id
    @Column(name = "task_id")
    private Long taskId;
    
    // 发布操作(预览或者上传) 任务id
    @Column(name = "deploy_task_id")
    private Long deployTaskId;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "type")
    private MiniprogramDeployType type;

    private Integer robot;

    private String version;

    private String description;
}
