package cn.huolala.van.api.dao.entity;

import cn.huolala.api.constants.enums.MetaType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "metas")
@SuperBuilder
@Setter
@Getter
@AllArgsConstructor
public class MetaEntity extends BaseEntity {
    private static final long serialVersionUID = 4649107737220673651L;

    private MetaType type;
    private Long metaId;
    private String meta;

    public MetaEntity() {

    }

    public MetaEntity(MetaType type, Long metaId) {
        this.type = type;
        this.metaId = metaId;
    }
}
