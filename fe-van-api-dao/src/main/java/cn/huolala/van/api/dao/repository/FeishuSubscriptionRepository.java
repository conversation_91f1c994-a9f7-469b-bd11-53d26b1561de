package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.FeishuSubscriptionEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface FeishuSubscriptionRepository extends JpaRepository<FeishuSubscriptionEntity, Long> {
    @Query(value = "SELECT * FROM feishu_subscription WHERE project_id = ?", nativeQuery = true)
    List<FeishuSubscriptionEntity> findByProjectId(long projectId);

}
