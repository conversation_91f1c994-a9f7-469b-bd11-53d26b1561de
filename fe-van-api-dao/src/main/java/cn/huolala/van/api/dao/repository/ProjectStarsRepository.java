package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.ProjectStarEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface ProjectStarsRepository extends JpaRepository<ProjectStarEntity, Long> {
    @Query("SELECT projectId FROM ProjectStarEntity WHERE userId = ?1")
    List<Long> listStaredProjectIdListByUserId(Long userId);
}
