package cn.huolala.van.api.dao.entity;

import cn.huolala.van.api.dao.enums.ComponentPublishStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "component_deploy_histories")
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ComponentDeployHistoryEntity extends BaseEntity {
    private String version;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "project_id")
    private Long projectId;

    @Column(name = "task_id")
    private Long taskId;

    private ComponentPublishStatus status;

    private String message;
}
