package cn.huolala.van.api.dao.repository;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.entity.UserMedalEntity;
import cn.huolala.van.api.dao.projection.MedalRankingProjection;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

public interface UserMedalRepository extends JpaRepository<UserMedalEntity, Long> {

    List<UserMedalEntity> findByUserIdOrderByCreatedAtDesc(Long userId);

    List<UserMedalEntity> findByUserIdAndCreatedAtBeforeOrderByCreatedAtDesc(Long userId, LocalDateTime endTime);

    long countByUniqName(MedalEnum name);

    List<UserMedalEntity> findByCreatedAtAfterAndCreatedAtBefore(LocalDateTime start, LocalDateTime end);

    @Query(value = "/* UserMedalRepository.rank */ \n" +
            "SELECT\n" +
            "u.uniq_id AS userUniqId,\n" +
            "u.name AS userName,\n" +
            "GROUP_CONCAT(m.uniq_name ORDER BY m.created_at DESC) AS medals,\n" +
            "COUNT(1) AS quantity,\n" +
            "MAX(m.created_at) AS activatedAt\n" +

            "FROM user_medals m\n" +
            "JOIN users u ON m.user_id = u.id\n" +
            "WHERE u.uniq_id NOT IN :excludeUsers\n" +

            "GROUP BY m.user_id\n" +
            "ORDER BY quantity DESC, activatedAt",

            countQuery = "/* UserMedalRepository.rank#count */ \n" +
                    "SELECT COUNT(DISTINCT m.user_id)\n" +
                    "FROM user_medals m\n" +
                    "JOIN users u ON m.user_id = u.id\n" +
                    "WHERE u.uniq_id NOT IN :excludeUsers ",
            nativeQuery = true)
    Page<MedalRankingProjection> rank(@Param("excludeUsers") Collection<String> excludeUsers, Pageable pageRequest);
}
