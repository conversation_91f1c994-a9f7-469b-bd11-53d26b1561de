package cn.huolala.van.api.dao.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.Entity;
import javax.persistence.Table;

import cn.huolala.api.constants.enums.WindowApprovalStatus;

import java.time.LocalDateTime;

@Entity
@Table(name = "one_time_window")
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class OneTimeWindowEntity extends BaseEntity {

    private Long projectId;

    private Long userId;

    private WindowApprovalStatus status;

    private String extra;

    private LocalDateTime startAt;

    private LocalDateTime endAt;

}
