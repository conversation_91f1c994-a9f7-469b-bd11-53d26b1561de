package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.BuildTaskEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface BuildTaskRepository extends JpaRepository<BuildTaskEntity, Long> {
    Optional<BuildTaskEntity> getFirstByUsername(String username);

    @Query(value = "SELECT * FROM build_tasks WHERE project_id = ?1 AND id = ?2", nativeQuery = true)
    Optional<BuildTaskEntity> findByProjectIdAndId(@NonNull Long projectId, @NonNull Long id);

    @Query(value = "SELECT * FROM build_tasks WHERE project_id = ?1 AND id IN ?2", nativeQuery = true)
    List<BuildTaskEntity> findByProjectIdAndIdIn(@NonNull Long projectId, @NonNull Set<Long> ids);

    @Query(value = "/* BuildTaskRepository.findProjectIdsByTaskIds */\n" +
            "SELECT id, project_id FROM build_tasks\n" +
            "WHERE id IN ?1",
            nativeQuery = true)
    List<Long[]> findProjectIdsByTaskIds(Collection<Long> ids);

    @Query(value = "/* BuildTaskRepository.findLatestSuccessTaskByProjectIds */\n" +
            "SELECT t1.project_id, t1.id FROM build_tasks t1\n" +
            "JOIN (\n" +
            "  SELECT project_id, MAX(created_at) AS created_at\n" +
            "  FROM build_tasks\n" +
            "  WHERE project_id IN :projectIds AND status = 2 AND type = 0\n" +
            "  GROUP BY project_id\n" +
            ") t2\n" +
            "ON t1.project_id = t2.project_id\n" +
            "AND t1.created_at = t2.created_at", nativeQuery = true)
    List<Long[]> findLatestSuccessTaskByProjectIds(@Param("projectIds") Set<Long> projectIds);

    @Query(value = "/* BuildTaskRepository.findLatestTaskByBranches */\n" +
            "SELECT t1.*\n" +
            "FROM build_tasks t1\n" +
            "JOIN (\n" +
            "  SELECT project_id, branch, MAX(created_at) AS created_at\n" +
            "  FROM build_tasks\n" +
            "  WHERE project_id = ?1 AND type = 0 AND branch IN ?2\n" +
            "  GROUP BY branch\n" +
            ") t2\n" +
            "ON t1.project_id = t2.project_id\n" +
            "AND t1.branch = t2.branch\n" +
            "AND t1.created_at = t2.created_at", nativeQuery = true)
    List<BuildTaskEntity> findLatestTaskByBranches(long projectIds, @NonNull Collection<String> branches);

    @Query(value = "/* countEachTypes */ \n" +
            "SELECT p.type, count(1)" +
            "FROM build_tasks t, projects p " +
            "WHERE p.id = t.project_id " +
            "GROUP BY p.type", nativeQuery = true)
    List<Integer[]> countEachTypes();

    @Query(value = "SELECT count(1) FROM build_tasks WHERE email LIKE ?1", nativeQuery = true)
    int countByEmailLike(String emailPrefix);

    @NonNull
    @Query(value = "/* BuildTaskRepository.listByType */ \n" +
            "SELECT * FROM build_tasks \n" +
            "WHERE project_id = ?1 AND type = ?2 \n" +
            "ORDER BY created_at DESC, id DESC \n" +
            "LIMIT ?3 OFFSET ?4", nativeQuery = true)
    List<BuildTaskEntity> listByType(long projectId, @NonNull int type, int limit, int offset);

    @NonNull
    @Query(value = "SELECT MAX(updated_at) FROM build_tasks", nativeQuery = true)
    LocalDateTime getMaxUpdatedAt();

    @NonNull
    @Query(value = "/* BuildTaskRepository.findAfter */\n" +
            "SELECT * FROM build_tasks WHERE updated_at > ?1\n" +
            "ORDER BY updated_at DESC LIMIT ?2", nativeQuery = true)
    List<BuildTaskEntity> findAfter(LocalDateTime maxUpdatedAt, int limit);

    @Query(value = "SELECT id FROM build_tasks WHERE id IN ?1 AND project_id != ?2", nativeQuery = true)
    Long[] findOutOfProjectId(@NonNull Set<Long> ids, long projectId);

    @Query(value = "/* BuildTaskRepository.listTaskIds */\n" +
            "SELECT p.id AS projectId, t.id AS taskId\n" +
            "FROM build_tasks t, projects p\n" +
            "WHERE t.project_id = p.id\n" +
            "AND t.type = 0\n" + //  Only NormalBuild
            "AND p.type != 4\n" + // Exclude Miniprogram
            "AND t.updated_at >= ?1\n" +
            "AND t.updated_at < ?2\n" +
            "LIMIT ?3",
            nativeQuery = true)
    List<Long[]> listTaskIds(OffsetDateTime startTime, OffsetDateTime endTime, int limit);

    @Modifying
    @Query(value = "/* BuildTaskRepository.updateStatus */ \n" +
            "UPDATE build_tasks SET status = ?3\n" +
            "WHERE project_id = ?1 AND id = ?2\n" +
            "AND status = ?4", nativeQuery = true)
    int updateStatus(long projectId, long taskId, int status, int prevStatus);

    @Modifying
    @Query(value = "/* BuildTaskRepository.updateBranch */ \n" +
            "UPDATE build_tasks SET branch = ?3\n" +
            "WHERE project_id = ?1 AND id = ?2", nativeQuery = true)
    void updateBranch(long projectId, long taskId, @Nullable String branch);

    @Modifying
    @Query(value = "/* BuildTaskRepository.updateBuildId */ \n" +
            "UPDATE build_tasks SET build_id = ?3\n" +
            "WHERE project_id = ?1 AND id = ?2", nativeQuery = true)
    void updateBuildId(long projectId, long taskId, @Nullable String buildID);

    @Query(value = "/* findUserRecentBuildTasks */\n" +
        "SELECT * FROM build_tasks WHERE email LIKE ?1\n" +
        "ORDER BY created_at DESC, id DESC LIMIT ?2", nativeQuery = true)
    List<BuildTaskEntity> findUserRecentBuildTasks(String emailPatter, int limit);
}
