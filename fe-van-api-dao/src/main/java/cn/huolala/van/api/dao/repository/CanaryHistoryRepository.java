package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.CanaryHistoryEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface CanaryHistoryRepository extends JpaRepository<CanaryHistoryEntity, Long> {
    List<CanaryHistoryEntity> findByCreatorIdAndCreatedAtAfter(Long creatorId, LocalDateTime createdAt);

    @Query(value = "/* CanaryHistoryRepository.countCanaryEachTypes */ \n" +
            "SELECT p.type, count(1) " +
            "FROM canary_histories h, projects p " +
            "WHERE h.project_id = p.id " +
            "GROUP BY p.type", nativeQuery = true)
    List<Integer[]> countCanaryEachTypes();

    @Query(value = "/* CanaryHistoryRepository.countCanaryEachTypesLikeCanary */ \n" +
            "SELECT p.type, count(1) " +
            "FROM canary_histories h, projects p " +
            "WHERE h.project_id = p.id AND h.canary LIKE '%canary%' " +
            "GROUP BY p.type", nativeQuery = true)
    List<Integer[]> countCanaryEachTypesLikeCanary();

    int countByCreatorId(Long creatorId);

    @Deprecated
    Optional<CanaryHistoryEntity> findFirstByProjectIdOrderByCreatedAtDescIdDesc(long projectId);

    @Query(value = "/* CanaryHistoryRepository.findLatestCanaryGroupByRegion */\n" +
            "SELECT t0.*\n" +
            "FROM canary_histories t0\n" +
            "JOIN (\n" +
            "  SELECT MAX(id) AS id\n" +
            "  FROM canary_histories t3\n" +
            "  JOIN (\n" +
            "    SELECT change_level AS region, project_id, MAX(created_at) AS created_at\n" +
            "    FROM canary_histories WHERE project_id = ?1 GROUP BY change_level\n" +
            "  ) t2\n" +
            "  ON t3.change_level = t2.region AND t3.created_at = t2.created_at AND t3.project_id = t2.project_id\n" +
            "  GROUP BY t3.change_level\n" +
            ") t1 ON t0.id = t1.id", nativeQuery = true)
    List<CanaryHistoryEntity> findLatestCanaryGroupByRegion(long projectId);

    @Query(value = "/* CanaryHistoryRepository.findActiveTaskIds */\n" +
            "SELECT h1.task_id_list\n" +
            "FROM canary_histories h1, (\n" +
            "  SELECT h3.project_id, MAX(h3.id) AS id\n" +
            "  FROM canary_histories h3, (\n" +
            "    SELECT project_id, MAX(created_at) AS created_at\n" +
            "    FROM canary_histories\n" +
            "    GROUP BY project_id\n" +
            "  ) h4\n" +
            "  WHERE h4.project_id = h3.project_id AND h4.created_at = h3.created_at\n" +
            "  GROUP BY h3.project_id\n" +
            ") h2\n" +
            "WHERE h1.id = h2.id AND h1.task_id_list IS NOT NULL AND h1.task_id_list != ''\n", nativeQuery = true)
    List<String> findActiveTaskIds();

    @Query(value = "SELECT * FROM canary_histories ORDER BY created_at DESC, id DESC LIMIT 1", nativeQuery = true)
    Optional<CanaryHistoryEntity> getLastModifiedRecord();

    @Query(value = "/* CanaryHistoryRepository.getHead */\n" +
            "SELECT * FROM canary_histories WHERE project_id = ?1 AND change_level IN ?2\n" +
            "ORDER BY created_at DESC, id DESC LIMIT 1", nativeQuery = true)
    Optional<CanaryHistoryEntity> getHead(long projectId, Collection<String> regions);

    @Query(value = "/* CanaryHistoryRepository.getHeadId */\n" +
            "SELECT id FROM canary_histories WHERE project_id = ?1 AND change_level IN ?2\n" +
            "ORDER BY created_at DESC, id DESC LIMIT 1", nativeQuery = true)
    Optional<Long> getHeadId(long projectId, Collection<String> regions);

    @Modifying
    @Query(value = "/* CanaryHistoryRepository.insert */\n" +
            "INSERT INTO canary_histories (\n" +
            "  project_id,\n" +
            "  creator_id,\n" +
            "  change_level,\n" +
            "  canary,\n" +
            "  task_id_list,\n" +
            "  message\n" +
            ") VALUES (?, ?, ?, ?, ?, ?)\n", nativeQuery = true)
    void insert(long projectId,
                long creatorId,
                @NonNull String region,
                @NonNull String canary,
                @NonNull String taskIds,
                @NonNull String message);


    @Query(value = "SELECT * FROM canary_histories WHERE id = ?", nativeQuery = true)
    Optional<CanaryHistoryEntity> get(long id);

    @NonNull
    @Query(value = "SELECT id FROM canary_histories WHERE id IN ?1 AND project_id != ?2", nativeQuery = true)
    Long[] findOutOfProjectId(@NonNull Set<Long> ids, long projectId);

    @Query(value = "/* CanaryHistoryRepository.wasReleased */\n" +
            "SELECT COUNT(1) FROM canary_histories\n" +
            "WHERE project_id = ?1 AND FIND_IN_SET(task_id_list, ?2) LIMIT 1", nativeQuery = true)
    int wasReleased(long projectId, long taskId);

    @NonNull
    @Query(value = "/* CanaryHistoryRepository.findByProjectIdAndTaskId */\n" +
            "SELECT c.* FROM build_tasks t, canary_histories c\n" +
            "WHERE t.id IN ?2 AND c.project_id = ?1\n" +
            "AND FIND_IN_SET(t.id, c.task_id_list)\n" +
            "ORDER BY c.created_at DESC", nativeQuery = true)
    List<CanaryHistoryEntity> findByProjectIdAndTaskId(long projectId, Set<Long> taskIds);

    Optional<CanaryHistoryEntity> findByIdAndProjectId(long id, long projectId);
}
