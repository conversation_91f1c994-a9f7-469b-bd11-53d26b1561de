package cn.huolala.van.api.dao.repository;

import cn.huolala.van.api.dao.entity.ProjectUserEntity;
import cn.huolala.van.api.dao.enums.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ProjectUserRepository extends JpaRepository<ProjectUserEntity, Long> {
    @Transactional
    long deleteByProjectIdAndUserId(Long projectId, Long userId);

    @Query(value = "/* ProjectUserRepository.getByProjectAndUserForUpdate */ \n" +
            "SELECT * FROM project_users " +
            "WHERE project_id = :projectId AND user_id = :userId " +
            "FOR UPDATE",
            nativeQuery = true)
    Optional<ProjectUserEntity> getByProjectAndUserForUpdate(@Param("projectId") Long projectId,
                                                             @Param("userId") Long userId);

    @Query(value = "/* ProjectUserRepository.findByProjectsAndUser */ \n" +
            "SELECT * FROM project_users " +
            "WHERE project_id IN :ids AND user_id = :userId AND role > 0",
            nativeQuery = true)
    List<ProjectUserEntity> findByProjectsAndUser(@Param("ids") Set<Long> ids, @Param("userId") Long userId);

    @Query(value = "/* ProjectUserRepository.filterUserAccessibleProjectIds */ \n" +
            "SELECT project_id FROM project_users " +
            "WHERE project_id IN :ids AND user_id = :userId AND role > 0",
            nativeQuery = true)
    Set<Long> filterUserAccessibleProjectIds(@Param("ids") Collection<Long> ids, @Param("userId") Long userId);

    List<ProjectUserEntity> findByProjectIdInOrderByRoleDesc(Set<Long> ids);

    List<ProjectUserEntity> findByProjectIdOrderByRoleDesc(Long id);

    List<ProjectUserEntity> findByProjectIdInAndRole(Set<Long> ids, Role role);

    @Query(value = "SELECT role FROM project_users WHERE user_id = ?1 AND project_id = ?2", nativeQuery = true)
    @Nullable
    Integer getRole(@NonNull Long userId, @NonNull Long projectId);

    @Modifying
    @Query(value = "/* ProjectUserRepository.insertOrUpdate#roleOnly */ \n" +
            "INSERT INTO project_users (uniq_str, project_id, user_id, role) " +
            "VALUES (:#{#userId+':'+#projectId}, :projectId, :userId, :role) " +
            "ON DUPLICATE KEY UPDATE role = VALUES(role)",
            nativeQuery = true)
    void insertOrUpdate(
            @Param("projectId") Long projectId,
            @Param("userId") Long userId,
            @Param("role") int role
    );
}
