<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.huolala</groupId>
        <artifactId>fe-van-api</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>fe-van-api-dao</artifactId>


    <dependencies>
        <dependency>
            <groupId>cn.lalaframework</groupId>
            <artifactId>lala-orm-mybatis</artifactId>
        </dependency>
      <dependency>
        <groupId>cn.huolala</groupId>
        <artifactId>fe-van-api-facade</artifactId>
        <version>1.0.1-SNAPSHOT</version>
      </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.huolala</groupId>
            <artifactId>fe-van-api-constants</artifactId>
            <version>${project.version}</version>
        </dependency>
      <dependency>
        <groupId>javax.validation</groupId>
        <artifactId>validation-api</artifactId>
        <version>2.0.1.Final</version>
      </dependency>

    </dependencies>
</project>
