<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.lalaframework.boot</groupId>
        <artifactId>lala-boot-parent</artifactId>
        <version>2.10.5.RELEASE</version>
    </parent>

    <groupId>cn.huolala</groupId>
    <artifactId>fe-van-api</artifactId>
    <packaging>pom</packaging>
    <version>1.0.1-SNAPSHOT</version>

    <properties>
        <jacoco.version>0.8.4</jacoco.version>
        <surefire.version>2.19.1</surefire.version>
        <guava.shade.version>28.2-jre</guava.shade.version>
    </properties>

    <modules>
        <module>fe-van-api-dao</module>
        <module>fe-van-api-facade</module>
        <module>fe-van-api-service</module>
        <module>fe-van-api-provider</module>
        <module>fe-van-api-test</module>
        <module>fe-van-api-constants</module>
        <module>fe-van-api-xl</module>
        <module>fe-van-api-hll</module>
    </modules>

    <distributionManagement>
        <repository>
            <id>fe-releases</id>
            <url>http://maven.huolala.cn/repository/fe-releases/</url>
        </repository>
        <snapshotRepository>
            <id>fe-snapshots</id>
            <url>http://maven.huolala.cn/repository/fe-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>7.16.2</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
