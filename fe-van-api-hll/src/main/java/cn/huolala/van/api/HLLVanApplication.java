package cn.huolala.van.api;

import cn.lalaframework.launcher.LalaApplication;
import cn.lalaframework.utils.ConfigUtil;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.TimeZone;

@EnableAsync
@SpringBootApplication
public class HLLVanApplication {

    public static void main(String[] args) {
        if ("3".equals(System.getProperty("hll.region"))) {
            TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        }
        LalaApplication.run(ConfigUtil.getAppId(), HLLVanApplication.class, args);
    }
}
