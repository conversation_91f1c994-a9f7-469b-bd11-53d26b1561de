#如有组件方面的使用疑问请参考 https://wiki.huolala.work/pages/viewpage.action?pageId=24701874
#在本地调试需要配置DEV环境apollo地址信息
#STG|PRE|PRD环境发布lalaplat2.0启动应用服务会自动注入信息，研发无须配置
apollo:
  bootstrap:
    namespaces: application

#配置mybatis
#mybatis:
#  config-location: classpath:mybatis/mybatis-config.xml  #配置加载mybatis配置文件路径
#  mapperLocations: classpath:mybatis/mapper/*.xml  #配置加载mybatis映射器路径
#  type-aliases-package: cn.huolala.van.api.entity  #配置加载别名实体类的包路径

#配置应用端口
server:
  port: 8082
  compression:
    enabled: true
    mime-types: application/json

spring:
  datasource:
    #dynamic config start
    dynamic:
      primary: van
      datasource:
        van:
          driver-class-name: com.mysql.jdbc.Driver
          #type: com.zaxxer.hikari.HikariDataSource
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none

lala:
  soa:
    consumer:
      enable: true
      scan-base-packages:
        - cn.huolala.jsonrpc.demo2.facade #配置扫描的service包路径,可以多个，逗号分隔
      scan-package-classes: cn.lalaweb.tools.facade.services.ErrorService #支持具体类的扫描，可以多个，逗号分隔即可
    provider:
      scan-base-packages: cn.huolala.van.api.provider
  #    application: #指定提供方{appId}的host和port，以方便在dev环境直连服务提供者进行调试
  #      jsonrpc-provider-svc:
  #        app-host: 127.0.0.1 #配置jsonrpc-provider-svc对应的host
  #        app-port: 8080    #配置jsonrpc-provider-svc对应的port
  cache: #jaf ehcache config start
    ehcache:
      directory: demo-cache #当persistent=true时，需要持久化到磁盘时的路径
      cache-template:
        default: #默认cache命名空间
          heap: 128 #推内分配的内存大小，单位M
          offheap: 512 #推外分配的内存大小，单位M
          disk: 2048 #如果持久化，持久化到磁盘的大小，单位M
          persistent: false #当persistent=true时，需要持久化到磁盘，需要设置disk的大小
          tti: 7200 #缓存淘汰机制
  redis:
    multiple:
      sources: van-workers
      redis:
        van-workers:
          #ci-resource-id: redis.fe-van-workers-svc
          value-serializer: JacksonJsonRedisSerializer
          dynamic-refresh-sources: false
  redisson:
    enabled: true
    #ci-resource-id: redis.fe-van-workers-svc
    timeout: 1000
    dynamic-refresh-sources: false

hll:
  sso:
    client:
      app-id: 100097
