import { jkl, createState } from 'https://static.huolala.cn/npm/jinkela@2.0.0-beta2/dist/index.esm.js';

const state = createState({ loading: 0 });

const update = async () => {
    state.loading = 1;

    const token = url.value;

    try {
        if (token) {
            location.hash = token;
        } else {
            location.hash = '';
            throw new Error('The token of wiki node must not be empty');
        }

        const res = await fetch(`../../api/feishu/wiki/node/${token}?info=meta`, { credentials: 'include' });

        if (!res.ok) {
            const { message } = await res.json();
            throw new Error(message);
        }

        const nodeInfo = await res.json();
        state.error = null;
        state.nodeInfo = nodeInfo;
        state.sheets = [];
        state.loading = 2;

        if (nodeInfo && nodeInfo.objType === 'sheet') {
            const res1 = await fetch(`../../api/feishu/sheet/${nodeInfo.objToken}?info=sheets`, { credentials: 'include' });
            state.sheets = await res1.json();
        } else {
            state.sheets = [];
        }
    } catch (error) {
        state.error = error;
        state.nodeInfo = null;
        state.sheets = [];
    }

    state.loading = 0;
};

const submit = (e) => {
    e.preventDefault();
    update();
};

const FEISHU = 'https://huolala.feishu.cn';

addEventListener('load', async () => {
    const el = jkl`
        <form @submit="${submit}">
            <strong>${FEISHU}/wiki/</strong>
            <input id="url" value="" autocomplete="off" />
            <button>${() => state.loading ? '···' : 'Submit'}</button>
        </form>
        <div>
        ${() => {
            const { nodeInfo, error, sheets = [] } = state;
            if (error) {
                return jkl`<p style="color: red;">${error.message}</p>`;
            }
            if (!nodeInfo) return null;
            const { title, objType, objToken } = nodeInfo;
            const url = `${FEISHU}/${objType}/${objToken}`;
            return jkl`
                <ul class="info">
                    <li><strong>Title: </strong> <var>${title}</var></li>
                    <li><strong>Type: </strong> <var>${objType}</var></li>
                    <li><strong>Token: </strong> <var>${objToken}</var></li>
                    <li>
                        <strong>URL: </strong>
                        <a href="${url}" target="_blank">${url}</a>
                    </li>
                </ul>
                <ul class="sheets">${sheets.map(i => {
                        return jkl`
                            <li>
                                <div>
                                    <strong>Title: </strong>
                                    <var>${i.title}</var>
                                </div>
                                <div>
                                    <strong>SheetID: </strong>
                                    <var>${i.sheetId}</var>
                                </div>
                                <div>
                                    <strong>URL: </strong>
                                    <a href="${url}?sheetId=${i.sheetId}" target="_blank">${url}?sheetId=${i.sheetId}</a>
                                </div>
                                <div>
                                    <strong>API: </strong>
                                    <a href="../../api/feishu/sheet/${objToken}/${i.sheetId}" target="_blank">../../api/feishu/sheet/${objToken}/${i.sheetId}</a>
                                </div>
                            </li>
                        `;
                    })}</ul>
            `;
        }}
        </div>
    `;
    document.body.appendChild(el);

    if (location.hash) {
        url.value = location.hash.replace(/^#/, '');
        update();
    }
});
