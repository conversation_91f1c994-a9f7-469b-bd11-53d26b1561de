<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.huolala</groupId>
        <artifactId>fe-van-api</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>
    <artifactId>fe-van-api-test</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.huolala</groupId>
            <artifactId>fe-van-api-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <configuration>
                    <!--重要！用追加的模式-->
                    <append>true</append>
                    <dataFile>target/jacoco.exec</dataFile>
                    <outputDirectory>target/jacoco-ut</outputDirectory>
                    <destFile>target/jacoco.exec</destFile>
                </configuration>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>jacocoArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${surefire.version}</version>
                <configuration>
                    <!--打印单元测试的详情-->
                    <printSummary>true</printSummary>
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <forkCount>3</forkCount>
                    <reuseForks>true</reuseForks>
                    <argLine>-Xmx1024m ${jacocoArgLine}</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
