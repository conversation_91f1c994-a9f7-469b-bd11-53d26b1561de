package cn.huolala.van.api;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 测试服务基类 这个无须启动mvc的web容器
 **/
@Ignore
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = SpringServiceTestApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class BaseTest {
    @BeforeClass
    public static void before() {
        System.setProperty("hll.app.id", "demo");
    }
}
