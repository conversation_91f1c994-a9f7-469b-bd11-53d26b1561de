#在本地调试需要配置DEV环境apollo地址信息
#STG|PRE|PRD环境发布lalaplat2.0启动应用服务会自动注入信息，研发无须配置
app:
  id: fe-van-api-test

apollo:
  #apollo中 项目app id下application,consul配置文件 
  #新建的项目 在apollo中无该配置 需进行申请
  bootstrap:
    namespaces: application

server:
  servlet:
    application-display-name: ${spring.application.name}
  port: 8888

logging:
  level:
    root: info
    cn.huolala.bme: info

mybatis:
  config-location: classpath:mybatis/mybatis-config.xml
  mapperLocations: classpath:mybatis/mapper/*.xml
  type-aliases-package: cn.huolala.van.api.entity

spring:
  datasource:
    #dynamic config start
    dynamic:
      primary: db1
      datasource:
        db1:
          ci-resource-id: mysql.lala-archetype-demo.master #db资源ID，在lalaplat中申请
          driver-class-name: com.mysql.jdbc.Driver
          #type: com.zaxxer.hikari.HikariDataSource
        db2:
          ci-resource-id: mysql.lala-archetype-demo.slave1
          driver-class-name: com.mysql.jdbc.Driver
          #type: com.zaxxer.hikari.HikariDataSource
          #dynamic config stop
  kafka:

    #lala spring multiple kafka config start
    multiple: #配置多源生产者和消费者
      producer:
        sources: producer1
        producer1:
          ci-resource-id: kafka.ci_demo_test
          value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      consumer:
        sources: consumer1,consumer2
        consumer1:
          ci-resource-id: kafka.ci_demo_test
          group-id: jaf_test_group_consumer1
          topic: ci_demo_test
          concurrency: 5  # 消费者数量，一个poll线程代表一个消费者
          value-serializer: org.springframework.kafka.support.serializer.JsonDeserializer
        consumer2:
          ci-resource-id: kafka.ci_demo_test
          group-id: jaf_test_group_consumer2
          topic: ci_demo_test
          concurrency: 5  # 消费者数量，一个poll线程代表一个消费者
          value-serializer: org.springframework.kafka.support.serializer.JsonDeserializer
    #lala spring multiple kafka config stop

  rabbitmq:
    #lala spring multiple rabbitmq config start
    multiple: # 多源配置
      queue:
        demo_test: ci_jaf_demo_queue
      enabled: true
      rabbitmq:
        rabbitmq1:
          ci-resource-id: rabbitmq.ci_jaf_test_demo
        rabbitmq2:
          ci-resource-id: rabbitmq.ci_jaf_test_demo
    #lala spring multiple rabbitmq config stop

  data:
    elasticsearch: #标准配置
      #jaf multiple elasticsearch config start
      multiple: #多源配置
        sources: multi
        client:
          multi:
            reactive:
              ci-resource-id: elasticsearch.jaf_demo
              connection-timeout: 5000
              socket-timeout: 3000
              #jaf multiple elasticsearch config stop

lala:
  #jaf soa config start
  soa:
    consumer:
      enable: true
      scan-base-packages: cn.huolala.van.api.facade.service,cn.huolala.jsonrpc.demo2.facade #配置扫描的service包路径,可以多个，逗号分隔
  #    application: #指定提供方{appId}的host和port，以方便在dev环境直连服务提供者进行调试
  #      jsonrpc-provider-svc:
  #        app-host: 127.0.0.1 #配置jsonrpc-provider-svc对应的host
  #        app-port: 8080    #配置jsonrpc-provider-svc对应的port
  #jaf soa config stop

  cache: #jaf ehcache config start
    ehcache:
      directory: demo-cache #当persistent=true时，需要持久化到磁盘时的路径
      cache-template:
        default: #默认cache命名空间
          heap: 128 #推内分配的内存大小，单位M
          offheap: 512 #推外分配的内存大小，单位M
          disk: 2048 #如果持久化，持久化到磁盘的大小，单位M
          persistent: false #当persistent=true时，需要持久化到磁盘，需要设置disk的大小
          tti: 7200 #缓存淘汰机制

  #在ehcache中，缓存有2个失效相关的配置即 timeToLiveSeconds和timeToIdleSeconds，分别简称为ttl和tti。 在通常的解释中，前者表示一条缓存自创建时间起多少秒后失效，而后者表示一条缓存自最后读取或更新起多少秒失效。
  #在2个同时配置时可能时间计算就不那么简单了。 简单说来 任何一方为0，则以另一方时间为准。否则就以最短时间为准。
  #ehcache是这样计算失效时间的

  #1 如果ttl不为0并且tti为0， 如果缓存未被读过，失效时间=ttl
  #2 如果tti不为0，失效时间=tti+读取时间
  #3 否则 失效时间=min(ttl, tti+读取时间)
  # jaf ehcache config stop
  redis:

    #jaf spring multiple redis config start
    multiple:
      enabled: true #非必须
      #timeout: 5000 #command超时时间
      sources: redis1,redis2
      redis:
        redis1:
          ci-resource-id: redis.lala_jaf_public_cluster  #redis资源ID，在lalaplat中申请
          value-serializer: JacksonJsonRedisSerializer
          #timeout: 5000 #command超时时间
        redis2:
          ci-resource-id: redis.lala_jaf_public_cluster  #redis资源ID，在lalaplat中申请
          value-serializer: JacksonJsonRedisSerializer
          #timeout: 5000 #command超时时间
          #jaf spring multiple redis config stop
