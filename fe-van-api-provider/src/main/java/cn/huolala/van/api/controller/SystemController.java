package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.model.LastModifiedModel;
import cn.huolala.api.constants.model.WatchDogAllConfigView;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.model.system.*;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.VanHook;
import cn.huolala.van.api.service.SystemService;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.service.VanConfigService;
import cn.huolala.van.api.util.UserUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.util.Optional;

@RestController
@RequestMapping("/api/system")
@Validated
public class SystemController {
    @Autowired
    private VanConfigService vanConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemService systemService;

    @GetMapping(params = "info=globalHooks")
    @Nullable
    public VanHook[] getGlobalHooks() {
        return systemService.getGlobalHooks();
    }

    @PutMapping(params = "action=updateGlobalHooks")
    @RequiredSuperAdmin
    public void updateGlobalHooks(@RequestBody VanHook[] hooks) {
        systemService.updateGlobalHooks(hooks);
    }

    @GetMapping(params = "info=allWatchDogConfig")
    @ApiOperation("NOTE: This method may return a null, that indicates the value is not modified.")
    public Optional<WatchDogAllConfigView> getAllWatchDogConfig(@RequestParam Env env,
                                                                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime ifModifiedSince) {
        WatchDogAllConfigView view = vanConfigService.getWatchDogAllConfigView(env);
        switch (LastModifiedModel.compare(view, ifModifiedSince)) {
            case Null:
            case After:
                return Optional.of(view);
            default:
                return Optional.empty();
        }
    }

    @GetMapping(params = "info=config")
    public SystemConfig getConfig() {
        UserModel user = UserUtils.getCurrentUser();
        SystemUser cuser = new SystemUser(user);
        cuser.setSuperAdmin(userService.isSuperAdmin(cuser.getUniqId()));
        SystemConfig config = systemService.getConfig();
        config.setCurrentUser(cuser);
        return config;
    }
}
