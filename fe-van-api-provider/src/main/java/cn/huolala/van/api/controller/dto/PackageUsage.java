package cn.huolala.van.api.controller.dto;

import cn.huolala.van.api.dao.entity.ProjectDependencyEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

@Getter
@AllArgsConstructor
public class PackageUsage {
    @ApiModelProperty("The projectId associated with taskId")
    @NonNull
    private final Long projectId;

    @ApiModelProperty("The taskId of the last used build task in a project")
    @NonNull
    private final Long taskId;

    @ApiModelProperty("The package version of last used in a project")
    @NonNull
    private final String version;

    @ApiModelProperty("The record creation time")
    @NonNull
    private final OffsetDateTime createdAt;

    @Nullable
    public static PackageUsage create(@Nullable Long projectId, @Nullable ProjectDependencyEntity pde) {
        if (projectId == null || pde == null) return null;
        return new PackageUsage(
                projectId, pde.getTaskId(), pde.getVersion(),
                pde.getCreatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime()
        );
    }
}
