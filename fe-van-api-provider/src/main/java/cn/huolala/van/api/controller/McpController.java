package cn.huolala.van.api.controller;

import cn.huolala.van.api.annotation.NoAuthentication;
import cn.huolala.van.api.controller.dto.JsonRpcError;
import cn.huolala.van.api.controller.dto.JsonRpcRequest;
import cn.huolala.van.api.controller.dto.JsonRpcResponse;
import cn.huolala.van.api.exception.BizException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.mcp.MyMcpImplementation;
import cn.lalaframework.tools.util.StringUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.victools.jsonschema.generator.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/mcp")
public class McpController {
    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private MyMcpImplementation myMcpService;

    private SchemaGenerator schemaGenerator;

    private static String getErrorMessage(Throwable e) {
        if (e instanceof InvocationTargetException) {
            Throwable te = ((InvocationTargetException) e).getTargetException();
            if (te instanceof BizException) {
                return ((BizException) te).getMsg();
            } else {
                return getErrorMessage(te);
            }
        } else {
            if (StringUtil.isBlank(e.getMessage()) && e.getCause() != null) {
                return getErrorMessage(e.getCause());
            }
            return e.getMessage();
        }
    }

    @PostConstruct
    private void init() {
        SchemaGeneratorConfig config = new SchemaGeneratorConfigBuilder(mapper, OptionPreset.PLAIN_JSON).without(
            Option.SCHEMA_VERSION_INDICATOR).build();
        schemaGenerator = new SchemaGenerator(config);
    }

    private ToolsListResult toolsList() {
        List<ToolsListResult.Tool> list = Arrays.stream(myMcpService.getClass().getDeclaredMethods()).map(m -> {
            ApiOperation ao = m.getAnnotation(ApiOperation.class);
            if (ao == null) return null;
            LinkedHashMap<String, Object> schema = new LinkedHashMap<>();
            schema.put("type", "object");
            LinkedHashMap<String, Object> properties = new LinkedHashMap<>();
            schema.put("properties", properties);
            List<String> required = new ArrayList<>();
            schema.put("required", required);
            int pc = m.getParameterCount();
            Parameter[] ps = m.getParameters();
            Type[] gps = m.getGenericParameterTypes();
            for (int i = 0; i < pc; i++) {
                Parameter parameter = ps[i];
                ApiParam ap = parameter.getAnnotation(ApiParam.class);
                NonNull nn = parameter.getAnnotation(NonNull.class);
                if (ap != null && ap.required() || nn != null || parameter.getType().isPrimitive())
                    required.add(parameter.getName());
                Type type = gps[i];
                ObjectNode pSchema = schemaGenerator.generateSchema(type);
                if (ap != null) pSchema.put("description", ap.value());
                properties.put(parameter.getName(), pSchema);
            }
            return new ToolsListResult.Tool(m.getName(), ao.value(), schema);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return new ToolsListResult(list);
    }

    private McpCallRes toolsCall(String name, JsonNode arguments) {
        Method method = Arrays.stream(myMcpService.getClass().getDeclaredMethods())
            .filter(v -> v.getName().equals(name)).findFirst()
            .orElseThrow(() -> new RuntimeException("Tool '" + name + "' is not found"));
        Object[] parameters = Arrays.stream(method.getParameters())
            .map(p -> mapper.convertValue(arguments.get(p.getName()), p.getType())).toArray();
        List<McpCallRes.Node> content = new ArrayList<>();
        try {
            Object result = method.invoke(myMcpService, parameters);
            if (result instanceof String) {
                content.add(new McpCallRes.Node((String) result));
            } else {
                content.add(new McpCallRes.Node(mapper.writeValueAsString(result)));
            }
            return new McpCallRes(content, false);
        } catch (Exception e) {
            content.add(new McpCallRes.Node(getErrorMessage(e)));
        }
        return new McpCallRes(content, true);
    }

    Object handleJsonRpcCall(String name, JsonNode params) {
        switch (name) {
            case "tools/list":
                return toolsList();
            case "tools/call":
                return toolsCall(params.get("name").asText(), params.get("arguments"));
            default:
                throw new VanBadRequestException("Method " + name + " is not supported");
        }
    }

    @NoAuthentication
    @PostMapping(consumes = "application/json")
    public JsonRpcResponse rpc(@RequestBody JsonRpcRequest req) {
        String method = req.getMethod();
        JsonNode params = req.getParams();
        Object id = req.getId();
        try {
            Object result = handleJsonRpcCall(method, params);
            return new JsonRpcResponse(result, null, id);
        } catch (Exception e) {
            return new JsonRpcResponse(null, new JsonRpcError(-1, getErrorMessage(e), null), id);
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class ToolsListResult {
        private List<Tool> tools;

        @Getter
        @Setter
        @AllArgsConstructor
        public static final class Tool {
            private String name;
            private String description;
            private Object inputSchema;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class McpCallRes {
        private List<Node> content;
        private Boolean isError;

        @Getter
        @Setter
        public static class Node {
            private final String type;
            private final String text;

            public Node(String text) {
                this.type = "text";
                this.text = text;
            }
        }
    }

}
