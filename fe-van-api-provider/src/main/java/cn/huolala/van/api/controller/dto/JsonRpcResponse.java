package cn.huolala.van.api.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class JsonRpcResponse {
    @NonNull
    private final String jsonrpc;
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private final Object result;
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private final JsonRpcError error;
    @NonNull
    private final Object id;

    public JsonRpcResponse(@Nullable Object result, @Nullable JsonRpcError error, @NonNull Object id) {
        this.jsonrpc = "2.0";
        this.result = result;
        this.error = error;
        this.id = id;
    }
}
