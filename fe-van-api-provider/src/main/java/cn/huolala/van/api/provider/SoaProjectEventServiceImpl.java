package cn.huolala.van.api.provider;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.facade.service.SoaProjectEventService;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.events.ProjectEventContent;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.EventService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@HermesService("/van/events")
@Service
public class SoaProjectEventServiceImpl implements SoaProjectEventService {
    @Autowired
    private EventService eventService;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ObjectMapper mapper;

    @Override
    public void add(String userUniqName, String projectName, Object meta) {
        UserModel user = userService.pullUserFromSsoIfNeeded(userUniqName);
        if (user == null) throw new ResourceNotFoundException("Bad userUniq " + userUniqName);
        Long id = projectService.getIdByName(projectName);
        ProjectModel project = projectService.getNeverNull(id);
        ProjectEventContent content = mapper.convertValue(meta, ProjectEventContent.Unknown.class);
        eventService.add(user, project, content);
    }
}
