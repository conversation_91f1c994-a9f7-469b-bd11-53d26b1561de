package cn.huolala.van.api.controller.unsafe.dto;

import cn.huolala.api.constants.enums.Env;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Data
public class PageServerAuthRequest {
    private String project;
    private String path;
    private Map<String, String> headers;
    private Env env;
    private String query;

    public String getPath() {
        if (StringUtils.isEmpty(path)) {
            return "";
        }
        if (path.startsWith("/")) {
            return path;
        }
        return "/" + path;
    }

    public String getQuery() {
        if (StringUtils.isEmpty(query)) {
            return "";
        }
        if (query.startsWith("?")) {
            return query;
        }
        return "?" + query;
    }

    public String getToken() {
        if (headers == null) {
            return null;
        }
        String cookies = headers.get("cookie");
        if (StringUtils.isEmpty(cookies)) {
            return null;
        }

        if (env == null) {
            return null;
        }
        String[] cookieArray = cookies.split(";");
        for (String cookie : cookieArray) {
            cookie = cookie.trim();
            String prefix = "_hll_identifier";
            switch (env) {
                case stg:
                    prefix = "_hll_identifier_stg";
                    break;
                case pre:
                    prefix = "_hll_identifier_pre_1";
                    break;
                case prd:
                    prefix = "_hll_identifier";
                    break;
            }
            if (cookie.startsWith(prefix + "=")) {
                int i = cookie.indexOf("=");
                String value = cookie.substring(i + 1).trim();
                try {
                    return URLDecoder.decode(value, StandardCharsets.UTF_8.name());
                } catch (UnsupportedEncodingException e) {
                    return value;
                }
            }
        }
        return null;
    }
}
