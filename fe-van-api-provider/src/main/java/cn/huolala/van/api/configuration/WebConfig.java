package cn.huolala.van.api.configuration;

import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.van.api.util.EnumUtils;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.interceptor.SpringWebAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.TimeUnit;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private GlobalExceptionHandler globalExceptionHandler;

    @Autowired
    private SpringWebAuthInterceptor springWebAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(springWebAuthInterceptor).addPathPatterns("/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/feishu/tools/**")
                .addResourceLocations("classpath:/tools/")
                .setCacheControl(CacheControl.maxAge(0, TimeUnit.SECONDS));
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new ProjectType.SpringConverter());
        registry.addConverter(String.class, BuildTaskType.class, x -> EnumUtils.fromString(BuildTaskType.class, x));
        registry.addConverter(String.class, UserLogType.class, x -> EnumUtils.fromString(UserLogType.class, x));
    }
}
