package cn.huolala.van.api.util;

import org.springframework.lang.NonNull;

import java.util.List;
import java.util.function.Consumer;

/**
 * Collect items from anywhere and handle them in batches later in a worker thread.
 */
public class DeferHandler<T> extends <PERSON><PERSON><PERSON>andler<T> {
    @NonNull
    public static final BatchHandler<Runnable> pool = new BatchHandler<>(list -> list.forEach(Runnable::run));

    DeferHandler(@NonNull Consumer<List<T>> handler) {
        super(handler);
    }

    @NonNull
    @Override
    protected List<T> initList() {
        pool.add(this::digest);
        return super.initList();
    }
}
