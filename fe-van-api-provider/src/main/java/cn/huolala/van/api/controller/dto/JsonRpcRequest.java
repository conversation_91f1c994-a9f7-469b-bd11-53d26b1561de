package cn.huolala.van.api.controller.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class JsonRpcRequest {
    @Nullable
    private final String jsonrpc;
    @NonNull
    private final String method;
    @NonNull
    private final JsonNode params;
    @NonNull
    private final Object id;

    @JsonCreator
    public JsonRpcRequest(@JsonProperty("jsonrpc") @Nullable String jsonrpc,
                          @JsonProperty("method") @NonNull String method,
                          @JsonProperty("params") @NonNull JsonNode params,
                          @JsonProperty("id") @NonNull Object id) {
        this.jsonrpc = jsonrpc;
        this.method = method;
        this.params = params;
        this.id = id;
    }
}
