package cn.huolala.van.api.provider;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.api.constants.enums.WindowApprovalStatus;
import cn.huolala.api.constants.model.ActiveTaskIds;
import cn.huolala.api.constants.model.LastModifiedModel;
import cn.huolala.api.constants.model.WatchDogAllConfigView;
import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.dao.entity.FeishuSubscriptionEntity;
import cn.huolala.van.api.dao.entity.OneTimeWindowEntity;
import cn.huolala.van.api.dao.entity.ProjectUserEntity;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.dao.repository.CanaryHistoryRepository;
import cn.huolala.van.api.dao.repository.FeishuSubscriptionRepository;
import cn.huolala.van.api.dao.repository.OneTimeWindowRepository;
import cn.huolala.van.api.dao.repository.ProjectUserRepository;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.facade.model.BuildTaskDTO;
import cn.huolala.van.api.facade.model.CanaryHistoryDTO;
import cn.huolala.van.api.facade.model.DeployDevParams;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.facade.model.LegacyDeployConfigDTO;
import cn.huolala.van.api.facade.model.MetaDTO;
import cn.huolala.van.api.facade.model.ProjectConfigDTO;
import cn.huolala.van.api.facade.model.ProjectDomainInfoDTO;
import cn.huolala.van.api.facade.model.ProjectIntroDTO;
import cn.huolala.van.api.facade.model.ProjectQueryDTO;
import cn.huolala.van.api.facade.model.TaskIntroDTO;
import cn.huolala.van.api.facade.model.UpdateWindowParams;
import cn.huolala.van.api.facade.model.UserInfoDTO;
import cn.huolala.van.api.facade.model.UserRoleInfoDTO;
import cn.huolala.van.api.facade.model.WindowParams;
import cn.huolala.van.api.facade.model.LegacyDeployConfigDTO.Operator;
import cn.huolala.van.api.facade.model.LegacyDeployConfigDTO.Rule;
import cn.huolala.van.api.facade.model.ProjectIntroDTO.ProjectConfig;
import cn.huolala.van.api.facade.model.WindowParams.WindowExtra;
import cn.huolala.van.api.facade.model.ProjectQueryDTO.DevDeploy;
import cn.huolala.van.api.facade.model.ProjectQueryDTO.DevDeployDTO;
import cn.huolala.van.api.facade.model.ProjectQueryDTO.ProdDeployDTO;
import cn.huolala.van.api.facade.model.SubscribeGroupParams;
import cn.huolala.van.api.facade.model.enums.ProjectCmdbReginEnum;
import cn.huolala.van.api.facade.model.enums.VanUserRoleEnum;
import cn.huolala.van.api.facade.service.MiscService;
import cn.huolala.van.api.model.ConfigEnv;
import cn.huolala.van.api.model.DeployParams;
import cn.huolala.van.api.model.HistorySearchForCanary;
import cn.huolala.van.api.model.ImportantProjectView;
import cn.huolala.van.api.model.ImportantProjectsParams;
import cn.huolala.van.api.model.ProjectAndTaskModel;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.VanProjectConfig;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.roles.ProjectUserMeta;
import cn.huolala.van.api.model.roles.UserRoleModel;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.AuditLogUtil;
import cn.huolala.van.api.util.TaskUtils;
import cn.lalaframework.job.sdk.HllXxlJobManager;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import cn.lalaframework.logging.LoggerFactory;

import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;

import static cn.huolala.api.constants.enums.UserLogType.FEISHU_DEPLOY_DEV;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@HermesService("/van/misc")
@Service
public class MiscServiceImpl implements MiscService {
    private static final Logger LOGGER = LoggerFactory.getLogger();

    private List<ProjectDomainInfoDTO> projectDomainCache;

    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private MonitorRecordService monitorRecordService;
    @Autowired
    private VanConfigService vanConfigService;
    @Autowired
    private MetaService metaService;
    @Autowired
    private DeployService deployService;
    @Autowired
    private UserLogService userLogService;
    @Autowired
    private OneTimeWindowRepository oneTimeWindowRepository;
    @Autowired
    private ProjectUserRepository projectUserRepository;
    @Autowired
    private FeishuSubscriptionRepository feishuSubscriptionRepository;
    @Autowired
    private CanaryHistoryRepository canaryHistoryRepository;
    @Autowired
    private ObjectMapper mapper;

    @Override
    @NonNull
    public TaskIntroDTO getTaskIntro(String projectName, Long taskId) {
        Long projectId = projectService.getIdByName(projectName);
        ProjectAndTaskModel both = buildTaskService.getBoth(projectId, taskId);

        TaskIntroDTO dto = new TaskIntroDTO();
        dto.setProjectId(projectId);
        dto.setRepository(both.getProject().getRepository());
        dto.setTaskOwner(both.getTask().getUserUniqId());
        dto.setCommitMessage(both.getTask().getCommitMessage());
        dto.setCommitHash(both.getTask().getHash());
        dto.setProjectMembers(
                userService.findProjectUsers(projectId).map(i -> {
                    TaskIntroDTO.Member m = new TaskIntroDTO.Member();
                    m.setAccount(i.getUserUniqId());
                    m.setRole(i.getRole().ordinal());
                    return m;
                }).collect(Collectors.toList()));
        return dto;
    }

    @Override
    @NonNull
    public List<String> getImportantProjects() {
        return monitorRecordService
                .getImportantProjects(new ImportantProjectsParams()).stream()
                .map(ImportantProjectView::getName)
                .collect(Collectors.toList());
    }

    @NonNull
    @Override
    public ProjectIntroDTO getProjectIntro(@NonNull Long projectId) {
        ProjectIntroDTO dto = new ProjectIntroDTO();
        ProjectModel project = projectService.getNeverNull(projectId);
        dto.setId(project.getId());
        dto.setType(project.getType());
        dto.setName(project.getName());
        dto.setDescription(project.getDescription());
        dto.setCreatedAt(project.getCreatedAt());
        dto.setActivatedAt(project.getActivatedAt());
        dto.setRepository(project.getRepository());
        dto.setAppId(project.getConfig().getAppId());
        ProjectConfig projectConfig = new ProjectConfig();
        projectConfig.setAppId(project.getConfig().getAppId());
        projectConfig.setDevDomain(project.getConfig().getDevDomain());
        dto.setConfig(projectConfig);
        return dto;
    }

    @NonNull
    @Override
    public ProjectIntroDTO getProjectIntroByName(@NonNull String projectName) {
        Long projectId = projectService.getIdByName(projectName);
        return this.getProjectIntro(projectId);
    }

    @NonNull
    @Override
    public List<UserRoleInfoDTO> getProjectRoles(@NonNull Long projectId) {
        List<UserRoleModel> members = userService.findRoles(projectId);
        List<UserRoleInfoDTO> result = members.stream().map(member -> {
            UserRoleInfoDTO u = new UserRoleInfoDTO();
            u.setUniqId(member.getUserUniqId());
            u.setEmail(member.getUserUniqId() + "@huolala.cn");
            u.setName(member.getUserName());
            u.setRole(VanUserRoleEnum.fromBitValue(member.getRole().bitValue));
            if (member.getRegion() != null) {
                u.setRegion(ProjectCmdbReginEnum.fromGolangEnumStr(member.getRegion().golangEnumStr));
            }
            return u;
        }).collect(Collectors.toList());
        return result;
    }

    @NonNull
    @Override
    public List<UserRoleInfoDTO> getProjectRolesByName(@NonNull String projectName) {
        Long projectId = projectService.getIdByName(projectName);
        return this.getProjectRoles(projectId);
    }

    @Override
    public ProjectConfigDTO getProjectConfig(Long projectId) {
        VanProjectConfig vanConfig = projectService.getVanProjectConfig(Region.defaultRegion(), projectId);
        ProjectConfigDTO projectConfigDTO = new ProjectConfigDTO();
        BeanUtils.copyProperties(vanConfig, projectConfigDTO);
        return projectConfigDTO;
    }

    @Override
    public ProjectConfigDTO getProjectConfigByName(String projectName) {
        Long projectId = projectService.getIdByName(projectName);
        return this.getProjectConfig(projectId);
    }

    @NonNull
    @Override
    public Map<String, Long> getAllProjects() {
        return projectService.findAllProjects();
    }

    @NonNull
    @Override
    public Boolean getOffwebStatus(@NonNull Long projectId) {
        return projectService.getVanProjectConfig(Region.defaultRegion(), projectId).isOffWeb();
    }

    @Override
    @Nullable
    public WatchDogAllConfigView getAllWatchDogConfig(@NonNull Env env,
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @Nullable OffsetDateTime ifModifiedSince) {
        WatchDogAllConfigView view = vanConfigService.getWatchDogAllConfigView(env);
        switch (LastModifiedModel.compare(view, ifModifiedSince)) {
            case Null:
            case After:
                return view;
            default:
                return null;
        }
    }

    @Override
    @Nullable
    public ActiveTaskIds findActiveTaskIds(
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @Nullable OffsetDateTime ifModifiedSince) {
        ActiveTaskIds atis = historyService.findActiveTaskIds();
        switch (LastModifiedModel.compare(atis, ifModifiedSince)) {
            case Null:
            case After:
                return atis;
            default:
                return null;
        }
    }

    private UserInfoDTO userModelToDTO(UserModel user) {
        if (user == null) return null;
        UserInfoDTO u = new UserInfoDTO();
        u.setId(user.getId());
        u.setUniqId(user.getUniqId());
        u.setName(user.getName());
        u.setEmail(user.getEmail());
        return u;
    }

    private void setDevDeploy(ProjectIntroDTO project, DevDeployDTO dto, Env env) {
        DeployRecord deployRecord = historyService.getLatestDeploy(project.getId(), env);
        if (deployRecord == null) return;
        if (deployRecord.getTaskId() != null) {
            DevDeploy devDeploy = new DevDeploy();
            BuildTaskDTO task = TaskUtils.buildTaskModelToDTO(
                buildTaskService.getNeverNull(project.getId(), deployRecord.getTaskId()));
            UserModel user = userService.getByUniqId(deployRecord.getUserUniqId());
            UserInfoDTO userDto = userModelToDTO(user);
            devDeploy.setTask(task);
            devDeploy.setUser(userDto);
            devDeploy.setCreatedAt(deployRecord.getCreatedAt());
            if (env.equals(Env.stg)) {
                dto.setStg(devDeploy);
            }
            if (env.equals(Env.pre)) {
                dto.setPre(devDeploy);
            }
        }
    }

    @Nullable
    @Override
    public ProjectQueryDTO getProjectQueryInfo(@NonNull String idOrName) {
        ProjectIntroDTO project;
        try {
            if (idOrName.matches("\\d+")) {
                try {
                    project = getProjectIntro(Long.parseLong(idOrName));
                } catch (NumberFormatException e) {
                    throw new VanBadRequestException("invalid id, " + idOrName);
                }
            } else {
                project = getProjectIntroByName(idOrName);
            }
        } catch (VanBadRequestException e) {
            // ignore not found exception
            if (e.getMessage().contains("not found")) {
                return null;
            }
            throw new VanBadRequestException(e);
        }

        ProjectQueryDTO projectQueryDTO = new ProjectQueryDTO(project);

        projectQueryDTO.setProjectUsers(getProjectRoles(project.getId()));

        ProdDeployDTO prodDeployDTO = new ProdDeployDTO();

        ProjectConfigDTO projectConfigDTO = getProjectConfigByName(project.getName());
        prodDeployDTO.setDomains(projectConfigDTO.getAllowedHosts());

        LegacyDeployConfig legacyDeployConfig = Region.defaultStorage()
                .getOptionalValue(String.format("%s/%s.json", project.getName(), Env.prd.getLongName()),
                        LegacyDeployConfig.class)
                .orElse(null);

        if (legacyDeployConfig != null) {
            List<BuildTaskDTO> list = buildTaskService.batchGet(project.getId(), legacyDeployConfig.collectTaskIds())
                    .map(TaskUtils::buildTaskModelToDTO)
                    .collect(Collectors.toList());
            prodDeployDTO.setOssTask(list);
        }

        List<CanaryRecord> records = historyService.searchCanaryHistory(project.getId(), new HistorySearchForCanary());
        if (!records.isEmpty()) {
            CanaryRecord cr = records.get(0);

            List<BuildTaskDTO> list = buildTaskService.batchGet(project.getId(), cr.collectTaskIds())
                    .map(TaskUtils::buildTaskModelToDTO)
                    .collect(Collectors.toList());

            prodDeployDTO.setDbTask(list);
            prodDeployDTO.setDbUser(userModelToDTO(userService.getByUniqId(cr.getUserUniqId())));
            prodDeployDTO.setCreatedAt(cr.getCreatedAt());
        }

        projectQueryDTO.setProdDeploy(prodDeployDTO);

        DevDeployDTO devDeployDTO = new DevDeployDTO();
        setDevDeploy(project, devDeployDTO, Env.stg);
        setDevDeploy(project, devDeployDTO, Env.pre);
        projectQueryDTO.setDevDeploy(devDeployDTO);

        return projectQueryDTO;
    }

    @Nullable
    @Override
    public UserInfoDTO getUserByUniqId(@NonNull String uniqId) {
        return userModelToDTO(userService.getByUniqId(uniqId));
    }

    @Override
    public Long addWindow(@NonNull WindowParams params) {
        if (params.getStartAt() == 0 || params.getEndAt() == 0) {
            throw new VanBadRequestException("need duration");
        }
        ProjectModel project = projectService.getNeverNull(IdOrName.create(params.getProjectName()));
        UserModel user = userService.getById(params.getUserId());
        if (user == null) {
            throw new InternalRequestException("no user found");
        }

        // AppIDWhiteListWindowPriority = 50
        // GlobalWindowPriority = 100
        // ProjectApprovalWindowPriority = 150
        WindowExtra windowExtra = new WindowExtra(150, false, params.getCode(), false, params.getRemark());
        String extra = "";
        try {
            extra = mapper.writeValueAsString(windowExtra);
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
        LocalDateTime start = Instant.ofEpochSecond(params.getStartAt()).atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime end = Instant.ofEpochSecond(params.getEndAt()).atZone(ZoneId.systemDefault()).toLocalDateTime();

        OneTimeWindowEntity entity = new OneTimeWindowEntity(
            project.getId(),
            user.getId(),
            WindowApprovalStatus.WindowApprovalPending,
            extra,
            start,
            end
        );
        return oneTimeWindowRepository.save(entity).getId();
    }

    private boolean findByCode(OneTimeWindowEntity entity, String code) {
        if (entity.getExtra() == null || entity.getExtra().isEmpty()) {
            return false;
        }
        try {
            WindowExtra extra = mapper.readValue(entity.getExtra(), WindowExtra.class);
            return extra.getCode().equals(code);
        } catch (IOException e) {
            // handle item we want, ignore other exceptions
            LOGGER.error("WindowExtra parse error, ", e);
        }
        return false;
    }

    @Override
    public void updateWindow(@NonNull UpdateWindowParams params) {
        if (params.getStartAt() == 0 || params.getEndAt() == 0) {
            throw new VanBadRequestException("need duration");
        }
        ProjectIntroDTO project = getProjectIntroByName(params.getProjectName());
        LocalDateTime epoch = LocalDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());
        LocalDateTime start = Instant.ofEpochSecond(params.getStartAt()).atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        LocalDateTime end = Instant.ofEpochSecond(params.getEndAt()).atZone(ZoneId.systemDefault()).toLocalDateTime();
        // find all pending, then update to the status we want
        List<OneTimeWindowEntity> list = oneTimeWindowRepository.listOneTimeWindow(project.getId(), epoch, start, end,
                WindowApprovalStatus.WindowApprovalPending.ordinal());
        // find item
        list.stream()
                .filter(o -> findByCode(o, params.getCode()))
                .findFirst()
                .ifPresent(entity -> {
                    oneTimeWindowRepository.updateOneTimeWindowStatus(
                            entity.getId(),
                            entity.getProjectId(),
                            params.getStatus().ordinal());
                });

    }

    private String trimPrefix(String str, String prefix) {
        if (str.startsWith(prefix)) {
            return str.substring(prefix.length());
        }
        return str;
    }
    private String findAndTrim(String str, String c) {
        int index = str.indexOf(c);
        if (index != -1) {
            return str.substring(index);
        }
        return str;
    }

    @Override
    public void setDomainForProject(@NonNull String projectName, @NonNull String domain) {
        ProjectModel project = projectService.getNeverNull(IdOrName.create(projectName));
        String predomain = domain.trim().toLowerCase();
        if (predomain.isEmpty()) {
            // use default
            predomain = project.getName() + "-v." + project.getConfig().getDevDomain();
        } else {
            predomain = trimPrefix(predomain, "https://");
            predomain = trimPrefix(predomain, "http://");
            predomain = findAndTrim(predomain, "/");
            predomain = findAndTrim(predomain, ":");
        }

        VanProjectConfig vanProjectConfig = projectService
                .getVanProjectConfig(Region.defaultRegion(), project.getId());
        if (vanProjectConfig.getAllowedHosts() != null && vanProjectConfig.getAllowedHosts().contains(predomain)) {
            // already exits, skip
            return;
        }
        // not exits, update allowedHosts
        List<String> newAllowedHosts = Optional.ofNullable(vanProjectConfig.getAllowedHosts())
                .orElse(new ArrayList<>());
        newAllowedHosts.add(predomain);
        // NOTE: isInternational = true, but devDomain != lalamove.com
        Stream<Region> stream = Stream.of(Region.cn);
        if (vanProjectConfig.isInternational()) {
            stream = Arrays.stream(Region.values());
        }
        // update all regions
        stream.parallel().forEach(r -> {
            LOGGER.info("update VanProjectConfig in region {}", r);
            projectService.partialUpdateVanProjectConfig(project.getId(), r, c -> {
                c.setAllowedHosts(newAllowedHosts);
            });
        });
    }

    @Override
    public void subscribeGroup(@NonNull SubscribeGroupParams params) {
        UserModel user = userService.getById(params.getUserId());
        ProjectModel project = projectService.getNeverNull(IdOrName.create(params.getProjectName()));

        if (user == null) {
            throw new InternalRequestException("no user found");
        }

        ProjectUserEntity entity = projectUserRepository
                .getByProjectAndUserForUpdate(project.getId(), user.getId())
                .orElse(null);
        if (entity == null) {
            throw new InternalRequestException("no role found");
        }
        List<Role> roles = new ArrayList<>(Collections.singletonList(entity.getRole()));
        ProjectUserMeta projectUserMeta = ProjectUserMeta.createFromJson(entity.getMeta());
        projectUserMeta.getRegionInfo().forEach((k, v) -> {
            roles.addAll(v.getRoles().stream().collect(Collectors.toList()));
        });
        if (roles.contains(Role.NoRole)) {
            throw new InternalRequestException("user has no permission with this project");
        }
        // create feishu subscription
        FeishuSubscriptionEntity feishuSubscriptionEntity = new FeishuSubscriptionEntity(
                project.getId(),
                user.getId(),
                params.getChatId());
        feishuSubscriptionRepository.save(feishuSubscriptionEntity);

    }

    @Override
    public void unsubscribeGroup(@NonNull SubscribeGroupParams params) {
        UserModel user = userService.getById(params.getUserId());
        ProjectModel project = projectService.getNeverNull(IdOrName.create(params.getProjectName()));

        if (user == null) {
            throw new InternalRequestException("no user found");
        }

        ProjectUserEntity entity = projectUserRepository
                .getByProjectAndUserForUpdate(project.getId(), user.getId())
                .orElse(null);
        if (entity == null) {
            throw new InternalRequestException("no role found");
        }
        List<Role> roles = new ArrayList<>(Collections.singletonList(entity.getRole()));
        ProjectUserMeta projectUserMeta = ProjectUserMeta.createFromJson(entity.getMeta());
        projectUserMeta.getRegionInfo().forEach((k, v) -> {
            roles.addAll(v.getRoles().stream().collect(Collectors.toList()));
        });
        if (roles.contains(Role.NoRole)) {
            throw new InternalRequestException("user has no permission with this project");
        }

        List<FeishuSubscriptionEntity> list = feishuSubscriptionRepository.findByProjectId(project.getId());
        list.stream()
                .filter(l -> l.getChatId().equals(params.getChatId()))
                .forEach(v -> feishuSubscriptionRepository.deleteById(v.getId()));
    }

    // NOTE: a job interface
    @Override
    @HllXxlJob(value = "refreshProjectDomainCache")
    public void refreshProjectDomainCache() {
        HllXxlJobManager.log("start to refresh projectDomainCache");
        projectDomainCache = projectService.findAllProjects().values()
                .parallelStream()
                .map(id -> {
                    try {
                        String projectName = projectService.getNameById(id);
                        VanProjectConfig vanProjectConfig = projectService
                                .getVanProjectConfig(Region.defaultRegion(), id);

                        List<String> allowedHosts = Optional.ofNullable(vanProjectConfig.getAllowedHosts())
                                .filter(l -> !l.isEmpty())
                                .orElse(null);
                        if (allowedHosts != null) {
                            return new ProjectDomainInfoDTO(
                                        projectName,
                                        vanProjectConfig.getAppid(),
                                        vanProjectConfig.getAllowedHosts()
                                    );
                        }
                    } catch (Exception e) {
                        // dont fail the job
                        HllXxlJobManager.log(
                                String.format("failed to handle project %d, error %s", id, e.getMessage()));
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        LOGGER.info("refreshProjectDomainCache {}", projectDomainCache);
    }

    @Override
    public List<ProjectDomainInfoDTO> listAllProjectDomain() {
        // just return the cache
        return projectDomainCache;
    }

    @Override
    public MetaDTO getMeta(@NonNull long metaId, @NonNull int type) {
        MetaType metaType = MetaType.createFromInt(type);
        return metaService.get(metaId, metaType).map(m -> {
            MetaDTO metaDTO = new MetaDTO();
            metaDTO.setId(m.getId());
            metaDTO.setMetaId(m.getMetaId());
            metaDTO.setType(m.getType());
            metaDTO.setMeta(m.getMeta());
            metaDTO.setCreatedAt(m.getCreatedAt());
            metaDTO.setUpdatedAt(m.getUpdatedAt());
            return metaDTO;
        }).orElse(null);
    }

    @Override
    public void updateMeta(@NonNull long metaId, @NonNull int type, @NonNull String meta) {
        if (meta.length() > 2000) {
            throw new VanBadRequestException("meta length should be less than 2000");
        }
        MetaType metaType = MetaType.createFromInt(type);
        metaService.partialUpdate(metaType, metaId, m -> {
            m.setMeta(meta);
        });
    }

    @Override
    public void deployDev(DeployDevParams params) {
        ProjectModel project = projectService.getNeverNull(params.getProjectId());
        UserModel user = userService.getByUniqId(params.getUniqId());
        if (user == null) {
            throw new InternalRequestException("no user found");
        }
        // like (stg stable-1)
        ConfigEnv ce = ConfigEnv.from(params.getEnv(), params.getStableEnv());

        DeployRecord dr = deployService.deployForDev(new DeployParams(user, project, ce, params.getTaskId()));

        AuditLogUtil.addAuditLogForDeployDev(dr, user.getId());

        if (params.getFrom() != null && Objects.equals(params.getFrom(), "feishu")) {
            // add user log
            Map<String, String> meta = new HashMap<>();
            meta.put("task_id", String.valueOf(params.getTaskId()));
            meta.put("env", params.getEnv());
            userLogService.addLog(user.getId(), project.getId(), FEISHU_DEPLOY_DEV, "", meta);
        }
    }

    @Override
    public CanaryHistoryDTO getProjectCanaryHistory(long projectId, long canaryId) {
        return canaryHistoryRepository.findByIdAndProjectId(canaryId, projectId).map(e -> {
            UserModel user = userService.getById(e.getCreatorId());
            CanaryHistoryDTO c = new CanaryHistoryDTO();
            c.setId(e.getId());
            c.setCanary(e.getCanary());
            c.setProjectId(e.getProjectId());
            c.setChangeLevel(e.getRegion());
            c.setMessage(e.getMessage());
            c.setTaskIdList(e.getTaskIdList());
            c.setCreator(userModelToDTO(user));
            c.setCreatedAt(e.getCreatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime());
            c.setUpdatedAt(e.getUpdatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime());
            return c;
        }).orElse(null);
    }

    @Override
    public LegacyDeployConfigDTO getProjectCanary(long projectId, String region) {
        return deployService.getCurrentLegacyReleased(projectId, Region.valueOf(region))
                .filter(v -> v.getDefaultTaskId() != null)
                .map(v -> {
                    LegacyDeployConfigDTO l = new LegacyDeployConfigDTO(v.getDefaultTaskId());
                    if (v.getCanary() != null) {
                        List<Rule> rules = v.getCanary().stream()
                                .map(r -> {
                                    Rule rule = new Rule();
                                    rule.setTaskId(r.getTaskId());
                                    rule.setCanaryId(r.getCanaryId());
                                    rule.setDescription(r.getDescription());

                                    List<Operator> operators = r.getOperatorChain().stream()
                                            .map(op -> {
                                                Operator o = new Operator();
                                                o.setLeft(op.getLeft());
                                                o.setType(op.getType());
                                                o.setRight(op.getRight());
                                                o.setOp(op.getOp());
                                                return o;
                                            }).collect(Collectors.toList());

                                    rule.setOperatorChain(operators);
                                    return rule;

                                }).collect(Collectors.toList());
                        l.setCanary(rules);
                    }
                    return l;
                }).orElse(null);
    }

    @Override
    public boolean isSuperAdmin(String uniqId) {
        return userService.isSuperAdmin(uniqId);
    }

}
