package cn.huolala.van.api.annotation;

import cn.huolala.van.api.configuration.validator.RequireRoleValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = RequireRoleValidator.class)
public @interface RequireRole {
    String message() default "You are not participating in this project";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
