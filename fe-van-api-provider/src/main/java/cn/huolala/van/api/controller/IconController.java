package cn.huolala.van.api.controller;

import cn.huolala.van.api.service.GitlabService;
import cn.huolala.van.api.service.IconService;
import cn.lalaframework.logging.LoggerFactory;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import javax.servlet.http.HttpServletResponse;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Controller
@RequestMapping("/api/icon")
@Validated
public class IconController {

    private static final Logger LOGGER = LoggerFactory.getLogger();

    @Autowired
    private IconService iconService;
    @Autowired
    private GitlabService gitlabService;

    @GetMapping
    public void getIcon(
        @RequestParam("projectId") long projectId,
        Long taskId,
        HttpServletResponse res
    ) throws ExecutionException {
        iconService.getIcon(projectId, taskId).accept(res);
    }

    @PostMapping(params = "action=clean")
    @ResponseBody
    public List<String> cleanCache() {
        return iconService.cleanCache();
    }

    @GetMapping(value = "/boilerplate/{id}")
    public ResponseEntity<String> getBoilerplateLogo(@PathVariable long id, @RequestParam String ref) {
        String logoRef = StringUtils.hasText(ref) ? ref : "master";
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "image/svg+xml");
        // let's set one hour
        headers.add("Cache-Control", "max-age=3600");
        byte[] logoFile = gitlabService.getRepositoryLogoIcon(String.valueOf(id), "logo.svg", ref);
        if (logoFile == null) {
            return new ResponseEntity<>("", headers, HttpStatus.OK);
        }
        return new ResponseEntity<>(new String(logoFile, StandardCharsets.UTF_8), headers, HttpStatus.OK);
    }
}
