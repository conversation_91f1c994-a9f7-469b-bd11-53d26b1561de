package cn.huolala.van.api.controller.dto;

import com.lark.oapi.service.contact.v3.model.AvatarInfo;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.util.Optional;

@Getter
public class FeishuUserSummary {
    @Nullable
    private final String uniqId;

    @Nullable
    private final String name;

    @Nullable
    private final String openId;

    /**
     * @deprecated Use avatar72 instead.
     */
    @Nullable
    @Deprecated
    private final AvatarInfo avatar;

    @Nullable
    private final String avatar72;

    public FeishuUserSummary(User user) {
        // The enName may not be empty for LLM users !!!
        uniqId = Optional.of(user.getEnName()).orElseGet(user::getName);
        name = user.getName();
        openId = user.getOpenId();
        avatar = user.getAvatar();
        avatar72 = user.getAvatar().getAvatar72();
    }
}
