package cn.huolala.van.api.provider;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.facade.model.gitlab.GitlabBranch;
import cn.huolala.van.api.facade.model.gitlab.GitlabCompare;
import cn.huolala.van.api.facade.model.gitlab.GitlabProject;
import cn.huolala.van.api.facade.service.OpenGitlabService;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.GitlabService;
import cn.huolala.van.api.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@HermesService(value = "/van/gitlab")
@Service
public class OpenGitlabServiceImpl implements OpenGitlabService {
    @Autowired
    private GitlabService gitlab;
    @Autowired
    private ProjectService projectService;

    @NonNull
    @Override
    public GitlabProject getProjectInfo(@NonNull IdOrName projectIdOrName) {
        ProjectModel project = projectService.getNeverNull(projectIdOrName);
        return gitlab.getProjectInfo(project.getRepository());
    }

    @Nullable
    @Override
    public GitlabBranch getBranchInfo(@NonNull IdOrName projectIdOrName, @NonNull String branchName) {
        ProjectModel project = projectService.getNeverNull(projectIdOrName);
        return gitlab.getBranchInfo(project.getRepository(), branchName);
    }

    @NonNull
    @Override
    public GitlabCompare compare(@NonNull IdOrName projectIdOrName,
                                 @NonNull String sourceBranch,
                                 @NonNull String targetBranch) {
        ProjectModel project = projectService.getNeverNull(projectIdOrName);
        return gitlab.compare(project.getRepository(), sourceBranch, targetBranch);
    }
}
