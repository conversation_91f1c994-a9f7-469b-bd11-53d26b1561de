package cn.huolala.van.api.interceptor;

import cn.huolala.van.api.annotation.NoAuthentication;
import cn.huolala.van.api.configuration.VanAuthenticator;
import cn.huolala.van.api.exception.ForbiddenException;
import cn.huolala.van.api.exception.UnauthorizedException;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.DeferHandler;
import cn.huolala.van.api.util.UserUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 权限校验的拦截器
 * 1. 先 auth
 * 2. 校验 controller 是否需要 SuperAdmin
 * 3. 校验项目的权限
 */
@Component
public class SpringWebAuthInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private VanAuthenticator vanAuthenticator;
    @Autowired
    private UserService userService;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request,
                             @NotNull HttpServletResponse response,
                             @NotNull Object handler) {

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Class<?> clazz = handlerMethod.getBeanType();
        Package pkg = clazz.getPackage();
        String packageName = pkg.getName();

        // 只处理 cn.huolala.van.api.controller 下的接口
        // 由于 interceptor 无法针对指定包添加，所以只能在这里处理，添加这个拦截器时要添加 /**，否则会拦截不到
        if (packageName.startsWith("cn.huolala.van.api.controller.unsafe")) {
            return true;
        }

        if (!packageName.startsWith("cn.huolala.van.api.controller")) {
            return true;
        }

        UserModel user = vanAuthenticator.authenticate(request);

        UserUtils.setCurrentUser(user);

        NoAuthentication noAuthentication = handlerMethod.getMethodAnnotation(NoAuthentication.class);
        if (user == null && noAuthentication == null) throw new UnauthorizedException();

        RequiredSuperAdmin annotation = handlerMethod.getMethodAnnotation(RequiredSuperAdmin.class);
        if (annotation != null) {
            if (user == null) throw new UnauthorizedException();
            if (!userService.isSuperAdmin(user.getUniqId())) {
                throw new ForbiddenException("Requires super admin privileges");
            }
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler,
                           @Nullable ModelAndView modelAndView) throws Exception {
        DeferHandler.pool.digest();
        super.postHandle(request, response, handler, modelAndView);
    }
}
