package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.controller.dto.MiniprogramDeployRequestBody;
import cn.huolala.van.api.controller.dto.ProdReleaseParams;
import cn.huolala.van.api.controller.dto.ReleasableTasksDTO;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.CanaryRule;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.UserUtils;
import groovy.lang.Tuple2;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.huolala.api.constants.enums.UserLogType.DEPLOY_PROD;
import static cn.huolala.van.api.util.AuditLogUtil.*;
import static cn.huolala.van.api.util.MocNotificationUtil.notifyMocForRelease;
import static java.util.concurrent.CompletableFuture.supplyAsync;

@RestController
@RequestMapping("/api/project/{projectId}/deploy")
@Validated
public class DeployController {
    @Autowired
    private HistoryService historyService;
    @Autowired
    private DeployService deployService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private MiniprogramService miniprogramService;
    @Autowired
    private BuildTaskService buildtaskService;

    @GetMapping(params = "info=status")
    public List<ConfigEnvTask> getLatestTaskIdForEachEnv(@RequireRole @PathVariable long projectId) {
        return historyService.getLatestTaskIdForEachEnv(projectId);
    }

    @GetMapping(params = "info=canary")
    public CanaryRecord getLatestCanary(@RequireRole @PathVariable long projectId) {
        return historyService.getLatestCanary(projectId);
    }

    @Deprecated
    @GetMapping(params = "info=getPreviousOf")
    public Optional<Tuple2<Long, String>> getPreviousOf(@RequireRole @PathVariable long projectId,
                                                        @RequestParam long canaryId) {
        return historyService.getPrevious(canaryId).map(cr -> new Tuple2<>(cr.getId(), cr.getRegion().name()));
    }

    @GetMapping(params = "info=getPreviousIdMap")
    public Map<Long, Long> getPreviousIdMap(@RequireRole @PathVariable long projectId,
                                            @RequestParam Set<Long> canaryIds) {
        return historyService.getPreviousIdMap(projectId, canaryIds);
    }

    @GetMapping(params = "info=heads")
    public List<CanaryRecord> getLatestCanaryForEachRegion(@RequireRole @PathVariable long projectId) {
        return historyService.getLatestCanaryForEachRegion(projectId);
    }

    @PostMapping(params = "info=canaryHistory")
    public List<CanaryRecord> searchCanaryHistory(@RequireRole @PathVariable long projectId,
                                                  @ModelAttribute HistorySearchForCanary params) {
        return historyService.searchCanaryHistory(projectId, params);
    }

    @PostMapping(params = "info=deployHistory")
    public List<DeployRecord> searchDeployHistory(@RequireRole @PathVariable long projectId,
                                                  @ModelAttribute HistorySearchForDeploy params) {
        return historyService.searchDeployHistory(projectId, params);
    }

    @Deprecated
    @ApiOperation("Use NpmController.searchHistory instead.")
    @PostMapping(params = "info=npmHistory")
    public List<List<NpmPublishRecord>> searchNpmHistory(@RequestBody List<SearchNpmPublishHistoryParams> queries) {
        return queries.stream().parallel().map(historyService::searchNpmPublishHistory).collect(Collectors.toList());
    }

    @GetMapping(params = "info=releasableTasks")
    public ReleasableTasksDTO findReleasableTasks(@RequireRole @PathVariable long projectId) {
        // Find the last 100 rows of deploy history and group them by taskId into a map.
        HistorySearchForDeploy params = new HistorySearchForDeploy();
        params.setPage(0);
        params.setSize(100);

        params.setBranchPrefix(new HashSet<>(Arrays.asList("release", "global/release")));
        Map<Long, List<DeployRecord>> deployMap = historyService.searchDeployHistory(projectId, params)
                .stream().filter(i -> i.getTaskId() != null)
                .collect(Collectors.groupingBy(DeployRecord::getTaskId, Collectors.toList()));

        List<BuildTaskModel> tasks = buildtaskService.batchGet(projectId, deployMap.keySet())
                .sorted(Comparator.comparing(BuildTaskModel::getCreatedAt).reversed())
                .collect(Collectors.toList());

        Set<Long> taskIds = tasks.stream().map(BuildTaskModel::getId).collect(Collectors.toSet());

        CompletableFuture<Map<Long, String>> fTagMap = supplyAsync(() -> buildtaskService.findTag(projectId, taskIds));

        // Find associated canary history and group by taskId into a map.
        Map<Long, Optional<CanaryRecord>> lastReleaseMap = historyService.findCanaryHistory(projectId, taskIds)
                // A canary record contains multiple task IDs.
                // Expand and copy them for each task ID.
                // Finally, group them into a map by task ID.
                .stream().flatMap(row -> row.getCanary().collectTaskIds().stream().map(id -> Pair.of(id, row)))
                .collect(Collectors.groupingBy(Pair::getKey,
                        // Find the newest of createdAt field only.
                        Collectors.mapping(
                                Pair::getValue,
                                Collectors.maxBy(Comparator.comparing(CanaryRecord::getCreatedAt))
                        )));

        return new ReleasableTasksDTO(tasks, fTagMap.join(), lastReleaseMap, deployMap);
    }

    @PostMapping(params = "action=deploy")
    @ApiOperation("Deploy for DEV environment")
    public void deployForDev(@RequireRole @PathVariable long projectId,
                             @RequestParam Long taskId,
                             @RequestParam Env env,
                             @RequestParam(required = false) String stableName) {
        ProjectModel project = projectService.getNeverNull(projectId);
        UserModel user = UserUtils.getCurrentUser();

        ConfigEnv ce = ConfigEnv.from(env, stableName);

        DeployRecord dr = deployService.deployForDev(new DeployParams(user, project, ce, taskId));

        addAuditLogForDeployDev(dr);
    }

    @PostMapping(params = "action=releaseV2")
    @ApiOperation("Deploy for PRD environment")
    public void releaseForProdV2(@RequireRole @PathVariable long projectId,
                                 @RequestParam Region region,
                                 @RequestBody ProdReleaseParams params) {
        LegacyDeployConfig config = params.getConfig();
        if (config == null) throw new VanBadRequestException("The config field is required in params");
        ProjectModel project = projectService.getNeverNull(projectId);
        UserModel user = UserUtils.getCurrentUser();
        PdmInfo pdmInfo = params.getPdmInfo();
        String message = params.getMessage();

        DeployParams deployParams = new DeployParams(user, project, region, config, message, pdmInfo);

        // TODO: The head point value should be provided from frontend to keep this API idempotent.
        CanaryRecord canaryRecord = deployService.releaseForProd(deployParams, null, null);

        addAuditLogForRelease(project, canaryRecord);
        notifyMocForRelease(project, canaryRecord);
    }

    @PostMapping(params = "action=rollbackTo")
    public void rollbackTo(@RequireRole @PathVariable long projectId,
                           @RequestParam long canaryId) {
        CompletableFuture<ProjectModel> cfProject = supplyAsync(() -> projectService.getNeverNull(projectId));

        CompletableFuture<Optional<Long>> cfCanaryInfo = supplyAsync(() -> historyService.getPreviousId(canaryId));

        CanaryRecord dst = historyService.getCanaryRecordNeverNull(canaryId);
        dst.assertProjectId(projectId);
        Region region = dst.getRegion();
        CanaryRecord src = historyService.getHeadNeverNull(projectId, region);

        String message = String.format("Rollback from (%s) to (%s)", src.toSummary(), dst.toSummary());

        long headPoint = src.getId();
        long backPoint = cfCanaryInfo.join().orElse(headPoint);

        UserModel user = UserUtils.getCurrentUser();
        ProjectModel project = cfProject.join();

        DeployParams deployParams = new DeployParams(user, project, region, dst.getCanary(), message, null);

        // TODO: The head point value should be provided from frontend to keep this API idempotent.
        CanaryRecord cr = deployService.releaseForProd(deployParams, backPoint, headPoint);

        addAuditLogForRelease(project, cr);
        notifyMocForRelease(project, cr);
    }

    @GetMapping(params = "info=release")
    @Nullable
    public Map<Region, Optional<CanaryRule[]>> getCurrentReleased(@RequireRole @PathVariable long projectId,
                                                                  @RequestParam List<Region> regions) {
        return regions.stream().distinct().parallel()
                .collect(Collectors.toMap(i -> i, i -> deployService.getCurrentReleased(projectId, i)));
    }

    @GetMapping(params = "info=legacyRelease")
    @Nullable
    public Map<Region, Optional<LegacyDeployConfig>> getCurrentLegacyReleased(@RequireRole @PathVariable long projectId,
                                                                              @RequestParam List<Region> regions) {
        return regions.stream().distinct().parallel()
                .collect(Collectors.toMap(i -> i, i -> deployService.getCurrentLegacyReleased(projectId, i)));
    }

    @PostMapping(params = "action=fixPageServerCanaryFile")
    @RequiredSuperAdmin
    public String fixPageServerCanaryFile(@PathVariable long projectId,
                                          @RequestParam Region region,
                                          @RequestParam(defaultValue = "false") boolean overwrite) {
        return deployService.fixPageServerCanaryFile(projectId, region, overwrite);
    }

    @Deprecated
    @PostMapping(params = "action=release")
    @ApiOperation("Deploy for PRD environment\n" +
            "NOTE: This method only writes PageServer canary files without notification and history record.\n" +
            "It's temporary used in UD migration program, and will be removed soon.")
    public void releaseForProd(@RequireRole @PathVariable long projectId,
                               @RequestParam Region region,
                               @RequestBody ProdReleaseParams params) {
        UserModel user = UserUtils.getCurrentUser();
        LegacyDeployConfig config = params.getConfig();
        String message = params.getMessage();

        ProjectModel project = projectService.getNeverNull(projectId);

        if (config == null) throw new VanBadRequestException("The config field is required in params");

        deployService.releaseForProd(user, project, region, config, message);

        addAuditLog(projectId, DEPLOY_PROD)
                .put("idc", region.name())
                .put("defaultTaskID", params.getConfig().getDefaultTaskId());
    }

    @PostMapping(params = "action=miniprogramDeploy")
    @ApiOperation("Miniprogram deploy(preview/upload)")
    public long deployMiniprogram(@RequireRole @PathVariable long projectId, @RequestParam long taskId, @RequestBody MiniprogramDeployRequestBody params) {
        UserModel user = UserUtils.getCurrentUser();
        return miniprogramService.deployMiniprogram(projectId, taskId, user.getId(), params.getType(), params.getRobot(), params.getVersion(), params.getDescription());
    }
}
