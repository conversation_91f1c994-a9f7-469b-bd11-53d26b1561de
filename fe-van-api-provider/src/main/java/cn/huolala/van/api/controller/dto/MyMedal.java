package cn.huolala.van.api.controller.dto;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.model.UserMedalModel;
import lombok.Getter;
import lombok.NonNull;

import java.time.OffsetDateTime;

@Getter
public class MyMedal {
    @NonNull
    private final MedalEnum type;
    @NonNull
    private final OffsetDateTime createdAt;

    public MyMedal(UserMedalModel model) {
        type = model.getUniqName();
        createdAt = model.getCreatedAt();
    }
}
