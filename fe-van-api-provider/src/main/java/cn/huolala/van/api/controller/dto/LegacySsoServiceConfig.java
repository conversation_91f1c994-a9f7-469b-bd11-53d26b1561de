package cn.huolala.van.api.controller.dto;

import cn.huolala.api.constants.enums.Env;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class LegacySsoServiceConfig {
    @JsonProperty(defaultValue = "false")
    private boolean enable;
    @JsonProperty(defaultValue = "true")
    private boolean ignoreError;

    public static String buildPath(String projectName, Env env) {
        String eName = env == Env.prd ? "prod" : env.name();
        return String.format("%s/config/sso-%s.json", projectName, eName);
    }
}
