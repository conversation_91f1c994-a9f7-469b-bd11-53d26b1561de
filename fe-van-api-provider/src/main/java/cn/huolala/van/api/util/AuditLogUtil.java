package cn.huolala.van.api.util;

import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.controller.dto.CanaryAuditLogMeta;
import cn.huolala.van.api.model.AuditLog;
import cn.huolala.van.api.model.Change;
import cn.huolala.van.api.model.ConfigEnv;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.UserLogService;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.google.common.collect.ImmutableMap;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.springframework.lang.NonNull;

import java.util.concurrent.CompletableFuture;

import static cn.huolala.api.constants.enums.UserLogType.*;
import static cn.huolala.api.constants.enums.ProjectType.Workers;

public class AuditLogUtil {
    @NonNull
    private static final DeferHandler<AuditLog> autoSubmitter = new DeferHandler<>(list -> {
        UserLogService userLogService = ApplicationContextUtil.getBean(UserLogService.class);
        CompletableFuture.runAsync(() -> userLogService.addLogs(list));
    });

    private AuditLogUtil() {
    }

    @NonNull
    @CanIgnoreReturnValue
    public static AuditLog addAuditLog(long projectId, @NonNull UserLogType type) {
        AuditLog log = new AuditLog(UserUtils.getCurrentUser().getId(), projectId, type, null, null);
        autoSubmitter.add(log);
        return log;
    }

    @NonNull
    @CanIgnoreReturnValue
    public static AuditLog addAuditLog(long projectId, long userId, @NonNull UserLogType type) {
        AuditLog log = new AuditLog(userId, projectId, type, null, null);
        autoSubmitter.add(log);
        return log;
    }

    @NonNull
    @CanIgnoreReturnValue
    public static AuditLog addAuditLog(long projectId, @NonNull UserLogType type, @NonNull Object meta) {
        AuditLog log = new AuditLog(UserUtils.getCurrentUser().getId(), projectId, type, null, meta);
        autoSubmitter.add(log);
        return log;
    }

    @NonNull
    @CanIgnoreReturnValue
    public static AuditLog addAuditLogForConfigChange(long projectId, @NonNull Change<?> change, @NonNull String type) {
        return addAuditLog(projectId, UserLogType.UPDATE_PROJECT_CONFIG)
                .put("type", type)
                .put("before", change.getBefore())
                .put("after", change.getAfter());
    }

    public static void addAuditLogForRelease(@NonNull ProjectModel project, @NonNull CanaryRecord canaryRecord) {
        long projectId = project.getId();

        CanaryAuditLogMeta logMeta = new CanaryAuditLogMeta(canaryRecord);

        // The normal deploy log.
        addAuditLog(projectId, DEPLOY_PROD, logMeta);

        // The workers deploy log.
        if (project.getType() == Workers) addAuditLog(projectId, WORKERS_RELEASE, logMeta);

        // The canary deploy log.
        if (canaryRecord.getCanary().hasCanary()) addAuditLog(projectId, CANARY_DEPLOY, logMeta);
    }

    public static void addAuditLogForDeployDev(@NonNull DeployRecord dr) {
        AuditLog log = addAuditLog(dr.getProjectId(), DEPLOY_DEV);
        ConfigEnv ce = dr.getEnv();
        if (ce != null) {
            log.put("env", ImmutableMap.of("name", ce.getName(), "stable_name", ce.getStableName()));
        }
        log.put("task_id", dr.getTaskId());
    }

    // NOTE: deployed by feishu, should inject userId
    public static void addAuditLogForDeployDev(@NonNull DeployRecord dr, @NonNull long userId) {
        AuditLog log = addAuditLog(dr.getProjectId(), userId, DEPLOY_DEV);
        ConfigEnv ce = dr.getEnv();
        if (ce != null) {
            log.put("env", ImmutableMap.of("name", ce.getName(), "stable_name", ce.getStableName()));
        }
        log.put("task_id", dr.getTaskId());
    }
}
