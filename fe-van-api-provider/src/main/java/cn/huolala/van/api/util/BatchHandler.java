package cn.huolala.van.api.util;

import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public class BatchHandler<T> {
    @NonNull
    private final ThreadLocal<List<T>> heap = new ThreadLocal<>();
    @NonNull
    private final Consumer<List<T>> handler;

    BatchHandler(@NonNull Consumer<List<T>> handler) {
        this.handler = handler;
    }

    @NonNull
    protected List<T> initList() {
        return new ArrayList<>();
    }

    public void digest() {
        final List<T> list = heap.get();
        if (list == null) return;
        heap.remove();
        if (list.isEmpty()) return;
        handler.accept(list);
    }

    public void add(@NonNull T item) {
        List<T> list = heap.get();
        if (list == null) {
            list = initList();
            heap.set(list);
        }
        list.add(item);
    }
}
