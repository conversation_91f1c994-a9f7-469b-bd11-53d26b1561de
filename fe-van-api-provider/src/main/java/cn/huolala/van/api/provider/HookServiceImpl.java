package cn.huolala.van.api.provider;

import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.dao.enums.MiniprogramDeployType;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.facade.model.HookDTO;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.facade.model.MiniprogramDeployHistoryDTO;
import cn.huolala.van.api.facade.model.MiniprogramInfoDTO;
import cn.huolala.van.api.facade.model.HookDTO.Timing;
import cn.huolala.van.api.facade.service.HookService;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.VanHook;
import cn.huolala.van.api.model.deploy.MiniprogramDeployHistoryRecord;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.FeishuUtils;
import cn.lalaframework.logging.LoggerFactory;
import cn.lalaframework.soa.exception.BusinessException;

import java.util.Arrays;
import java.util.Base64;
import java.util.Optional;
import java.util.stream.Collectors;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lark.oapi.service.im.v1.model.CreateImageRespBody;

@HermesService("/van/hook")
@Service
public class HookServiceImpl implements HookService {

    private static final Logger LOGGER = LoggerFactory.getLogger();

    @Autowired
    private MiniprogramService miniprogramService;
    @Autowired
    private MetaService metaService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private UserService userService;
    @Autowired
    private SystemService systemService;

    @Override
    public MiniprogramInfoDTO getMiniprogramInfo(Long projectId) {
        return metaService.getValue(MetaType.MiniprogramInfo, projectId, MiniprogramInfoDTO.class);
    }

    @Override
    public MiniprogramDeployHistoryDTO getMiniprogramInfoDeployHistory(Long projectId, Long deployTaskId) {
        return miniprogramService
                .getByProjectIdAndDeployTaskId(projectId, deployTaskId)
                .map(record -> new MiniprogramDeployHistoryDTO(
                        record.getId(),
                        record.getProjectId(),
                        record.getTaskId(),
                        record.getDeployTaskId(),
                        record.getCreatorId(),
                        record.getType().ordinal(),
                        record.getRobot(),
                        record.getVersion(),
                        record.getDescription()))
                .orElse(null);
    }

    @Override
    public void miniprogramDeployResultNotifyLark(Long projectId, Long deployTaskId, String imageBaseStr,
            String devPluginId, String hookStatus) {
        ProjectModel project = projectService.getNeverNull(projectId);
        if (project.getType() != ProjectType.Miniprogram) {
            throw new VanBadRequestException("project type is not supported");
        }
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, deployTaskId);
        if (task.getType() != BuildTaskType.MiniprogramDeploy && task.getType() != BuildTaskType.MiniprogramUpload) {
            throw new VanBadRequestException("task type is not supported");
        }
        // NOTE: 使用base64处理上传图片
        // 预览成功上传qrcode图片至飞书后，推送飞书卡片
        String imageKey = "";
        if (task.getType() == BuildTaskType.MiniprogramDeploy && !imageBaseStr.isEmpty() && "ok".equals(hookStatus)) {
            Path tempFile = null;
            try {
                tempFile = Files.createTempFile(String.format("%s-%d-qrcode", project.getName(), task.getId()), ".png");
                Files.write(tempFile, Base64.getDecoder().decode(imageBaseStr));
                imageKey = feishuService.uploadImage(tempFile.toFile()).map(CreateImageRespBody::getImageKey)
                        .orElse("");
            } catch (IOException e) {
                throw new BusinessException(e);
            } finally {
                if (tempFile != null) {
                    try {
                        Files.deleteIfExists(tempFile);
                    } catch (IOException e) {
                        // ignored
                    }
                }
            }
        }
        Optional<MiniprogramDeployHistoryRecord> recordOptional = miniprogramService
                .getByProjectIdAndDeployTaskId(projectId, deployTaskId);
        if (recordOptional.isPresent()) {
            FeishuCard card = FeishuUtils.buildMiniprogramLarkPushCard(project, task, recordOptional.get(), hookStatus,
                    imageKey, devPluginId);
            // use creatorId to find the user who triggered the task
            UserModel user = userService.getById(recordOptional.get().getCreatorId());
            // sendCard to user
            if (user != null) {
                feishuService.sendCardToUser(card, user.getUniqId());
            }
        }
    }

    @Override
    public Long createMiniprogramDeployHistory(Long projectId, Long taskId, Long deployTaskId, Long creatorId, int type,
            int robot, String version, String description) {
        return miniprogramService.createMiniprogramDeployHistory(projectId, taskId, deployTaskId, creatorId, type,
                robot, version, description);
    }

    @Override
    public Long deployMiniprogram(Long projectId, Long taskId, Long creatorId, int deployType, int robot,
            String version, String description) {
        LOGGER.info(
                "deployMiniprogram projectId: {}, taskId: {}, creatorId: {}, deployType: {}, robot: {}, version: {}, description: {}",
                projectId, taskId, creatorId, deployType, robot, version, description);
        return miniprogramService.deployMiniprogram(projectId, taskId, creatorId,
                MiniprogramDeployType.fromInt(deployType), robot, version, description);
    }

    @Override
    public void reportTaskMetaInfo(String projectName, Long taskId, String filename, String content) {
        ProjectModel project = projectService.getNeverNull(IdOrName.create(projectName));
        BuildTaskModel task = buildTaskService.getNeverNull(project.getId(), taskId);

        if (task.getStatus() != BuildTaskStatus.Running) {
            throw new InternalRequestException("only running task could report meta");
        }

        if (filename == null || filename.isEmpty()) {
            throw new VanBadRequestException("invalid filename");
        }

        String filekey = String.format("%s/tasks_meta/%d/%s", project.getName(), task.getId(), filename);
        Region.defaultStorage().put(filekey, content.getBytes(), null);

    }

    private HookDTO vanHookToDTO(VanHook hook) {
        HookDTO h = new HookDTO();
        h.setName(hook.getName());
        h.setBranch(hook.getBranch());
        h.setTimeout(hook.getTimeout());
        if (hook.getTiming() != null) {
            h.setTiming(
                hook.getTiming().stream()
                    .map(t -> Timing.valueOf(t.name()))
                    .collect(Collectors.toList()));
        }
        h.setVersion(hook.getVersion());
        h.setThrowError(hook.getThrowError());
        return h;
    }

    @Override
    public HookDTO[] getGlobalHooks() {
        return Optional.ofNullable(systemService.getGlobalHooks())
            .map(Arrays::stream)
            .orElseGet(Stream::empty)
            .map(this::vanHookToDTO)
            .toArray(HookDTO[]::new);
    }


    @Override
    public HookDTO[] getProjectHooks(Long projectId) {
        return metaService.getOptional(MetaType.ProjectHookConfiguration, projectId, HookDTO[].class).orElse(null);
    }

    @Override
    public void reportHookException(String projectName, Long taskId, String hookName, String message) {
        ProjectModel project = projectService.getNeverNull(IdOrName.create(projectName));
        BuildTaskModel task = buildTaskService.getNeverNull(project.getId(), taskId);
        if (task.getStatus() != BuildTaskStatus.Running) {
            throw new InternalRequestException("invalid task status");
        }

        FeishuCard card = FeishuUtils.buildHookExceptionCard(projectName, taskId, hookName, message);
        // send feishu to the admin
        // NOTE: there are duplicated admin with the same user, user has different
        // regions
        // like haha.wang + DOMESTIC haha.wang + OVERSEAS, but we only want to send once
        // should remove duplicated
        userService.findRoles(project.getId())
                .stream()
                .filter(r -> r.getRole() == Role.AdminRole)
                .map(UserBase::getUserUniqId)
                .collect(Collectors.toSet())
                .forEach(u -> {
                    feishuService.sendCardToUser(card, u);
                });
        ;
        // logging
        LOGGER.warn("project {}, task {}, hook {}, message {}", projectName, String.valueOf(taskId), hookName, message);
    }
}
