package cn.huolala.van.api.util;

import cn.huolala.van.api.exception.BizErrorCode;
import cn.huolala.van.api.exception.BizException;
import cn.huolala.van.api.model.UserModel;
import org.springframework.lang.NonNull;

public class UserUtils {
    private static final ThreadLocal<UserModel> userModelThreadLocal = new ThreadLocal<>();

    private UserUtils() {
    }

    @NonNull
    public static UserModel getCurrentUser() {
        UserModel user = UserUtils.userModelThreadLocal.get();
        if (user == null) throw new BizException(BizErrorCode.USER_AUTH_FAILED);
        return user;
    }

    public static void setCurrentUser(UserModel user) {
        UserUtils.userModelThreadLocal.set(user);
    }

    public static void removeCurrentUser() {
        UserUtils.userModelThreadLocal.remove();
    }

    @NonNull
    public static Long getCurrentUserId() {
        return getCurrentUser().getId();
    }

    @NonNull
    public static String getCurrentUserUniqId() {
        return getCurrentUser().getUniqId();
    }

    @NonNull
    public static String getCurrentUserEmail() {
        return getCurrentUser().getEmail();
    }
}
