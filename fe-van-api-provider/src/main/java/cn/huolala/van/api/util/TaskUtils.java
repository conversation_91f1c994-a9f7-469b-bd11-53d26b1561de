package cn.huolala.van.api.util;

import cn.huolala.van.api.facade.model.BuildTaskDTO;
import cn.huolala.van.api.model.tasks.BuildTaskModel;

public class TaskUtils {

    public static BuildTaskDTO buildTaskModelToDTO(BuildTaskModel model) {
        BuildTaskDTO dto = new BuildTaskDTO();
        dto.setId(model.getId());
        dto.setHash(model.getHash());
        dto.setBranch(model.getBranch());
        dto.setCommitMessage(model.getCommitMessage());
        dto.setUsername(model.getLegacyUsername());
        dto.setEmail(model.getLegacyEmail());
        dto.setType(model.getType());
        dto.setStatus(model.getStatus());
        dto.setProjectId(model.getProjectId());
        dto.setBuildId(model.getBuildId());
        dto.setCreatedAt(model.getCreatedAt());
        dto.setUpdatedAt(model.getUpdatedAt());
        return dto;
    }
}
