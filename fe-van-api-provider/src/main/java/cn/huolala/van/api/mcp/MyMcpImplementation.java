package cn.huolala.van.api.mcp;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.CanaryRule;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.feishu.FeishuActionValue;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.tasks.BuildTaskSearchField;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.FeishuUtils;
import cn.huolala.van.api.util.MdUtil;
import cn.huolala.van.api.util.UserUtils;
import cn.huolala.van.api.util.VanUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.huolala.van.api.model.feishu.FeishuActionValue.ButtonType.DANGER;
import static cn.huolala.van.api.util.AuditLogUtil.addAuditLogForDeployDev;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toMap;

@Service
public class MyMcpImplementation {
    @Autowired
    private DeployService deployService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private UserService userService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private FeishuService feishuService;

    @NonNull
    private MdUtil.UlBuilder buildTaskMdInfo(@NonNull BuildTaskModel o) {
        Long projectId = o.getProjectId();
        MdUtil.UlBuilder ul = MdUtil.ul();
        String idLink = MdUtil.link(String.valueOf(o.getId()), VanUtils.buildTaskLink(projectId, o.getId()));
        ul.add("构建 ID", idLink);
        OffsetDateTime time = o.getCreatedAt().atZoneSameInstant(ZoneId.systemDefault()).toOffsetDateTime();
        ul.add("状态", o.getStatus().name());
        ul.add("负责人", o.extractSimple().toUserString());
        ul.add("开始时间", time);
        String projectName = projectService.getNameById(projectId);
        String projectLink = MdUtil.link(projectName, VanUtils.buildProjectLink(projectId));
        ul.add("所属项目").indent().add("项目 ID", projectId).add("项目名", projectLink);
        ul.add("Git 信息", o.getBranch()).indent().add("Branch", o.getBranch())
            .add("Commit 信息", StringUtils.trim(o.getCommitMessage()));
        return ul;
    }

    private void assertParticipant(UserModel user, Long projectId) {
        if (userService.isParticipant(user, projectId)) return;
        throw new VanBadRequestException("你不是这个项目的参与者，无权限查询");
    }

    @ApiOperation("通过项目名获取项目基本信息")
    public String getProjectIntro(@ApiParam("项目名") @NonNull String projectName) {
        Long id = projectService.getIdByName(projectName);
        return projectService.get(id).map(project -> {
            MdUtil.UlBuilder ul = MdUtil.ul();
            ul.add("项目 ID", project.getId());
            ul.add("项目描述", project.getDescription());
            ul.add("代码仓库", FeishuUtils.buildGitlabRepoUrl(project));
            String appId = project.getConfig().getAppId();
            ul.add("AppID", appId);
            return ul.build();
        }).orElseGet(() -> "项目 " + MdUtil.bold(projectName) + " 不存在");
    }

    @ApiOperation("查询当前用户的最近构建")
    public String getMyRecentBuilds(@ApiParam("查看的条数，默认为 1") Integer limit) {
        UserModel user = UserUtils.getCurrentUser();
        if (limit == null) limit = 1;
        String res = buildTaskService.findUserRecentBuildTasks(user, limit).map(o -> {
            MdUtil.UlBuilder ul = buildTaskMdInfo(o);
            return ul.build();
        }).collect(joining("\n"));
        return StringUtils.isBlank(res) ? "未查到构建信息" : res;
    }

    @ApiOperation("查询某个项目的最近构建")
    public String searchProjectBuilds(@ApiParam("要查询的项目名") @NonNull String projectName,
                                      @ApiParam("筛选器：只看构建成功的") Boolean onlyDone,
                                      @ApiParam("筛选器：只看我的") Boolean onlyMe,
                                      @ApiParam("分页：第几页，下标从 0 开始，默认 0") Integer page,
                                      @ApiParam("分页：每页条数，默认 5") Integer size) {
        if (page == null) page = 0;
        if (size == null) size = 5;
        UserModel user = UserUtils.getCurrentUser();
        Long projectId = projectService.getIdByName(projectName);
        assertParticipant(user, projectId);

        SortedMap<BuildTaskSearchField, Set<String>> conditions = new TreeMap<>();
        if (onlyDone == Boolean.TRUE) conditions.put(BuildTaskSearchField.status, Collections.singleton("Done"));
        if (onlyMe == Boolean.TRUE)
            conditions.put(BuildTaskSearchField.username, Collections.singleton(user.getUniqId()));

        String res = buildTaskService.search(projectId, page, size, conditions).stream().map(this::buildTaskMdInfo)
            .map(MdUtil.UlBuilder::build).collect(joining("\n"));

        return StringUtils.isBlank(res) ? "未查到满足条件的构建信息" : res;
    }

    @ApiOperation("查询测试环境的发布历史记录")
    public String searchTestingDeployHistory(@ApiParam("要查询的项目名") @NonNull String projectName,
                                             @ApiParam("筛选器：环境") TestingEnv env,
                                             @ApiParam("筛选器：子环境编号，取值为 0 到 6，默认为 0 表示发布到默认子环境 stable 大于 0 表示发布到具体的子环境，比如 1 表示发布到 stable-1，以此类推") Integer stable,
                                             @ApiParam("筛选器：发布代码所属 Git 分支，前缀匹配") String branchPrefix,
                                             @ApiParam("分页：第几页，下标从 0 开始，默认 0") Integer page,
                                             @ApiParam("分页：每页条数，默认 5") Integer size) {
        UserModel user = UserUtils.getCurrentUser();
        long projectId = projectService.getIdByName(projectName);

        assertParticipant(user, projectId);

        HistorySearchForDeploy params = new HistorySearchForDeploy();
        params.setPage(page == null ? 0 : page);
        params.setSize(size == null ? 5 : size);
        if (env != null) params.setEnvName(Env.valueOf(env.name()));
        if (stable != null) params.setStableNames(Collections.singleton(ConfigEnv.buildStableName(stable)));
        if (branchPrefix != null) params.setBranchPrefix(Collections.singleton(branchPrefix));
        List<DeployRecord> history = historyService.searchDeployHistory(projectId, params);

        if (history.isEmpty()) return "未查到满足条件的构建信息";

        Set<Long> buildIds = history.stream().map(DeployRecord::getTaskId).collect(Collectors.toSet());
        Map<Long, BuildTaskModel> buildTaskMap = buildTaskService.batchGet(projectId, buildIds)
            .collect(toMap(BuildTaskModel::getId, Function.identity()));
        MdUtil.TableBuilder table = MdUtil.table("时间", "操作人", "环境", "构建 ID", "分支", "Commit 信息");
        for (DeployRecord i : history) {
            BuildTaskModel task = buildTaskMap.get(i.getTaskId());
            OffsetDateTime time = i.getCreatedAt().atZoneSameInstant(ZoneId.systemDefault()).toOffsetDateTime();
            String idLink = MdUtil.link(String.valueOf(task.getId()), VanUtils.buildTaskLink(projectId, task.getId()));
            String userName = i.extractSimple().toUserString();
            String message = StringUtils.trim(task.getCommitMessage());
            table.add(time, userName, i.getEnv(), idLink, task.getBranch(), message);
        }
        return table.build();
    }

    @ApiOperation("查询测试环境的发布历史记录")
    public String searchProductionDeployHistory(@ApiParam("要查询的项目名") @NonNull String projectName,
                                                @ApiParam("筛选：可用区") Region region,
                                                @ApiParam("分页：第几页，下标从 0 开始，默认 0") Integer page,
                                                @ApiParam("分页：每页条数，默认 5") Integer size) {
        UserModel user = UserUtils.getCurrentUser();
        long projectId = projectService.getIdByName(projectName);

        assertParticipant(user, projectId);

        HistorySearchForCanary params = new HistorySearchForCanary();
        params.setPage(page == null ? 0 : page);
        params.setSize(size == null ? 5 : size);
        if (region != null) params.setRegions(Collections.singleton(region.name()));

        List<CanaryRecord> history = historyService.searchCanaryHistory(projectId, params);

        if (history.isEmpty()) return "未查到满足条件的构建信息";

        Set<Long> buildIds = history.stream().map(CanaryRecord::getRules).flatMap(List::stream)
            .map(CanaryRule::getVersion).collect(Collectors.toSet());

        Map<Long, BuildTaskModel> buildTaskMap = buildTaskService.batchGet(projectId, buildIds)
            .collect(toMap(BuildTaskModel::getId, Function.identity()));

        MdUtil.UlBuilder ul = MdUtil.ul();
        for (CanaryRecord i : history) {
            OffsetDateTime time = i.getCreatedAt().atZoneSameInstant(ZoneId.systemDefault()).toOffsetDateTime();
            String userName = i.extractSimple().toUserString();
            MdUtil.UlBuilder uul = ul.add(time + " - by " + userName).indent();
            for (CanaryRule r : i.getRules()) {
                BuildTaskModel task = buildTaskMap.get(r.getVersion());
                if (task == null) continue;
                String idLink = MdUtil.link(String.valueOf(task.getId()),
                    VanUtils.buildTaskLink(projectId, task.getId()));
                uul.add(idLink, StringUtils.trim(task.getCommitMessage()));
            }
        }
        return ul.build();
    }

    @ApiOperation("发布到测试环境")
    public String deployToTesting(@ApiParam("要发布的项目名") @NonNull String projectName,
                                  @ApiParam("要发布的构建 ID") long taskId,
                                  @ApiParam("发布的目标环境，默认为 stg") @Nullable TestingEnv env,
                                  @ApiParam("子环境编号，取值为 0 到 6，默认为 0 表示发布到默认子环境 stable 大于 0 表示发布到具体的子环境，比如 1 表示发布到 stable-1，以此类推") Integer stable) {
        if (env == null) env = TestingEnv.stg;
        if (stable == null) stable = 0;
        UserModel user = UserUtils.getCurrentUser();

        Long projectId = projectService.getIdByName(projectName);
        ProjectModel project = projectService.getNeverNull(projectId);

        if (!userService.isParticipant(user, projectId))
            throw new VanBadRequestException("你不是这个项目的参与者，无权限发布");

        switch (project.getType()) {
            case Component:
                throw new VanBadRequestException("当前项目是一个 NPM 包，暂不支持发布");
            case Miniprogram:
                throw new VanBadRequestException("当前项目是一个小程序，暂不支持发布");
        }

        if (env == TestingEnv.pre && stable == 0) {
            boolean hasTestRole = userService.findByProjectIdsAndUserId(Collections.singleton(projectId), user.getId())
                .stream().anyMatch(i -> i.checkRole(Role.TestRole));
            if (!hasTestRole) throw new VanBadRequestException("只有项目的测试角色才能发布 pre 环境的 stable");
        }

        ConfigEnv ce = new ConfigEnv(Env.valueOf(env.name()), stable);
        DeployRecord dr = deployService.deployForDev(new DeployParams(user, project, ce, taskId));
        addAuditLogForDeployDev(dr);
        String testingLink = VanUtils.buildTestingUrl(project.getName(), ce);
        return "发布成功，现在你可通过以下链接来访问刚刚发布的版本：" + testingLink;
    }

    @ApiOperation("发布到生产环境")
    public String deployToProduction(@ApiParam("要发布的项目名") @NonNull String projectName,
                                     @ApiParam("要发布的构建 ID") long taskId) {
        UserModel user = UserUtils.getCurrentUser();

        Long projectId = projectService.getIdByName(projectName);
        ProjectModel project = projectService.getNeverNull(projectId);

        if (!userService.isParticipant(user, projectId))
            throw new VanBadRequestException("你不是这个项目的参与者，无权限发布");

        switch (project.getType()) {
            case Component:
                throw new VanBadRequestException("当前项目是一个 NPM 包，暂不支持发布");
            case Miniprogram:
                throw new VanBadRequestException("当前项目是一个小程序，暂不支持发布");
        }

        VanProjectConfig vanConfig = projectService.getVanProjectConfig(Region.defaultRegion(), projectId);
        String hosts = vanConfig.getAllowedHosts().stream().filter(i -> !i.endsWith(".huolala.work"))
            .collect(joining(", "));
        if (!hosts.isEmpty()) throw new VanBadRequestException(
            "只有纯办公网项目可以发布生产，当前项目包含域名 " + hosts + "，你可以在 " + VanUtils.buildGrayPageUrl(
                projectId) + " 手动操作");

        Region region = Region.defaultRegion();
        Optional<CanaryRecord> current = historyService.getHead(projectId, region);

        Long srcTaskId = current.map(c -> {
            List<CanaryRule> rules = c.getRules();
            if (rules.isEmpty()) return null;
            if (rules.size() == 1) {
                CanaryRule rule = rules.get(0);
                if (rule.getMode() == CanaryRule.Mode.every && rule.getPredicates().isEmpty()) return rule.getVersion();
            }
            throw new VanBadRequestException(
                "当前项目存在生产灰度规则，暂不支持 MCP 发布，你可以在 " + VanUtils.buildGrayPageUrl(
                    projectId) + " 手动操作");
        }).orElse(null);

        Long dstTaskId = taskId;
        Long headPoint = current.map(CanaryRecord::getId).orElse(null);

        FeishuCard card = FeishuUtils.buildFeishuReleaseConfirmCard(project, current.orElse(null), srcTaskId, dstTaskId,
            buildTaskService);

        FeishuCard.Element.ActionElement actions = new FeishuCard.Element.ActionElement().appendTo(card);

        new FeishuActionValue.PerformRelease(project.getId(), dstTaskId, headPoint).sign()
            .wrapAsButton("执行发布", DANGER).appendTo(actions);

        feishuService.sendCardToUser(card, user.getUniqId());

        return "生产发布申请已提交，请仔细 Review 飞书卡片，确认无误后执行操作";
    }

    public enum TestingEnv {stg, pre}
}
