package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.model.config.VanConfig;
import cn.huolala.van.api.model.config.VanConfigFile;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.events.ProjectEventContent.EditProjectConfig;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.EventService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.service.VanConfigService;
import cn.huolala.van.api.util.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/project/{projectId}/projectConfig")
@Validated
public class VanConfigController {
    @Autowired
    private ProjectService projectService;
    @Autowired
    private VanConfigService vanConfigService;
    @Autowired
    private EventService eventService;

    private void addEvent(ProjectModel project, VanConfig old, VanConfig config) {
        eventService.add(UserUtils.getCurrentUser(), project, new EditProjectConfig(config, old));
    }

    @GetMapping("/listFiles")
    @NonNull
    public List<VanConfigFile> listFiles(@RequireRole @PathVariable Long projectId, @RequestParam Env env) {
        ProjectModel projectModel = projectService.getNeverNull(projectId);
        if (env == null) env = Env.prd;
        return vanConfigService.listConfigName(env, projectModel);
    }

    @GetMapping("/getProjectEditorHistory")
    @NonNull
    public List<ProjectEvent> getProjectEditorHistory(@RequireRole @PathVariable Long projectId) {
        ProjectModel projectModel = projectService.getNeverNull(projectId);
        return vanConfigService.getProjectFileEvent(projectModel);
    }

    @GetMapping("/getFile")
    @NonNull
    public VanConfig getFile(@RequireRole @PathVariable Long projectId,
                             @RequestParam String fileName,
                             @RequestParam Env env) {
        ProjectModel project = projectService.getNeverNull(projectId);
        if (env == null) env = Env.prd;
        return vanConfigService.getAndAssertConfig(env, project.getName(), fileName);
    }

    @PostMapping("/createFile")
    @NonNull
    public VanConfig createFile(@RequireRole @PathVariable Long projectId,
                                @RequestBody VanConfig config) {
        ProjectModel projectModel = projectService.getNeverNull(projectId);
        config.setProjectName(projectModel.getName());
        config.setUpdatedAt(new Date());
        vanConfigService.createConfig(projectModel, config);
        addEvent(projectModel, null, config);
        return config;
    }

    @PostMapping("/updateFile")
    @NonNull
    public VanConfig updateFile(@RequireRole @PathVariable Long projectId,
                                @RequestBody VanConfig config) {
        ProjectModel projectModel = projectService.getNeverNull(projectId);
        config.setProjectName(projectModel.getName());
        config.setUpdatedAt(new Date());
        VanConfig oldConfig = vanConfigService.updateConfig(projectModel, config);
        addEvent(projectModel, oldConfig, config);
        return config;
    }
}
