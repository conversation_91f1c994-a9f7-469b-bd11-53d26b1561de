package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.dao.enums.MiniprogramDeployType;
import cn.huolala.van.api.exception.ForbiddenException;
import cn.huolala.van.api.facade.model.gitlab.GitlabMergeRequest;
import cn.huolala.van.api.model.ProjectAndTaskModel;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.deploy.MiniprogramDeployHistoryRecord;
import cn.huolala.van.api.model.meta.MiniprogramDeployMeta;
import cn.huolala.van.api.model.meta.TaskSyncInfo;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.tasks.BuildTaskSearchField;
import cn.huolala.van.api.model.tasks.TaskFlagName;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.ModelUtils;
import cn.huolala.van.api.util.StorageHelper;
import cn.lalaframework.storage.adapter.Storage;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.api.constants.enums.MetaType.MiniprogramDeployHistory;
import static cn.huolala.api.constants.enums.MetaType.UdSyncTaskStatus;
import static cn.huolala.van.api.facade.model.gitlab.GitlabMergeRequest.State.merged;
import static cn.huolala.van.api.facade.model.gitlab.GitlabMergeRequest.State.opened;
import static cn.huolala.van.api.util.UserUtils.getCurrentUser;

@RestController
@RequestMapping("/api/project/{projectId}/task")
@Validated
public class BuildTaskController {
    @Autowired
    private BuildTaskService buildTaskService;

    @Autowired
    private MetaService metaService;

    @Autowired
    private ProjectService projectService;
    @Autowired
    private GitlabService gitlabService;
    @Autowired
    private UserService userService;

    @Autowired
    private MiniprogramService miniprogramService;

    @PostMapping(params = "info=search")
    public List<BuildTaskModel> search(@RequireRole @PathVariable long projectId,
                                       @RequestParam(defaultValue = "0") int page,
                                       @RequestParam(defaultValue = "30") int size,
                                       @RequestBody SortedMap<BuildTaskSearchField, Set<String>> conditions) {
        return buildTaskService.search(projectId, page, size, conditions);
    }

    @PostMapping(params = "info=suggestion")
    public Map<BuildTaskSearchField, List<String>> findSuggestion(@RequireRole @PathVariable long projectId,
                                                                  String keywords) {
        return buildTaskService.findSuggestion(projectId, keywords);
    }

    @PostMapping(params = "info=find")
    @ApiOperation("Batch get build tasks by id list\n" + "@return A model list ordered by the request id list, and using null as a placeholder if not found.")
    public List<Optional<BuildTaskModel>> find(
        // Do not use @RequireRole, because this method requires a special role assertion.
        @PathVariable long projectId, @RequestParam(defaultValue = "[]") List<Long> ids) {
        ProjectModel project = projectService.getNeverNull(projectId);
        // Special Rule:
        // Npm packages can be accessed by everyone without role assertion.
        // In other words, skip role assertion for Npm packages.
        if (project.getType() != ProjectType.Component && !userService.isParticipant(getCurrentUser(), projectId)) {
            throw new ForbiddenException(String.format("You are not participating in project '%s'", project.getName()));
        }

        return buildTaskService.batchGet(projectId, new HashSet<>(ids))
            .collect(ModelUtils.toAlignedList(ids, BuildTaskModel::getId));
    }

    @Deprecated
    @PostMapping(params = "info=miniprogramDeployHistory")
    public Map<Long, MiniprogramDeployMeta[]> findMiniprogramDeployHistory(@RequireRole @PathVariable long projectId,
                                                                           @RequestParam(defaultValue = "[]") List<Long> ids) {
        return metaService.getValue(MiniprogramDeployHistory, new HashSet<>(ids), MiniprogramDeployMeta[].class);
    }

    @PostMapping(params = "info=miniprogramDeployHistoryV2")
    public List<MiniprogramDeployHistoryRecord> getMiniprogramDeployHistories(@RequireRole @PathVariable long projectId,
                                                                              @RequestParam("taskId") long taskId) {
        List<MiniprogramDeployHistoryRecord> previewList = miniprogramService.getLatestDeployByTypeOfEachRobot(
            projectId, taskId, MiniprogramDeployType.Preview);
        List<MiniprogramDeployHistoryRecord> uploadList = miniprogramService.getLatestDeployByTypeOfEachRobot(projectId,
            taskId, MiniprogramDeployType.Upload);
        return Stream.concat(previewList.stream(), uploadList.stream()).collect(Collectors.toList());
    }

    @PostMapping(params = "info=tag")
    public Map<Long, String> findTag(@RequireRole @PathVariable long projectId,
                                     @RequestParam(defaultValue = "[]") Set<Long> ids) {
        return buildTaskService.findTag(projectId, ids);
    }

    @PostMapping(params = "info=purgedFlag")
    public Map<Long, Boolean> hasPurgedFlag(@RequireRole @PathVariable long projectId,
                                            @RequestParam Set<Long> taskIds) {
        final String projectName = projectService.getNameById(projectId);
        // TODO: Considering the other regions?
        final Storage storage = Region.defaultStorage();
        return buildTaskService.batchGet(projectId, taskIds).parallel()
            .collect(Collectors.toMap(BuildTaskModel::getId, task -> {
                return StorageHelper.hasFlag(storage, projectName, task.getId(), TaskFlagName.Purged);
            }));
    }

    @PostMapping(params = "info=syncStatus")
    public Map<Region, Boolean> findSyncStatus(@RequireRole @PathVariable long projectId,
                                               @RequestParam long taskId,
                                               @RequestParam List<Region> regions) {
        buildTaskService.getNeverNull(projectId, taskId);
        String name = projectService.getNameById(projectId);
        String path = String.format("%s/tree/%d.txt", name, taskId);
        return regions.stream().distinct().parallel()
            .collect(Collectors.toMap(i -> i, i -> i.getStorage().exist(path)));
    }

    @PostMapping(params = "info=llmSync")
    public Map<Long, TaskSyncInfo> findLlmSyncInfo(@RequireRole @PathVariable long projectId,
                                                   @RequestParam(defaultValue = "[]") Set<Long> ids) {
        buildTaskService.assertOwnership(ids, projectId);
        return buildTaskService.findLlmSyncInfo(ids);
    }

    @PostMapping(params = "info=udSync")
    @Deprecated
    public Map<Long, TaskSyncInfo> findUdSyncInfo(@RequireRole @PathVariable long projectId,
                                                  @RequestParam(defaultValue = "[]") Set<Long> ids) {
        buildTaskService.assertOwnership(ids, projectId);
        return metaService.getValue(UdSyncTaskStatus, ids, TaskSyncInfo.class);
    }

    @GetMapping(params = "info=mergeRequest")
    public Optional<GitlabMergeRequest> getMergeRequest(@RequireRole @PathVariable long projectId,
                                                        @RequestParam long taskId) {
        ProjectAndTaskModel both = buildTaskService.getBoth(projectId, taskId);
        BuildTaskModel task = both.getTask();

        String repo = both.getProject().getRepository();

        String branch = task.getBranch();
        String hash = task.getHash();
        if (hash.isEmpty()) return Optional.empty();
        GitlabMergeRequest.SearchParams params = new GitlabMergeRequest.SearchParams();
        params.setSourceBranch(branch);
        for (GitlabMergeRequest.State state : Arrays.asList(opened, merged)) {
            params.setState(state);
            Optional<GitlabMergeRequest> found = gitlabService.findMergeRequests(repo, params)
                .filter(i -> hash.equals(i.getSha())).findFirst();
            if (found.isPresent()) return found;
        }
        return Optional.empty();
    }

    @GetMapping(params = "info=latestByBranches")
    @Nullable
    public Map<String, BuildTaskModel> findLatestByBranches(@RequireRole @PathVariable long projectId,
                                                            @RequestParam Set<String> branches) {
        return buildTaskService.findLatestByBranches(projectId, branches);
    }

    @GetMapping(params = "info=listByType")
    @Nullable
    public List<BuildTaskModel> listByType(@RequireRole @PathVariable long projectId,
                                           @RequestParam BuildTaskType type,
                                           @RequestParam(defaultValue = "0") int page,
                                           @RequestParam(defaultValue = "30") int size) {
        return buildTaskService.listByType(projectId, type, page, size);
    }
}
