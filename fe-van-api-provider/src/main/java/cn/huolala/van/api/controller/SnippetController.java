package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.model.EnvRegionName;
import cn.huolala.van.api.model.EnvRegionSnippet;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.Snippet;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.service.SnippetService;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.api.constants.enums.UserLogType.*;
import static cn.huolala.van.api.util.AuditLogUtil.addAuditLog;
import static cn.huolala.van.api.util.MocNotificationUtil.notifyMocForSwitchChange;

@RestController
@RequestMapping("/api/snippet")
@Validated
public class SnippetController {
    @Autowired
    private SnippetService snippetService;
    @Autowired
    private ProjectService projectService;

    @PostMapping(params = "info=all")
    public List<EnvRegionSnippet> find(@RequireRole @RequestParam Long projectId,
                                       @RequestBody List<EnvRegionName> requests) {
        return Stream.<Supplier<List<EnvRegionSnippet>>>of(
                () -> snippetService.findInProject(projectId, requests),
                () -> snippetService.findInGlobal(requests)
        ).parallel().map(Supplier::get).flatMap(List::stream).collect(Collectors.toList());
    }

    @PostMapping(params = "info=project")
    public List<EnvRegionSnippet> findInProject(@RequireRole @RequestParam Long projectId,
                                                @RequestBody List<EnvRegionName> requests) {
        return snippetService.findInProject(projectId, requests);
    }

    @PostMapping(params = "action=updateInProject")
    public void updateInProject(@RequireRole @RequestParam Long projectId,
                                @RequestParam Env env,
                                @RequestParam Region region,
                                @RequestBody List<Snippet.Update> updates) {
        ProjectModel project = projectService.getNeverNull(projectId);
        snippetService.updateInProject(projectId, env, region, updates);

        updates.forEach(u -> {
            addAuditLog(projectId, KnownService.getLogTypeByName(u.getName()))
                    .put("name", u.getName())
                    .put("env", env)
                    .put("region", region)
                    .put("isRemove", Boolean.TRUE.equals(u.getRemove()))
                    .put("place", u.getPlace())
                    .put("metaLength", StringUtils.length(u.getMeta()))
                    .put("contentLength", StringUtils.length(Snippet.getContent(u.getSnippet())));
            notifyMocForSwitchChange(
                    project,
                    env,
                    region,
                    project.getName(),
                    StringUtils.isEmpty(u.getName()));
        });
    }

    @PostMapping(params = "info=global")
    public List<EnvRegionSnippet> findInGlobal(@RequestBody List<EnvRegionName> requests) {
        return snippetService.findInGlobal(requests);
    }

    @PostMapping(params = "action=updateInGlobal")
    @RequiredSuperAdmin
    public void updateGlobal(@RequestParam Env env,
                             @RequestParam Region region,
                             @RequestBody List<Snippet.Update> updates) {
        snippetService.updateInGlobal(env, region, updates);
    }

    @Getter
    enum KnownService {
        TUNNEL("tunnel-inject", SERVICE_TUNNEL),
        V_CONSOLE("vConsole-inject", SERVICE_VCONSOLE),
        SWIM("swim-inject", SERVICE_VERSION_SWITCH),
        WATER_MARK("sso-watermark-inject", SERVICE_WATERMARK);

        @NonNull
        private final String name;
        @NonNull
        private final UserLogType userLogType;

        KnownService(@NonNull String name, @NonNull UserLogType logType) {
            this.name = name;
            this.userLogType = logType;
        }

        @NonNull
        public static UserLogType getLogTypeByName(@Nullable String name) {
            return Arrays.stream(values())
                    .filter(i -> i.getName().equals(name)).findFirst()
                    .map(KnownService::getUserLogType).orElse(UPDATE_PROJECT_SERVICE);
        }
    }
}
