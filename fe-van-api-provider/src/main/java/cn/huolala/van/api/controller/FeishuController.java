package cn.huolala.van.api.controller;

import cn.huolala.van.api.controller.dto.FeishuUserSummary;
import cn.huolala.van.api.exception.FeishuTokenPermissionException;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.feishu.GetSheetRangeParams;
import cn.huolala.van.api.service.FeishuService;
import cn.huolala.van.api.util.UserUtils;
import com.lark.oapi.service.bitable.v1.model.ListAppTableRecordRespBody;
import com.lark.oapi.service.bitable.v1.model.ListAppTableRespBody;
import com.lark.oapi.service.bitable.v1.model.ListAppTableViewRespBody;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.docx.v1.enums.RawContentDocumentLangEnum;
import com.lark.oapi.service.docx.v1.model.Document;
import com.lark.oapi.service.docx.v1.model.ListDocumentBlockRespBody;
import com.lark.oapi.service.docx.v1.model.RawContentDocumentRespBody;
import com.lark.oapi.service.sheets.v3.model.GetSpreadsheet;
import com.lark.oapi.service.sheets.v3.model.Sheet;
import com.lark.oapi.service.wiki.v2.model.Node;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/feishu")
@Validated
public class FeishuController {
    @Autowired
    private FeishuService feishuService;

    @GetMapping("/sheet/{spreadsheetToken}/{range}")
    public List<Object> getSheet(
            @PathVariable String spreadsheetToken,
            @PathVariable String range,
            @RequestParam(required = false) String valueRenderOption
    ) throws IOException, InterruptedException {
        assertTokenPermission(spreadsheetToken);
        return feishuService.getSheet(spreadsheetToken, range, valueRenderOption);
    }

    @GetMapping("/wiki/node/{token}")
    public Node getWikiNode(@PathVariable String token) {
        return feishuService.getWikiNode(token);
    }

    @GetMapping(value = "/document/{documentId}", params = {"info=meta"})
    public Document getDocumentMeta(@PathVariable String documentId) {
        return feishuService.getDocumentMeta(documentId);
    }

    @GetMapping(value = "/document/{documentId}", params = {"info=raw"})
    public RawContentDocumentRespBody getDocumentRawContent(
            @PathVariable String documentId,
            @RequestParam(required = false) RawContentDocumentLangEnum lang
    ) throws IOException, InterruptedException {
        assertTokenPermission(documentId);
        return feishuService.getDocumentRawContent(documentId, lang);
    }

    @GetMapping(value = "/document/{documentId}", params = {"info=blocks"})
    public ListDocumentBlockRespBody getDocumentBlock(
            @PathVariable String documentId
    ) throws IOException, InterruptedException {
        assertTokenPermission(documentId);
        return feishuService.getDocumentBlocks(documentId);
    }

    @GetMapping(value = "/sheet/{spreadsheetToken}", params = {"info=sheets"})
    public Sheet[] getSheets(@PathVariable String spreadsheetToken) {
        return feishuService.getSheets(spreadsheetToken);
    }

    @GetMapping(value = "/sheet/{spreadsheetToken}", params = {"info=meta"})
    public GetSpreadsheet getSpreadsheetMeta(@PathVariable String spreadsheetToken) {
        return feishuService.getSheetMeta(spreadsheetToken);
    }

    @GetMapping(value = "/sheet/{spreadsheetToken}/{sheetId}", params = {"info=meta"})
    public Sheet getSheetMeta(@PathVariable String spreadsheetToken, @PathVariable String sheetId) {
        return feishuService.getSheetMeta(spreadsheetToken, sheetId);
    }

    @GetMapping(value = "/bitable/{appToken}", params = {"info=tables"})
    public Optional<ListAppTableRespBody> getBitableTables(@PathVariable String appToken) {
        return feishuService.getBitableTables(appToken);
    }

    @GetMapping(value = "/bitable/{appToken}/{tableId}", params = {"info=views"})
    public Optional<ListAppTableViewRespBody> getBitableTableViews(
            @PathVariable String appToken,
            @PathVariable String tableId
    ) {
        return feishuService.getBitableTableViews(appToken, tableId);
    }

    @GetMapping(value = "/bitable/{appToken}/{tableId}/{viewId}", params = {"info=records"})
    public Optional<ListAppTableRecordRespBody> getBitableRecords(
            @PathVariable String appToken,
            @PathVariable String tableId,
            @PathVariable String viewId
    ) throws IOException, InterruptedException {
        assertTokenPermission(appToken);
        return feishuService.getBitableRecords(appToken, tableId, viewId);
    }

    private void assertTokenPermission(String token) throws IOException, InterruptedException {
        if (token == null) throw new FeishuTokenPermissionException("The 'token' must not be null");
        // Read white list from https://huolala.feishu.cn/wiki/BAnLwDLNBi8CbAkV8bVclYm8nVd
        GetSheetRangeParams req = new GetSheetRangeParams("XZTnsnNiFh1zjlt5iwlcapLbnof", "8f4a32", "ToString");
        boolean r = feishuService.getSheetWithCache(req, 60)
                .stream().noneMatch(i -> i instanceof List && token.equals(((List<?>) i).get(0)));
        if (r) throw new FeishuTokenPermissionException("Not allowed for token '" + token + "'");
    }

    @PostMapping(params = "info=userDetail")
    @RequiredSuperAdmin
    public Map<String, User> batchGetUserForSuperAdmin(@RequestParam Set<String> uniqIds) {
        Map<String, User> map = feishuService.batchGetUserInfo(uniqIds)
                .stream().collect(Collectors.toMap(User::getEnName, i -> i));
        return uniqIds.stream().collect(Collectors.toMap(i -> i, map::get));
    }

    @PostMapping(params = "info=user")
    public Map<String, FeishuUserSummary> batchGetUser(@RequestParam Set<String> uniqIds) {
        return feishuService.batchGetUserInfo(uniqIds).stream()
                .collect(Collectors.toMap(User::getEnName, FeishuUserSummary::new));
    }

    @GetMapping(params = "info=me")
    public User getMe() {
        UserModel user = UserUtils.getCurrentUser();
        return feishuService.getUserInfo(user.getUniqId());
    }
}
