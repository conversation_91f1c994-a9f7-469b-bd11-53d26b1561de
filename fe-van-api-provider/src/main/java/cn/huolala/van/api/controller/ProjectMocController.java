package cn.huolala.van.api.controller;

import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.controller.dto.AddMocRecordDTO;
import cn.huolala.van.api.model.MocRecordModel;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.MocService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.util.UserUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/project/{projectId}/moc")
@Validated
public class ProjectMocController {
    @Autowired
    private MocService mocService;

    @Autowired
    private ProjectService projectService;

    @PostMapping("/add")
    public void add(@RequireRole @PathVariable Long projectId,
                    @Valid @RequestBody AddMocRecordDTO addMocRecordDTO) {
        MocRecordModel mocRecordModel = new MocRecordModel();
        BeanUtils.copyProperties(addMocRecordDTO, mocRecordModel);

        ProjectModel projectModel = projectService.getNeverNull(projectId);

        mocRecordModel.setServiceName(projectModel.getConfig().getAppId());
        mocRecordModel.setDeployUser(UserUtils.getCurrentUser().getUniqId());

        mocRecordModel.setEnv("prd");
        Long now = System.currentTimeMillis() / 1000;
        mocRecordModel.setStartTime(now);
        mocRecordModel.setEndTime(now);

        mocService.createMocRecord(mocRecordModel);
    }

//    @PostMapping(params="action=toggleService")
//    public void toggleService(@RequireRole @PathVariable long projectId) {
//        ProjectModel project = projectService.getById(projectId);
//        MocRecordModel model = new MocRecordModel();
//        //    private String action;
//        //    private MocRegionEnum region;
//        //    private String title
//        //    private String message;
//        //    private String status;
//        //            action: '配置更新',
//        //            status: 'success',
//        //            title: `${projectName} Service ${name} ${v ? '开启' : '关闭'}`,
//        //            message: `更新 ${name} 配置`,
//        //            region: toMocRegion(idc),
//
//        model.setAction("配置更新");
//        model.setStatus("success");
//        model.setRegion(null);
//        model.setTitle(null);
//        model.setMessage(null);
//
//        model.setDeployUser(UserUtils.getCurrentUser().getUniqId());
//    }
}
