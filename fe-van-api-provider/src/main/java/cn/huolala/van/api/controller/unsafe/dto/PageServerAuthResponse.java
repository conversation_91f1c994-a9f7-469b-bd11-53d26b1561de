package cn.huolala.van.api.controller.unsafe.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;

@Data
public class PageServerAuthResponse {

    private Type type;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String content;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String url;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    public enum Type {
        INJECT("data"),
        REDIRECT("redirect"),
        ;

        private final String type;

        Type(String t) {
            this.type = t;
        }

        @JsonValue
        public String getType() {
            return this.type;
        }
    }

}
