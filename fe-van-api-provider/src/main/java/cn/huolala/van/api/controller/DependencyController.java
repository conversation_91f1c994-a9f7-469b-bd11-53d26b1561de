package cn.huolala.van.api.controller;

import cn.huolala.van.api.controller.dto.PackageUsage;
import cn.huolala.van.api.dao.entity.ProjectDependencyEntity;
import cn.huolala.van.api.exception.ForbiddenException;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.ProjectDependencyService;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/dependency")
@Validated
public class DependencyController {
    @Autowired
    private ProjectDependencyService projectDependencyService;

    @Autowired
    private BuildTaskService buildTaskService;

    @Autowired
    private UserService userService;

    @GetMapping(params = "info=uses")
    public List<PackageUsage> findUses(@RequestParam String projectName) {
        List<ProjectDependencyEntity> pdList = projectDependencyService.findUsesByPackageName(projectName);
        Map<Long, Long> projectIdMap = pdList.stream().map(ProjectDependencyEntity::getTaskId)
                .collect(Collectors.collectingAndThen(
                        Collectors.toSet(),
                        buildTaskService::findProjectIdsByTaskIds
                ));
        return pdList.stream()
                .map(i -> PackageUsage.create(projectIdMap.get(i.getTaskId()), i))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Deprecated
    @PostMapping("action=saveTasks")
    public int deprecatedSaveTasks(@RequestParam Long projectId, @RequestParam Set<Long> taskIds) {
        return saveTasks(projectId, taskIds);
    }

    @PostMapping(params = "action=saveTasks")
    public int saveTasks(@RequestParam Long projectId, @RequestParam Set<Long> taskIds) {
        UserModel user = UserUtils.getCurrentUser();

        // Assert projectId permission for current user.
        if (userService.filterUserAccessibleProjectIds(Collections.singleton(projectId), user).isEmpty()) {
            throw new ForbiddenException("No permission to operate project " + projectId);
        }

        // Assert taskIds are owned by projectId.
        Map<Long, BuildTaskModel> taskMap = buildTaskService.batchGet(projectId, taskIds)
                .collect(Collectors.toMap(BuildTaskModel::getId, Function.identity()));
        if (taskMap.size() != taskIds.size()) {
            String badIds = taskIds.stream().filter(i -> !taskMap.containsKey(i))
                    .map(Object::toString)
                    .collect(Collectors.joining(", "));
            throw new ForbiddenException("Provided taskIds (" + badIds + ") is not owned by projectId");
        }

        return projectDependencyService.saveTasks(taskMap.values());
    }
}
