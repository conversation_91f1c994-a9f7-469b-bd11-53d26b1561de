package cn.huolala.van.api.controller.dto;

import cn.huolala.van.api.model.PdmInfo;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
@NoArgsConstructor
public class ProdReleaseParams {
    @JsonProperty(required = true)
    private LegacyDeployConfig config;
    @Nullable
    private String message;
    @Nullable
    private PdmInfo pdmInfo;
}
