package cn.huolala.van.api.controller.internal.dto;

import cn.huolala.van.api.util.ModelUtils;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.lark.oapi.card.model.Action;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * Why not use the com.lark.oapi.card.model.CardAction?
 * Because lark model use gson annotations, that is not compatible with jackson.
 */
@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CardActionParams {
    @Nullable
    private String openId;
    @Nullable
    private String userId;
    @Nullable
    private String openMessageId;
    @Nullable
    private String tenantKey;
    @Nullable
    private String token;
    @Nullable
    private String timezone;
    @Nullable
    private Action action;
    @Nullable
    private String challenge;
    @Nullable
    private String type;

    @NonNull
    public String getUserId() {
        ModelUtils.requireNonNullInGetter(userId);
        return userId;
    }
}
