package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.annotation.NoAuthentication;
import cn.huolala.van.api.controller.dto.PortalCounter;
import cn.huolala.van.api.controller.dto.TaskIntro;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.lone.LoneApp;
import cn.huolala.van.api.model.lone.LoneAppSummary;
import cn.huolala.van.api.model.lone.LoneEnv;
import cn.huolala.van.api.model.lone.LoneRegion;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.roles.UserRoleModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.tasks.BuildTaskSearchField;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.StorageHelper;
import cn.huolala.van.api.util.UserUtils;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

@RestController
@RequestMapping("/api/misc")
@Validated
public class MiscController {
    @Autowired
    private LoneService loneService;

    @Autowired
    private FeishuService feishuService;

    @Autowired
    private BuildTaskService buildTaskService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SsoService ssoService;

    @Autowired
    private MonitorRecordService monitorRecordService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private MiniprogramService miniprogramService;

    @Autowired
    private GitlabService gitlabService;

    @GetMapping(params = "info=myAppIds")
    public List<LoneApp> getMyAppIds() {
        return getMyAppIdsEachRegions().values().stream().flatMap(Collection::stream).distinct()
            .collect(Collectors.toList());
    }

    @GetMapping(params = "info=myAppIdsWithRegions")
    public Map<LoneRegion, List<LoneApp>> getMyAppIdsEachRegions() {
        UserModel user = UserUtils.getCurrentUser();
        return LoneRegion.supportedRegions().parallel().map(region -> {
            List<LoneApp> list = loneService.getBusListByUser(region, LoneEnv.prd, user.getUniqId());
            return Pair.of(region, list);
        }).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
    }

    @GetMapping(params = "info=searchAppIds")
    public List<LoneAppSummary> searchAppIds(String keyword) {
        return loneService.getAllAppid(keyword);
    }

    @GetMapping(params = "info=getAppIdSummaryEachRegions")
    public Map<LoneRegion, LoneAppSummary> getAppIdSummaryEachRegions(String appId) {
        return LoneRegion.supportedRegions().parallel().map(region -> {
            LoneAppSummary detail = loneService.getOneAppidInfoSummary(region, LoneEnv.prd, appId);
            if (detail == null) return null;
            return Pair.of(region, detail);
        }).filter(Objects::nonNull).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
    }

    /**
     * Get all participants of TestRoe
     * NOTE: This API is probably not called from the Nad SDK.
     */
    @GetMapping(value = "/tasks")
    @NoAuthentication
    public List<BuildTaskModel> getCompleteTaskListForAnonymous(@RequestParam String projectName) {
        // Get the project model using the request parameters and assert it is not null.
        Long projectId = projectService.getIdByName(projectName);
        Map<BuildTaskSearchField, Set<String>> conditions = new EnumMap<>(BuildTaskSearchField.class);
        conditions.put(BuildTaskSearchField.status, Collections.singleton(BuildTaskStatus.Done.name()));
        return buildTaskService.search(projectId, 0, 50, conditions);
    }

    /**
     * Get all participants of TestRoe
     * NOTE: This API is probably not called from the Nad SDK.
     */
    @GetMapping(value = "/task")
    @NoAuthentication
    public TaskIntro getTaskIntroForAnonymous(@RequestParam String projectName, @RequestParam Long taskId) {
        // Get the project model using the request parameters and assert it is not null.
        Long projectId = projectService.getIdByName(projectName);

        ProjectAndTaskModel both = buildTaskService.getBoth(projectId, taskId);
        ProjectModel project = both.getProject();
        BuildTaskModel task = both.getTask();

        // Get the project roles and group them by userUniq.
        Map<String, List<Tuple2<Role, CmdbAppRegion>>> roleMap = userService.findRoles(task.getProjectId()).stream()
            .collect(Collectors.groupingBy(UserRoleModel::getUserUniqId,
                Collectors.mapping(i -> new Tuple2<>(i.getRole(), i.getRegion()), Collectors.toList())));

        // Batch get user models by all userUniqIds, and build them into a List<TaskIntro.Member>.
        List<TaskIntro.Member> projectMembers = feishuService.batchGetUserInfo(roleMap.keySet()).stream().map(i -> {
            String name = i.getName();
            if (name == null) return null;
            List<Tuple2<Role, CmdbAppRegion>> roles = ofNullable(roleMap.get(i.getEnName())).orElseGet(
                Collections::emptyList);
            return new TaskIntro.Member(i, roles);
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // Attempt to parse dirty username and build it into a UserBasic object.
        TaskIntro.UserBasic user = Optional.of(task).map(buildTaskService::getFixedUser).map(UserBase::getUserUniqId)
            .map(feishuService::getUserInfo).map(TaskIntro.UserBasic::new).orElse(null);

        return new TaskIntro(project, task, user, projectMembers);
    }

    /**
     * Migrate to UserController
     */
    @ApiOperation("Migrated to UserController")
    @Deprecated
    @GetMapping(params = "info=searchUserFromSso")
    public List<UserBase.Simple> searchUserFromSso(@RequestParam String keyword,
                                                   @RequestParam(defaultValue = "5") int limit) {
        if (keyword == null) return Collections.emptyList();
        keyword = keyword.trim();
        if (keyword.isEmpty()) return Collections.emptyList();
        return Arrays.stream(ssoService.searchUser(keyword)).map(i -> UserBase.create(i.getAccount(), i.getUserName()))
            .filter(Objects::nonNull).limit(limit).collect(Collectors.toList());
    }

    @GetMapping(params = "info=portal")
    public Map<ProjectType, Map<PortalCounter, Integer>> getPortal() {
        return Arrays.stream(PortalCounter.values())
            // Get all data concurrently and convert to Tuple3<PortalCounter, ProjectType, Integer>.
            .parallel()
            .flatMap(i -> i.getData().entrySet().stream().map(e -> new Tuple3<>(i, e.getKey(), e.getValue())))
            // Convert to Map<ProjectType, Map<PortalCounter, Integer>>.
            .collect(Collectors.groupingBy(Tuple3::getSecond, Collectors.toMap(Tuple3::getFirst, Tuple3::getThird)));
    }

    @GetMapping(params = "info=importantProjects")
    public List<ImportantProjectView> getImportantProjects(@DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam(required = false) @Nullable OffsetDateTime date,
                                                           @RequestParam(required = false) @Nullable Integer limit) {
        return monitorRecordService.getImportantProjects(new ImportantProjectsParams(
            date == null ? null : date.atZoneSameInstant(ZoneId.systemDefault()).toLocalDate(), limit));
    }

    @PostMapping(params = "action=clearImportantProjectsCache")
    @RequiredSuperAdmin
    public void clearImportantProjectsCache() {
        monitorRecordService.clearImportantProjectsCache();
    }

    @NoAuthentication
    @GetMapping("/deploy")
    public List<ConfigEnvTask> getProjectEnvDeployStatus(@RequestParam @NonNull String projectName) {
        long projectId = projectService.getIdByName(projectName);
        return historyService.getLatestTaskIdForEachEnv(projectId);
    }

    @PostMapping(params = "action=touchLastModifiedFlag")
    public void touchLastModifiedFlag(@RequestParam Region region, @RequestParam Env env) {
        StorageHelper.touchLastModifiedFlag(region.getStorage(), env);
    }

    @GetMapping(params = "info=lastModifiedFlag")
    public Optional<LastModifiedData<String>> getLastModifiedFlag(@RequestParam Region region, @RequestParam Env env) {
        return StorageHelper.getLastModifiedFlag(region.getStorage(), env);
    }

    @GetMapping(params = "info=allLastModifiedFlag")
    public Map<Region, Map<Env, Optional<LastModifiedData<String>>>> getAllLastModifiedFlag() {
        return Region.supportedRegions().parallel().collect(Collectors.toMap(Function.identity(),
            region -> Arrays.stream(Env.values()).parallel().collect(Collectors.toMap(Function.identity(),
                v -> StorageHelper.getLastModifiedFlag(region.getStorage(), v)))));
    }

    @GetMapping(params = "info=gitlabProxy")
    public ResponseEntity<?> gitlabProxy(@RequestHeader(value = "x-van-project") String projectName,
            @RequestHeader(value = "x-van-task-id") String taskId, @RequestParam Map<String, String> query) {
        ProjectModel project = projectService.getNeverNull(IdOrName.create(projectName));
        BuildTaskModel task = buildTaskService.getNeverNull(project.getId(), Long.valueOf(taskId));
        return gitlabService.simpleProxy(project.getRepository(), task, query);
    }

}
