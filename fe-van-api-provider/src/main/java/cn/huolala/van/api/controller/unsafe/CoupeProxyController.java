package cn.huolala.van.api.controller.unsafe;

import cn.huolala.van.api.model.PageServerPayload;
import cn.huolala.van.api.service.PageServerCoupeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Nonnull;
import java.util.Map;

@RestController
@RequestMapping("/coupe/proxy")
public class CoupeProxyController {
    @Autowired
    PageServerCoupeService pageServerService;

    @PostMapping("/page_server_schema")
    Map<String, Object> getCoupeResponse(@RequestBody @Nonnull PageServerPayload payload) throws Exception {
        return pageServerService.getCoupeInject(payload);
    }
}
