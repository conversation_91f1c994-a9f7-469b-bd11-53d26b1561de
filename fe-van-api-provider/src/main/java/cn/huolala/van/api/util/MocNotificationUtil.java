package cn.huolala.van.api.util;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.MocRecordModel;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.CanaryRule;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.MocService;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.springframework.lang.NonNull;

import java.util.concurrent.CompletableFuture;

public class MocNotificationUtil {
    @NonNull
    private static final DeferHandler<MocRecordModel> autoSubmitter = new DeferHandler<>(list -> {
        MocService mocService = ApplicationContextUtil.getBean(MocService.class);
        list.stream().<Runnable>map(item -> () -> {
            item.setEndTime(System.currentTimeMillis() / 1000);
            mocService.createMocRecord(item);
        }).forEach(CompletableFuture::runAsync);
    });

    private MocNotificationUtil() {
    }

    @NonNull
    @CanIgnoreReturnValue
    public static MocRecordModel notifyMoc(@NonNull ProjectModel project) {
        MocRecordModel item = new MocRecordModel();
        item.setStartTime(System.currentTimeMillis() / 1000);
        item.setServiceName(project.getConfig().getAppId());
        item.setDeployUser(UserUtils.getCurrentUser().getUniqId());
        item.setEnv("prd");
        item.setStatus("success");
        autoSubmitter.add(item);
        return item;
    }

    @NonNull
    @CanIgnoreReturnValue
    public static MocRecordModel notifyMocForProjectConfigChange(@NonNull ProjectModel project,
                                                                 @NonNull Env env,
                                                                 @NonNull Region region,
                                                                 @NonNull String configName) {
        String action = "Project Config Update";
        String verb = "Update";
        return notifyMocForProjectAction(project, action, env, region, verb, configName);
    }

    @NonNull
    @CanIgnoreReturnValue
    public static MocRecordModel notifyMocForSwitchChange(@NonNull ProjectModel project,
                                                          @NonNull Env env,
                                                          @NonNull Region region,
                                                          @NonNull String serviceName,
                                                          boolean status) {
        String action = "Project Switch";
        String verb = status ? "Enable" : "Disable";
        return notifyMocForProjectAction(project, action, env, region, verb, serviceName);
    }

    @NonNull
    @CanIgnoreReturnValue
    public static MocRecordModel notifyMocForProjectAction(@NonNull ProjectModel project,
                                                           @NonNull String action,
                                                           @NonNull Env env,
                                                           @NonNull Region region,
                                                           @NonNull String verb,
                                                           @NonNull String objectName) {
        return notifyMoc(project)
                .action(action)
                .env(env)
                .region(region)
                .title(String.format("[%s] %s the %s", project.getName(), verb, objectName))
                .message(String.format("%s the %s of project %s", verb, objectName, project.getName()));
    }

    @NonNull
    @CanIgnoreReturnValue
    public static MocRecordModel notifyMocForRelease(@NonNull ProjectModel project,
                                                     @NonNull CanaryRecord canaryRecord) {
        String action = "Project Release";
        String verb = "Release";
        StringBuilder sb = new StringBuilder();
        Long[] ids = canaryRecord.getRules().stream()
                .map(CanaryRule::getVersion).filter(i -> i != null && i > 0)
                .toArray(Long[]::new);
        if (ids.length == 1) {
            sb.append("task ");
            sb.append("#");
            sb.append(ids[0]);
        } else {
            sb.append("tasks ");
            for (int i = 0; i < ids.length; i++) {
                if (i > 0) sb.append(", ");
                sb.append("#");
                sb.append(ids[i]);
            }
        }
        Region region = canaryRecord.getRegion();
        if (region == null) region = Region.cn;
        return notifyMocForProjectAction(project, action, Env.prd, region, verb, sb.toString());
    }
}
