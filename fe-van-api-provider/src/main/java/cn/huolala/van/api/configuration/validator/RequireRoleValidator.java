package cn.huolala.van.api.configuration.validator;

import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

@Component
public class RequireRoleValidator implements ConstraintValidator<RequireRole, Long> {
    @Autowired
    private UserService userService;

    @Override
    public boolean isValid(Long projectId, ConstraintValidatorContext context) {
        return userService.isParticipant(UserUtils.getCurrentUser(), projectId);
    }
}