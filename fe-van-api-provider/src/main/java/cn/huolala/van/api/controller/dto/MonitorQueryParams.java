package cn.huolala.van.api.controller.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.Set;

import static org.springframework.format.annotation.DateTimeFormat.ISO.DATE_TIME;

@Getter
@Setter
@NoArgsConstructor
public class MonitorQueryParams {
    @NonNull
    private Set<Long> projectIds = Collections.emptySet();

    private Integer limit = 10;

    private Double minimalPv = 2000.0;

    @Nullable
    @DateTimeFormat(iso = DATE_TIME)
    private OffsetDateTime startTime;

    @Nullable
    @DateTimeFormat(iso = DATE_TIME)
    private OffsetDateTime endTime;
}
