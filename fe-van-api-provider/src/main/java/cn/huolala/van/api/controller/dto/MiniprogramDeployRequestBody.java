
package cn.huolala.van.api.controller.dto;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import com.fasterxml.jackson.annotation.JsonProperty;

import cn.huolala.van.api.dao.enums.MiniprogramDeployType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MiniprogramDeployRequestBody {
    @JsonProperty(required = true)
    private MiniprogramDeployType type;

    @JsonProperty(required = true)
    @NonNull
    private Integer robot;

    @Nullable
    private String version;

    @Nullable
    private String description;
}
