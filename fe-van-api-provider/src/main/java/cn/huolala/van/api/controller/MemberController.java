package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.exception.ForbiddenException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.roles.ProjectUserModel;
import cn.huolala.van.api.model.roles.UserRoleModel;
import cn.huolala.van.api.service.GitlabService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static cn.huolala.van.api.util.AuditLogUtil.addAuditLog;

@RestController
@RequestMapping("/api/members")
@Validated
public class MemberController {
    @Autowired
    private ProjectService projectService;
    @Autowired
    private UserService userService;
    @Autowired
    private GitlabService gitlabService;

    @PostMapping(params = "action=put")
    public void put(
            @RequestParam Long projectId,
            @RequestParam String uniqId,
            @RequestParam Role role
    ) {
        Boolean cim = canIManage(Collections.singleton(projectId)).get(projectId);
        if (cim == null || cim.equals(Boolean.FALSE)) {
            throw new ForbiddenException("No permission to operate project " + projectId);
        }
        UserModel user = userService.pullUserFromSsoIfNeeded(uniqId);
        if (user == null) throw ResourceNotFoundException.create("user", "uniqId", uniqId);

        userService.updateProjectUser(projectId, user.getId(), role);

        addAuditLog(projectId, UserLogType.ADD_USER).put("role", role).put("userID", user.getId());
    }

    @PostMapping(params = "info=admins")
    public Map<Long, List<UserBase.Simple>> getAdminByProjectIds(@RequestParam Set<Long> ids) {
        return userService.findProjectUsers(ids, Role.AdminRole).collect(Collectors.groupingBy(
                ProjectUserModel::getProjectId,
                Collectors.mapping(UserBase::extractSimple, Collectors.toList())
        ));
    }

    @PostMapping(params = "info=all")
    public Map<Long, List<UserRoleModel>> getMembersByProjectIds(@RequestParam Set<Long> ids) {
        UserModel user = UserUtils.getCurrentUser();
        Set<Long> filteredIds = userService.filterUserAccessibleProjectIds(ids, user);
        return userService.findRoles(filteredIds);
    }

    @PostMapping(params = "info=canIAccess")
    public Map<Long, Boolean> canIAccess(@RequestParam Set<Long> ids) {
        Set<Long> result = userService.filterUserAccessibleProjectIds(ids, UserUtils.getCurrentUser());
        return ids.stream().collect(Collectors.toMap(i -> i, result::contains));
    }

    @PostMapping(params = "info=canIManage")
    public Map<Long, Boolean> canIManage(@RequestParam Set<Long> ids) {
        if (userService.isSuperAdmin(UserUtils.getCurrentUserUniqId())) {
            return ids.stream().collect(Collectors.toMap(i -> i, i -> true));
        }
        return userService.findByProjectIdsAndUserId(ids, UserUtils.getCurrentUserId())
                .stream().collect(Collectors.groupingBy(
                        ProjectUserModel::getProjectId,
                        Collectors.reducing(false, e -> e.checkRole(Role.AdminRole), (a, b) -> a || b)
                ));
    }

    @PostMapping(params = "info=canITest")
    public Map<Long, Boolean> canITest(@RequestParam Set<Long> ids) {
        if (userService.isSuperAdmin(UserUtils.getCurrentUserUniqId())) {
            return ids.stream().collect(Collectors.toMap(i -> i, i -> true));
        }
        return userService.findByProjectIdsAndUserId(ids, UserUtils.getCurrentUserId())
                .stream().collect(Collectors.groupingBy(
                        ProjectUserModel::getProjectId,
                        Collectors.reducing(false, e -> e.checkRole(Role.TestRole), (a, b) -> a || b)
                ));
    }

    @PostMapping(params = "action=mergeIntoGitlab")
    public int mergeIntoGitlab(@RequestParam Long projectId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        List<UserRoleModel> roles = userService.findRoles(projectId);
        return gitlabService.********************(project.getRepository(), roles).size();
    }

    @PostMapping(params = "action=pullFromLone")
    public int pullFromLone(@RequestParam Long projectId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return projectService.pullUsersFromLone(projectId, project.fetchAndAssertAppId());
    }

}
