package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.NpmPublishParams;
import cn.huolala.van.api.model.NpmPublishRecord;
import cn.huolala.van.api.model.SearchNpmPublishHistoryParams;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.npm.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.HistoryService;
import cn.huolala.van.api.service.NpmService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.util.UserUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/npm")
public class NpmController {

    @Autowired
    private HistoryService historyService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private NpmService npmService;

    @PostMapping(params = "info=history")
    public List<List<NpmPublishRecord>> searchHistory(@RequestBody List<SearchNpmPublishHistoryParams> queries) {
        return queries.stream().parallel().map(historyService::searchNpmPublishHistory).collect(Collectors.toList());
    }

    @GetMapping(params = "info=find")
    public NpmInfo getInfo(@RequireRole @RequestParam long projectId, @RequestParam long taskId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        if (project.getType() != ProjectType.Component) {
            throw new VanBadRequestException("project is not component project");
        }
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        return npmService.getInfo(project, task);
    }

    @PostMapping(params = "action=publish")
    public void publish(@RequireRole @RequestParam long projectId,
            @RequestParam long taskId,
            @RequestBody NpmPublishParams params) {

        UserModel user = UserUtils.getCurrentUser();
        ProjectModel project = projectService.getNeverNull(projectId);
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        npmService.publish(project, task, user, params.getMessage());
    }

    @GetMapping(params = "info=versions")
    public NpmVersions listNpmVersions(@RequestParam long projectId, @RequestParam long taskId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        return npmService.listNpmVersions(project, task);
    }

 }
