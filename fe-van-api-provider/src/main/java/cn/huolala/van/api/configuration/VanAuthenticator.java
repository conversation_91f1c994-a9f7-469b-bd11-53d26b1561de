package cn.huolala.van.api.configuration;

import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.service.UserService;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.lalamove.infra.sso.SsoClient;
import com.lalamove.infra.sso.SsoTicket;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Component
public class VanAuthenticator {
    @Autowired
    private SsoClient ssoClient;

    @Autowired
    private UserService userService;

    @Value("${van.auth.jwt.secret}")
    private String jwtSecret;

    @Value("${van.auth.cookieName}")
    private String vanTokenCookieName;

    @Nullable
    private String vanAuth(@Nullable Cookie[] cookies) {
        JWTVerifier verifier = JWT.require(Algorithm.HMAC256(jwtSecret)).build();
        return Optional.ofNullable(cookies)
                .map(Arrays::stream)
                .orElseGet(Stream::empty)
                // Get all cookies where the name equals vanTokenCookieName.
                // Normally, cookie names are unique, but in some cases, such as same name in different domains, they can be multiple.
                .filter(i -> i.getName().equals(vanTokenCookieName))
                .map(Cookie::getValue)
                .filter(StringUtils::isNotBlank)
                // Verify each token in order, and find the first successful one.
                .map(token -> {
                    try {
                        DecodedJWT decodedJWT = verifier.verify(token);
                        Claim claim = decodedJWT.getClaim("user");
                        if (claim.isMissing()) return null;
                        Object account = claim.asMap().get("uniq_id");
                        if (account == null) return null;
                        return account.toString();
                    } catch (JWTVerificationException ignored) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .findFirst().orElse(null);
    }

    /**
     * This method does not assert the result and returns null if nothing can be authenticated.
     */
    @Nullable
    public UserModel authenticate(HttpServletRequest httpServletRequest) {
        String uniqId = Optional
                // Attempt to use SSO authentication first.
                .ofNullable(ssoClient.getTicket(httpServletRequest))
                .map(SsoTicket::getAccount)
                // Otherwise, use Van authentication.
                .orElseGet(() -> vanAuth(httpServletRequest.getCookies()));

        return userService.getByUniqId(uniqId);
    }
}
