package cn.huolala.van.api.controller.dto;

import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CanaryAuditLogMeta {
    @Nullable
    @JsonProperty("defaultTaskID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long defaultTaskId;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Region idc;

    @Nullable
    @JsonProperty("taskID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Collection<Long> taskIds;

    public CanaryAuditLogMeta(@NonNull CanaryRecord canaryRecord) {
        this.idc = canaryRecord.getRegion();
        LegacyDeployConfig config = canaryRecord.getCanary();
        if (config == null) return;
        this.defaultTaskId = config.getDefaultTaskId();
        this.taskIds = config.collectTaskIds();
    }
}
