package cn.huolala.van.api.controller.unsafe;

import cn.huolala.arch.hermes.common.util.JsonUtils;
import cn.huolala.van.api.controller.unsafe.dto.PageServerAuthRequest;
import cn.huolala.van.api.controller.unsafe.dto.PageServerAuthResponse;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.InternalRequestException;
import com.alibaba.druid.support.json.JSONUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lalamove.infra.sso.SsoClient;
import com.lalamove.infra.sso.SsoTicket;
import com.lalamove.infra.sso.configuration.SsoProperties;
import com.lalamove.infra.sso.utils.SignatureUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/sso/proxy")
public class SSOProxyController {

    private static final String INJECT_CONTENT = "<script>window.$vanSSO = {\"user\": %s}</script>";
    private static final String EMPTY_CONTENT = "<script>console.log('sso verify failed, ignore error');</script>";
    @Autowired
    private SsoClient ssoClient;
    @Value("#{'${sso.proxy.callback.whiteList}'.split(',')}")
    private List<String> callbackWhiteList;

    private Map<String, String> ssoHost;
    private Map<String, String> ssoCallbackHost;

    @Value("${sso.proxy.defaultSSOHost}")
    private String defaultSSOHost;

    @Value("${sso.proxy.callback}")
    private String callbackURL;
    @Value("${sso.proxy.hostKey:x-gear-original-host}")
    private String hostKey;
    @Autowired
    private SsoProperties ssoProperties;
    @Autowired
    private ObjectMapper mapper;

    @Value("${sso.proxy.ssoHost}")
    public void setSsoHost(final String ssoHost) throws JsonProcessingException {
        TypeReference<Map<String, String>> typeReference = new TypeReference<Map<String, String>>() {
        };
        this.ssoHost = JsonUtils.deserialize(ssoHost, typeReference);
    }

    @Value("${sso.proxy.ssoCallbackHost}")
    public void setSsoCallbackHost(final String hosts) throws JsonProcessingException {
        TypeReference<Map<String, String>> typeReference = new TypeReference<Map<String, String>>() {
        };
        this.ssoCallbackHost = JsonUtils.deserialize(hosts, typeReference);
    }

    private String getSSOHostByHost(String host) {
        if (StringUtils.isEmpty(host)) {
            return defaultSSOHost;
        }
        return ssoHost.entrySet().
                stream().
                filter(item -> host.endsWith(item.getKey())). // 如果 suffix 有重叠，就可能不是预期的结果
                findFirst().
                map(Map.Entry::getValue).
                orElse(defaultSSOHost);
    }

    private String getSSOCallbackHostByHost(String host) {
        if (StringUtils.isEmpty(host)) {
            return callbackURL;
        }

        return ssoCallbackHost.entrySet().
                stream().
                filter(item -> host.endsWith(item.getKey())). // 如果 suffix 有重叠，就可能不是预期的结果
                findFirst().
                map(Map.Entry::getValue).
                orElse(callbackURL);
    }

    private String getLoginUrl(String redirectUrl, String incomingHost) throws UnsupportedEncodingException {
        long timestamp = System.currentTimeMillis() / 1000;

        TreeMap<String, Object> signParam = new TreeMap<>();
        signParam.put("callback", redirectUrl);
        signParam.put("appid", ssoProperties.getAppId());
        signParam.put("_t", timestamp);
        if (StringUtils.hasText(ssoProperties.getAppNameCn())) {
            signParam.put("show_name_cn", ssoProperties.getAppNameCn());
        }
        if (StringUtils.hasText(ssoProperties.getAppNameEn())) {
            signParam.put("show_name_en", ssoProperties.getAppNameEn());
        }

        String signature = SignatureUtils.sign(signParam, ssoProperties.getAppSecret());


        // 传递的callback参数需要URLEncode.encode
        signParam.put("callback", URLEncoder.encode(redirectUrl, StandardCharsets.UTF_8.name()));
        StringBuilder ssoUri = new StringBuilder(getSSOHostByHost(incomingHost)).append("/login?");
        signParam.forEach((k, v) -> ssoUri.append(k).append("=").append(v).append("&"));

        return ssoUri.append("_sign=").append(signature).toString();
    }

    /**
     * Create a login response with login url.
     * it will redirect to the login page.
     *
     * @return Login response.
     */
    private PageServerAuthResponse createLoginResponse(PageServerAuthRequest request) throws UnsupportedEncodingException {
        String host = request.getHeaders().get(hostKey);

        // 理论上没有非 HTTPS 了
        String next = "https://" + host + request.getPath() + request.getQuery();

        PageServerAuthResponse response = new PageServerAuthResponse();
        response.setType(PageServerAuthResponse.Type.REDIRECT);
        response.setStatus(302);
        String base64Next = Base64.getEncoder().encodeToString(next.getBytes());

        String callbackURL = getSSOCallbackHostByHost(host);
        response.setUrl(getLoginUrl(callbackURL + "/sso/proxy/page_server_auth_callback" + "?next=" + URLEncoder.encode(base64Next, StandardCharsets.UTF_8.name()), host));
        return response;
    }

    /**
     * Create an inject response with inject content.
     *
     * @param ticket SSO ticket.
     * @return Inject response.
     */
    private PageServerAuthResponse createInjectResponse(SsoTicket ticket) {
        PageServerAuthResponse response = new PageServerAuthResponse();
        InjectUser user = new InjectUser();
        user.setAccount(ticket.getAccount());
        user.setHllerId(ticket.getAccountId());
        user.setUsername(ticket.getUserName());

        String content;
        try {
            content = String.format(INJECT_CONTENT, mapper.writeValueAsString(user));
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }

        response.setContent(content);
        response.setType(PageServerAuthResponse.Type.INJECT);

        return response;
    }

    PageServerAuthResponse createEmptyResponse() {
        PageServerAuthResponse response = new PageServerAuthResponse();
        response.setContent(EMPTY_CONTENT);
        response.setType(PageServerAuthResponse.Type.INJECT);
        return response;
    }

    @PostMapping("/page_server_auth")
    public PageServerAuthResponse pageServerAuth(
            @RequestBody PageServerAuthRequest request,
            @RequestParam(required = false) boolean ignoreError
    ) throws IOException {
        String token = request.getToken();
        if (token == null) {
            if (ignoreError) {
                return createEmptyResponse();
            }
            return createLoginResponse(request);
        }

        SsoTicket ticket = ssoClient.getTicket(token);
        if (ticket == null) {
            if (ignoreError) {
                return createEmptyResponse();
            }
            return createLoginResponse(request);
        }

        return createInjectResponse(ticket);
    }

    @GetMapping("/page_server_auth_callback")
    public void pageServerAuthCallback(
            @RequestParam String next,
            HttpServletResponse response
    ) throws IOException {
        byte[] decodeNext = Base64.getDecoder().decode(next);
        String nextUrl = new String(decodeNext);
        URL url = new URL(nextUrl);
        Set<String> collect = callbackWhiteList.stream().filter(u -> {
            if (u.startsWith(".")) {
                return url.getHost().endsWith(u);
            }
            return url.getHost().endsWith("." + u);
        }).collect(Collectors.toSet());
        if (collect.isEmpty()) {
            throw new InternalRequestException("Invalid next url");
        }
        response.sendRedirect(nextUrl);
    }

    @Data
    static class InjectUser {
        private String account;
        private String hllerId;
        private String username;
    }
}
