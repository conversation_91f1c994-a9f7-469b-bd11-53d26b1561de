package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.controller.dto.FightRecordEnum;
import cn.huolala.van.api.controller.dto.MedalRanking;
import cn.huolala.van.api.controller.dto.MyMedal;
import cn.huolala.van.api.dao.projection.MedalRankingProjection;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.model.AuditLog;
import cn.huolala.van.api.model.PageResult;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.service.UserLogService;
import cn.huolala.van.api.service.UserMedalService;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.StreamUtils;
import cn.huolala.van.api.util.UserUtils;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/medal")
@Validated
public class MedalController {
    @Autowired
    private UserMedalService userMedalService;
    @Autowired
    private UserLogService userLogService;
    @Autowired
    private UserService userService;

    @GetMapping(params = "info=myMedals")
    public List<MyMedal> getMyMedals() {
        UserModel user = UserUtils.getCurrentUser();
        return userMedalService.list(user.getId()).stream().map(MyMedal::new).collect(Collectors.toList());
    }

    @GetMapping(params = "info=coverage")
    public double getCoverage(@RequestParam MedalEnum type) {
        long totalUsers = userService.getTotalUserCount();
        if (totalUsers == 0) return 0;
        long received = userMedalService.countMedalReceived(type);
        return (double) received / totalUsers;
    }

    @RequiredSuperAdmin
    @PostMapping(params = "action=sudoAdd")
    public Long sudoAdd(@RequestParam Long userId, @RequestParam MedalEnum type, @RequestParam String remark) {
        return userMedalService.add(userId, type, remark);
    }

    /**
     * TODO: Add type restriction.
     */
    @PostMapping(params = "action=log")
    public Long addLog(
            @RequestParam UserLogType type,
            @RequestParam Long projectId,
            @RequestParam(required = false) String description,
            @RequestBody(required = false) Object meta
    ) {
        UserModel user = UserUtils.getCurrentUser();
        AuditLog log = new AuditLog(user.getId(), projectId, type, description, meta);
        return userLogService.addLogs(Collections.singleton(log));
    }

    @PostMapping(params = "info=ranking")
    public PageResult<MedalRanking> getRanking(
            @ApiParam("A zero-based page index")
            @RequestBody Collection<String> excludeUsers,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Page<MedalRankingProjection> pmr = userMedalService.rank(excludeUsers, page, size);
        List<MedalRanking> list = pmr.getContent().stream().map(MedalRanking::new).collect(Collectors.toList());
        return new PageResult<>(list, pmr.getTotalElements());
    }

    @GetMapping(params = "info=myFightRecord")
    public Map<FightRecordEnum, Integer> getMyFightRecord() {
        UserModel user = UserUtils.getCurrentUser();
        return Arrays.stream(FightRecordEnum.values()).parallel()
                .map(i -> Pair.of(i, i.countByUser(user)))
                .collect(StreamUtils.toMap());
    }
}

