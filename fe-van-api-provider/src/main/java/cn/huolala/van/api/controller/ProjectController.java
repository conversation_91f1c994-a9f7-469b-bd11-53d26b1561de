package cn.huolala.van.api.controller;

import cn.huolala.api.constants.XLEnv;
import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.annotation.RequireAdminRole;
import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.controller.dto.LegacySsoServiceConfig;
import cn.huolala.van.api.exception.*;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.facade.model.UserInfoDTO;
import cn.huolala.van.api.facade.service.feishu.dto.FeishuChatDTO;
import cn.huolala.van.api.facade.service.feishu.dto.FeishuSubscriptionDTO;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.events.ProjectEventListener;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.facade.model.gitlab.GitlabBranch;
import cn.huolala.van.api.facade.model.gitlab.GitlabCompare;
import cn.huolala.van.api.facade.model.gitlab.GitlabMergeRequest;
import cn.huolala.van.api.facade.model.gitlab.GitlabProject;
import cn.huolala.van.api.model.meta.*;
import cn.huolala.van.api.model.project.ProjectBoilerplate;
import cn.huolala.van.api.model.project.ProjectConfig;
import cn.huolala.van.api.model.project.ProjectConfig.Cache;
import cn.huolala.van.api.model.project.ProjectConfig.MultiBuildScript;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.project.ProjectSummary;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.EncodingUtils;
import cn.huolala.van.api.util.FeishuUtils;
import cn.huolala.van.api.util.ModelUtils;
import cn.huolala.van.api.util.StorageHelper;
import cn.huolala.van.api.util.UserUtils;
import cn.lalaframework.logging.LoggerFactory;
import cn.lalaframework.storage.adapter.Storage;
import cn.lalaframework.tools.thread.ThreadUtil;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.hc.core5.http.ContentType;
import org.slf4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.api.constants.enums.MetaType.*;
import static cn.huolala.van.api.util.AuditLogUtil.addAuditLog;
import static cn.huolala.van.api.util.AuditLogUtil.addAuditLogForConfigChange;
import static cn.huolala.van.api.util.MocNotificationUtil.notifyMocForProjectConfigChange;
import static cn.huolala.van.api.util.MocNotificationUtil.notifyMocForSwitchChange;
import static cn.huolala.van.api.util.UserUtils.getCurrentUser;
import static org.apache.commons.lang3.ObjectUtils.firstNonNull;

@RestController
@RequestMapping("/api/project")
@Validated
public class ProjectController {

    private static final Logger LOGGER = LoggerFactory.getLogger();

    private final String internationalDomain = "lalamove.com";
    @Value("${van.repository.gitlab}")
    private String gitlabUrl;
    @Value("${van.repository.official_group:group-van}")
    private String gitlabGroup;
    @Value("${van.repository.webhook:http://gitlab.van.hll.hui.lu:24359/trigger/repository}")
    private String gitlabWebhookUrl;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private MetaService metaService;
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private LoneService loneService;
    @Autowired
    private GitlabService gitlabService;
    @Autowired
    private UserService userService;
    @Autowired
    private EventService eventService;
    @Autowired
    private PmisService pmisService;
    @Autowired
    private UserLogService userLogService;
    @Autowired
    private BuildTaskService buildTaskService;

    /**
     * @deprecated Use `search` instead.
     */
    @PostMapping(value = "info=search")
    @Deprecated
    @SuppressWarnings("java:S1133")
    public void deprecatedSearch(HttpServletResponse res, HttpServletRequest req) {
        res.setStatus(HttpStatus.PERMANENT_REDIRECT.value());
        res.addHeader("Location", "../project?info=search");
    }

    @GetMapping(params = "info=boilerplate")
    public List<ProjectBoilerplate> getBoilerplate(@RequestParam String type) {
        String typ = StringUtils.hasText(type) ? type : "project";
        int boilerplateGroupId = 1724;
        return projectService.getBoilerplateList(typ, boilerplateGroupId);
    }

    @PostMapping(params = "action=verifyName")
    public boolean verifyName(@NonNull @RequestParam String name) {
        return projectService.validateName(name, UserUtils.getCurrentUser());
    }

    @PostMapping(params = "action=verifyRepository")
    public boolean verifyRepository(@NonNull @RequestParam String repositoryUrl) {
        return projectService.validateRepository(repositoryUrl, UserUtils.getCurrentUser());
    }

    @PostMapping(params = "action=create")
    public Long create(@NonNull @RequestBody ProjectCreateParams params) {
        UserModel user = UserUtils.getCurrentUser();
        // create
        long projectId = projectService.create(params, user);
        String appId = params.getConfig().getAppId();

        // update van project config, in all supported regions
        boolean isInternational = Objects.equals(params.getConfig().getDevDomain(), internationalDomain);
        Stream<Region> stream = Stream.of(Region.cn);
        if (isInternational) {
            stream = Arrays.stream(Region.values());
        }
        stream.parallel().forEach(r -> {
            LOGGER.info("update VanProjectConfig in region {}", r);
            projectService.partialUpdateVanProjectConfig(projectId, r, c -> {
                c.setAppid(appId);
                c.setInternational(isInternational);
                c.setWorkers(Objects.equals(params.getType(), ProjectType.Workers));
            });
        });
        // sync users from lone
        projectService.pullUsersFromLone(projectId, appId);

        Stream.<Runnable>of(
                () -> {
                    // add user log
                    userLogService.addLog(user.getId(), projectId, UserLogType.CREATE_PROJECT, "", null);
                },
                () -> {
                    // xl don't send feishu card
                    if (!XLEnv.isXL()) {
                        FeishuCard card = FeishuUtils.buildCreateProjectCard(params.getName(), params.getType());
                        feishuService.sendCardToUser(card, user.getUniqId());
                    }
                }).forEach(CompletableFuture::runAsync);
        return projectId;
    }

    @PostMapping(params = "info=search")
    public PageResult<ProjectSummary> search(@RequestBody ProjectSearchParams params) {
        return projectService.searchForUser(getCurrentUser(), params);
    }

    @PostMapping(params = "info=find")
    public List<Optional<ProjectSummary>> find(@RequestParam(defaultValue = "[]") List<Long> ids) {
        return projectService.findForUser(getCurrentUser(), new HashSet<>(ids)).stream()
            .collect(ModelUtils.toAlignedList(ids, ProjectSummary::getId));
    }

    @GetMapping(value = "/{projectId}")
    public ProjectModel get(@RequireRole @PathVariable long projectId) {
        return projectService.getNeverNull(projectId);
    }

    @GetMapping(value = "/{projectId}/van.json")
    public VanProjectConfig getVanConfig(@RequireRole @PathVariable long projectId,
                                         @RequestParam(required = false) Region region) {
        if (region == null) region = Region.defaultRegion();
        return projectService.getVanProjectConfig(region, projectId);
    }

    /**
     * @deprecated In page server, use snippet to implement the sso service.
     */
    @GetMapping(value = "/{projectId}/config", params = "info=legacySsoService")
    public Map<Env, LegacySsoServiceConfig> getLegacySsoServiceConfig(@PathVariable long projectId,
                                                                      @RequestParam Set<Env> envs) {
        final ProjectModel project = projectService.getNeverNull(projectId);
        final Storage storage = Region.defaultStorage();
        return envs.parallelStream().map(env -> {
            String key = LegacySsoServiceConfig.buildPath(project.getName(), env);
            LegacySsoServiceConfig config = storage.getOptionalValue(key, LegacySsoServiceConfig.class).orElse(null);
            if (config == null) return null;
            return Pair.of(env, config);
        }).filter(Objects::nonNull).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateLegacySsoService")
    public void updateLegacySsoServiceConfig(@RequireAdminRole @PathVariable long projectId,
                                             @RequestBody Map<Env, LegacySsoServiceConfig> map) {
        ProjectModel project = projectService.getNeverNull(projectId);
        final Storage storage = Region.defaultStorage();
        map.entrySet().parallelStream().map(e -> {
                Env env = e.getKey();
                String key = LegacySsoServiceConfig.buildPath(project.getName(), env);
                LegacySsoServiceConfig newConfig = e.getValue();
                Change<LegacySsoServiceConfig> change = StorageHelper.partialUpdate(storage, key,
                    LegacySsoServiceConfig.class, c -> {
                        if (c == null) c = new LegacySsoServiceConfig();
                        c.setIgnoreError(newConfig.isIgnoreError());
                        c.setEnable(newConfig.isEnable());
                        return c;
                    });
                return Pair.of(env, change);
            })

            // Collects all result into the worker thread,
            // because the AuditLog and MocNotification utils can only be used in the worker thread.
            .collect(Collectors.toList())

            // Add AuditLog and MocNotification.
            .forEach(e -> {
                Env env = e.getKey();
                Change<LegacySsoServiceConfig> change = e.getValue();

                Change<Boolean> eChange = change.map(i -> i != null && i.isEnable());
                Change<Boolean> iChange = change.map(i -> i == null || i.isIgnoreError());

                if (eChange.isChanged()) {
                    addAuditLog(projectId, UserLogType.SERVICE_SSO_INJECT).put("env", env).putObject(change.getAfter());
                    notifyMocForSwitchChange(project, env, Region.cn, "SSO Inject", eChange.getAfterOrElse(false));
                }

                if (iChange.isChanged()) {
                    addAuditLog(projectId, UserLogType.SERVICE_SSO_GUARD).put("env", env).putObject(change.getAfter());
                    notifyMocForSwitchChange(project, env, Region.cn, "SSO Guard",
                        Boolean.FALSE.equals(iChange.getAfter()));
                }
            });
    }

    @GetMapping(value = "/{projectId}/config", params = "info=buildCacheStatus")
    @Nullable
    public Map<String, BuildCacheInfo> getBuildCacheStatus(@RequireRole @PathVariable long projectId) {
        return projectService.getBuildCacheStatus(projectId);
    }

    @GetMapping(value = "/{projectId}/config", params = "info=notificationFilter")
    public FeishuNotificationConfigurationModel getNotificationFilter(@RequireRole @PathVariable long projectId) {
        return metaService.getValue(VanFeishuNotificationFilterConfiguration, projectId,
            FeishuNotificationConfigurationModel.class);
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateNotificationFilter")
    public void updateNotificationFilter(@RequireAdminRole @PathVariable long projectId,
                                         @RequestBody FeishuNotificationConfigurationModel model) {
        metaService.setValue(VanFeishuNotificationFilterConfiguration, projectId,
                FeishuNotificationConfigurationModel.class, model)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "prodRelease"));
    }

    @GetMapping(value = "/{projectId}/config", params = "info=miniprogramInfo")
    @Nullable
    public MiniprogramInfoModel getMiniprogramInfo(@RequireRole @PathVariable long projectId) {
        MiniprogramInfoModel model = metaService.getValue(MiniprogramInfo, projectId, MiniprogramInfoModel.class);
        if (model != null) model.encrypt();
        return model;
    }

    @GetMapping(value = "/{projectId}/config", params = "info=prodRelease")
    @Nullable
    public ProjectProdReleaseConfigModel getProdReleaseConfig(@RequireRole @PathVariable long projectId) {
        return metaService.getValue(ProjectProdReleaseConfig, projectId, ProjectProdReleaseConfigModel.class);
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateProdRelease")
    public void updateProdReleaseConfig(@RequireAdminRole @PathVariable long projectId,
                                        @RequestBody ProjectProdReleaseConfigModel data) {
        metaService.setValue(ProjectProdReleaseConfig, projectId, ProjectProdReleaseConfigModel.class, data)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "prodRelease"));
    }

    @GetMapping(value = "/{projectId}/config", params = "info=deployHook")
    public DeployHookModel getDeployHookConfig(@RequireRole @PathVariable long projectId) {
        return projectService.getDeployHookConfig(projectId);
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateDeployHook")
    public void updateDeployHookConfig(@RequireAdminRole @PathVariable long projectId,
                                       @RequestBody DeployHookModel data) {
        projectService.updateDeployHookConfig(projectId, data)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "deployHook"));
    }

    @GetMapping(value = "/{projectId}/config", params = "info=webHook")
    @Nullable
    public WebHookModel getWebHookConfig(@RequireRole @PathVariable long projectId) {
        return metaService.getValue(ProjectWebhookConfiguration, projectId, WebHookModel.class);
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateWebHook")
    public void updateWebHookConfig(@RequireAdminRole @PathVariable long projectId, @RequestBody WebHookModel data) {
        metaService.setValue(ProjectWebhookConfiguration, projectId, WebHookModel.class, data)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "webHook"));
    }

    @NonNull
    @GetMapping(value = "/{projectId}/config", params = "info=feishuSubscriptions")
    public List<FeishuSubscriptionDTO> getFeishuSubscriptions(@RequireRole @PathVariable long projectId) {
        return feishuService.findSubscriptions(projectId).stream().map(model -> {
            FeishuSubscriptionDTO feishuSubscriptionDTO = new FeishuSubscriptionDTO();
            BeanUtils.copyProperties(model, feishuSubscriptionDTO);

            UserInfoDTO userInfoDTO = new UserInfoDTO();
            BeanUtils.copyProperties(model.getUser(), userInfoDTO);
            feishuSubscriptionDTO.setUser(userInfoDTO);

            FeishuChatDTO feishuChatDTO = new FeishuChatDTO();
            BeanUtils.copyProperties(model.getChat(), feishuChatDTO);
            feishuSubscriptionDTO.setFeishuChat(feishuChatDTO);

            return feishuSubscriptionDTO;
        }).collect(Collectors.toList());
    }

    @PostMapping(value = "/utils/findIdByNames")
    public List<Optional<Long>> findIdByNames(@RequestParam(defaultValue = "[]") List<String> names) {
        return ModelUtils.<String, Long>alignToList(names).apply(projectService.findIdByNames(names));
    }

    @PostMapping(value = "/utils/findLoneAppIds")
    public List<Optional<String>> findLoneAppIds(@RequestParam(defaultValue = "[]") List<Long> ids) {
        return projectService.find(new HashSet<>(ids)).stream().map(i -> Pair.of(i.getId(), i.getConfig().getAppId()))
            .collect(ModelUtils.toAlignedList(ids));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateBuildScript")
    void updateBuildScript(@RequireAdminRole @PathVariable long projectId, @RequestParam String buildScript) {
        projectService.partialUpdateConfig(projectId, c -> c.setBuildScript(buildScript))
            .mapIfNonNull(ProjectConfig::getBuildScript)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "buildScript"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateMultiBuildScript")
    void updateMultiBuildScript(@RequireAdminRole @PathVariable long projectId,
                                @RequestBody List<MultiBuildScript> multiBuildScript) {
        projectService.partialUpdateConfig(projectId, c -> c.setMultiBuildScript(multiBuildScript))
            .map(ProjectConfig::getMultiBuildScript)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "multiBuildScript"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateBuildImage")
    void updateBuildImage(@RequireAdminRole @PathVariable long projectId, @RequestParam String buildImage) {
        if (!buildImage.startsWith("node:")) {
            throw new VanBadRequestException("The build image must starts with 'node:'");
        }

        projectService.partialUpdateConfig(projectId, c -> c.setBuildImage(buildImage))
            .mapIfNonNull(ProjectConfig::getBuildImage)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "buildImage"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateMessageConfig")
    void updateMessageConfig(@RequireAdminRole @PathVariable long projectId,
                             @RequestBody ProjectConfig.MessageConfig value) {
        value.validate();

        projectService.partialUpdateConfig(projectId, c -> c.setMessageConfig(value))
            .mapIfNonNull(ProjectConfig::getMessageConfig)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "messageConfig"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateSkipChecks")
    void updateSkipChecks(@RequireAdminRole @PathVariable long projectId, @RequestParam Boolean value) {
        projectService.partialUpdateConfig(projectId, c -> c.setSkipChecks(value))
            .mapIfNonNull(ProjectConfig::getSkipChecks)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "skipChecks"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateCache")
    void updateCache(@RequireAdminRole @PathVariable long projectId, @RequestParam Boolean value) {
        ProjectModel project = projectService.getNeverNull(projectId);
        Cache after = project.getConfig().getCache();
        if (after == null) {
            after = new Cache();
        }
        after.setEnable(value);
        Cache finalAfter = after;
        projectService.partialUpdateConfig(projectId, c -> c.setCache(finalAfter))
            .mapIfNonNull(ProjectConfig::getCache)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "cache"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=fixCacheStatus")
    void fixCacheStatus(@RequireAdminRole @PathVariable long projectId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        Cache after = project.getConfig().getCache();
        if (after == null) {
            after = new Cache();
        }
        if (!ProjectConfig.TriggeredTask.Status.running.equals(after.getStatus())) {
            throw new BadRequestException("The cache status is not running, cannot fix it");
        }
        Long taskId = after.getTaskId();
        if (taskId != null) {
            Optional<BuildTaskModel> task = buildTaskService.get(projectId, taskId);
            if (task.isPresent() && BuildTaskStatus.Running.equals(task.get().getStatus())) {
                throw new BadRequestException("The cache task is still running, cannot fix it, you should wait for it to finish or cancel it");
            } else if (task.isPresent() && BuildTaskStatus.Ready.equals(task.get().getStatus())) {
                OffsetDateTime tenMinutesAfter = task.get().getCreatedAt().plusMinutes(10);
                if (task.get().getCreatedAt().isAfter(tenMinutesAfter)) {
                    // cancel the task if it is still in the ready state for more than 10 minutes
                    buildTaskService.updateBuildTaskStatus(projectId, taskId, BuildTaskStatus.Failed, BuildTaskStatus.Ready);
                }
            }
        }
        after.setStatus(ProjectConfig.TriggeredTask.Status.failed);
        Cache finalAfter = after;
        projectService.partialUpdateConfig(projectId, c -> c.setCache(finalAfter))
            .mapIfNonNull(ProjectConfig::getCache)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "cacheStatusFixed"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=fixNpmAutoReleaseStatus")
    void fixNpmAutoReleaseStatus(@RequireAdminRole @PathVariable long projectId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        ProjectConfig config = project.getConfig();
        if (config.getNpm() == null) {
            throw new BadRequestException("The project does not have npm auto release configuration");
        }
        if (!ProjectConfig.TriggeredTask.Status.running.equals(config.getNpm().getStatus())) {
            throw new BadRequestException("The npm auto release status is not running, cannot fix it");
        }

        Long taskId = config.getNpm().getTaskId();
        if (taskId != null) {
            Optional<BuildTaskModel> task = buildTaskService.get(projectId, taskId);
            if (task.isPresent() && BuildTaskStatus.Running.equals(task.get().getStatus())) {
                throw new BadRequestException("The npm auto release task is still running, cannot fix it, you should wait for it to finish or cancel it");
            }
        }

        config.getNpm().setStatus(ProjectConfig.TriggeredTask.Status.failed);
        projectService.partialUpdateConfig(projectId, c -> c.setNpm(config.getNpm()))
            .mapIfNonNull(ProjectConfig::getNpm)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "npmAutoReleaseStatusFixed"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateBuildLimiter")
    void updateBuildLimiter(@RequireAdminRole @PathVariable long projectId, @RequestParam ProjectConfig.Build value) {
        projectService.partialUpdateConfig(projectId, c -> c.setBuild(value)).mapIfNonNull(ProjectConfig::getBuild)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "buildLimiter"));
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateAppId")
    void updateAppId(@RequireAdminRole @PathVariable long projectId, @RequestParam String appId) {
        UserModel user = getCurrentUser();

        boolean skipAppIdCheck = userService.isSuperAdmin(user.getUniqId());

        // Check the user permission for AppID if not skip.
        if (!skipAppIdCheck && !loneService.isParticipant(appId, getCurrentUser())) {
            throw new ForbiddenException("You are not a participant of the AppID");
        }

        projectService.partialUpdateConfig(projectId, c -> c.setAppId(appId)).mapIfNonNull(ProjectConfig::getAppId)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "appId"));

        // Only the Region.cn appId needs change.
        projectService.partialUpdateVanProjectConfig(projectId, Region.cn, c -> c.setAppid(appId))
            .mapIfNonNull(VanProjectConfig::getAppid);
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateOffWeb")
    void updateOffWeb(@RequireAdminRole @PathVariable long projectId,
                      @RequestParam boolean status,
                      @RequestParam(required = false) Region region) {
        ProjectModel project = projectService.getNeverNull(projectId);

        if (region == null) region = Region.cn;

        if (region != Region.cn) throw new VanBadRequestException("This operation is only support the cn region");

        projectService.partialUpdateVanProjectConfig(projectId, Region.cn, c -> c.setOffWeb(status));

        addAuditLog(projectId, UserLogType.SERVICE_OFFWEB).put("offweb", status);

        notifyMocForSwitchChange(project, Env.prd, Region.cn, "OffWeb", status);
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateAllowedHosts")
    void updateAllowedHosts(@RequireAdminRole @PathVariable long projectId,
                            @RequestParam List<String> hosts,
                            @RequestParam Region region) {
        ProjectModel project = projectService.getNeverNull(projectId);
        projectService.partialUpdateVanProjectConfig(projectId, region, c -> c.setAllowedHosts(hosts))
            .mapIfNonNull(VanProjectConfig::getAllowedHosts).ifChanged(change -> {
                addAuditLogForConfigChange(projectId, change, "allowedHosts").put("region", region);
                notifyMocForProjectConfigChange(project, Env.prd, region, "allowed hosts");
            });
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateDescription")
    void updateDescription(@RequireAdminRole @PathVariable long projectId, @RequestParam String description) {
        projectService.updateDescription(projectId, description).mapIfNonNull(EncodingUtils::encodeGolangJson)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "description"));

        // Update GitLab description in background.
        ThreadUtil.execute(() -> {
            ProjectModel project = projectService.getNeverNull(projectId);
            gitlabService.updateRepositoryDescription(project.getRepository(), description);
        });
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateMiniprogramPrivateKey")
    void updateMiniprogramPrivateKey(@RequireAdminRole @PathVariable long projectId,
                                     @RequestParam MultipartFile privateKeyFile) {
        ProjectModel project = projectService.getNeverNull(projectId);
        if (project.getType() != ProjectType.Miniprogram) {
            throw new BadRequestException("This is not a Miniprogram project");
        }

        String privateKey;
        try {
            privateKey = StreamUtils.copyToString(privateKeyFile.getInputStream(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }

        metaService.partialUpdateValue(MiniprogramInfo, projectId, MiniprogramInfoModel.class, m -> {
                if (m == null) m = new MiniprogramInfoModel();
                m.setPrivateKey(privateKey);
                return m;
            }).mapIfNonNull(MiniprogramInfoModel::fetchEncryptedPrivateKey)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "miniprogramPrivateKey"));

    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateMiniprogramProjectPath")
    void updateMiniprogramProjectPath(@RequireAdminRole @PathVariable long projectId,
                                      @RequestParam String projectPath) {
        ProjectModel project = projectService.getNeverNull(projectId);

        if (project.getType() != ProjectType.Miniprogram) {
            throw new BadRequestException("This is not a Miniprogram project");
        }

        metaService.partialUpdateValue(MiniprogramInfo, projectId, MiniprogramInfoModel.class, info -> {
                if (info == null) info = new MiniprogramInfoModel();
                info.setProjectPath(projectPath);
                return info;
            }).mapIfNonNull(MiniprogramInfoModel::getProjectPath)
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, "miniprogramProjectPath"));
    }

    @GetMapping(value = "/{projectId}/config", params = "info=hooks")
    public LastModifiedData<VanHook[]> getHooks(@RequireRole @PathVariable long projectId) {
        return metaService.get(projectId, ProjectHookConfiguration)
            .map(m -> new LastModifiedData<>(m.getUpdatedAt(), m.parseMeta(VanHook[].class)))
            .orElseGet(LastModifiedData::new);
    }

    @PutMapping(value = "/{projectId}/config", params = "action=updateHooks")
    public void updateHooks(@RequireAdminRole @PathVariable long projectId,
                            @RequestBody LastModifiedData<VanHook[]> lastModifiedData) {
        metaService.partialUpdate(ProjectHookConfiguration, projectId, m -> {
                // If the passed lm does not match the current lm, it indicates that a conflict has occurred.
                // In this case, there is nothing to do and return false directly.
                OffsetDateTime lm = lastModifiedData.getLastModified();
                OffsetDateTime ua = m.getUpdatedAt();
                if (lm != null && ua != null && lm.toEpochSecond() != ua.toEpochSecond()) {
                    // The transaction will be rolled back after the throw.
                    throw new VanBadRequestException("The LastModified does not matched");
                }
                m.setMeta(MetaModel.serializeMeta(lastModifiedData.getData()));
            }).mapIfNonNull(m -> m.parseMeta(VanHook[].class))
            .ifChanged(change -> addAuditLogForConfigChange(projectId, change, ProjectHookConfiguration.name()));
    }

    @GetMapping(value = "/{projectId}/gitlab", params = "info=branch")
    GitlabBranch getBranchInfo(@RequireRole @PathVariable long projectId, @NotEmpty @RequestParam String branchName) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return gitlabService.getBranchInfo(project.getRepository(), branchName);
    }

    @GetMapping(value = "/{projectId}/gitlab", params = "info=branches")
    @NonNull
    public List<GitlabBranch> searchBranches(@RequireRole @PathVariable long projectId,
                                             @RequestParam(required = false) String keyword) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return gitlabService.searchBranches(project.getRepository(), keyword);
    }

    @GetMapping(value = "/{projectId}/gitlab", params = "info=mergeRequests")
    @NonNull
    public List<GitlabMergeRequest> getMergeRequests(@RequireRole @PathVariable long projectId,
                                                     @ModelAttribute GitlabMergeRequest.SearchParams searchParams) {
        ProjectModel project = projectService.getNeverNull(projectId);
        String repo = project.getRepository();
        return gitlabService.findMergeRequests(repo, searchParams).collect(Collectors.toList());
    }

    @GetMapping(value = "/{projectId}/gitlab", params = "info=repository")
    @NonNull
    public GitlabProject getRepositoryInfo(@RequireRole @PathVariable long projectId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return gitlabService.getProjectInfo(project.getRepository());
    }

    @GetMapping(value = "/{projectId}/gitlab", params = "action=compare")
    @NonNull
    public GitlabCompare gitCompare(@RequireRole @PathVariable long projectId,
                                    @RequestParam String from,
                                    @RequestParam String to) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return gitlabService.compare(project.getRepository(), from, to);
    }

    @GetMapping(value = "/{projectId}/pmis", params = "info=availableReleasePlanList")
    public List<PmisReleasePlan> getAvailableReleasePlanList(@RequireRole @PathVariable long projectId,
                                                             @RequestParam(required = false) Region region) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return pmisService.getAvailableReleasePlanList(project, region);
    }

    @GetMapping(value = "/{projectId}/events")
    void watchEvents(@RequireRole @PathVariable long projectId,
                     @RequestParam(required = false) @Nullable String lastEventId,
                     @NonNull HttpServletRequest req,
                     @NonNull HttpServletResponse res) throws IOException {
        res.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.TEXT_EVENT_STREAM.toString());
        res.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        res.flushBuffer();
        PrintWriter w = res.getWriter();

        lastEventId = firstNonNull(req.getHeader("Last-Event-Id"), lastEventId);
        ProjectEventListener listener = eventService.createListener(projectId, lastEventId);

        for (int i = 0; i < 100; i++) {
            ProjectEvent event = listener.getAndWait();
            if (event == null) {
                w.println("event: ping");
                w.println(String.format("data: \"%s\"", OffsetDateTime.now()));
            } else {
                w.println(String.format("id: %s", listener.getLastEventId()));
                w.println(String.format("data: %s", event));
            }
            w.println("");
            try {
                res.flushBuffer();
            } catch (IOException e) {
                // Ignore broken pipe exception.
                w.close();
                break;
            }
        }
    }

    @DeleteMapping(value = "/{projectId}/repository", params = "action=archive")
    public int archiveProject(@RequireAdminRole @PathVariable long projectId) {
        return projectService.archive(projectId);
    }

    @GetMapping(value = "/{projectId}/repository", params = "info=raw_file")
    public ResponseEntity<String> getRepositoryRawFile(@PathVariable long projectId, @RequestParam String path,
            @RequestParam String ref) {
        if (path == null || path.isEmpty()) {
            throw new VanBadRequestException("path cannot be empty");
        }
        return projectService.getRawFile(projectId, path, ref);
    }

    @PostMapping(value = "/{projectId}/apply_domain")
    public String applyDomain(@RequireRole @PathVariable long projectId, @RequestBody ApplyDomainParams params) {
        UserModel user = getCurrentUser();
        ProjectModel project = projectService.getNeverNull(projectId);
        // create feishu approval
        return projectService.applyDomain(project, user, params.getDomainValue(), params.getDomainType(),
                params.getRemark());
    }

    @PostMapping(value = "/{projectId}/merge_requests", params = "action=rebase")
    public void rebaseMergeRequest(@PathVariable long projectId, @RequestParam long iid) {
        UserModel user = UserUtils.getCurrentUser();
        ProjectModel project = projectService.getNeverNull(projectId);
        gitlabService.rebaseMergeRequest(project.getRepository(), iid);

        CompletableFuture.runAsync(() -> {
            // add user log
            Map<String, String> meta = new HashMap<>();
            meta.put("iid", String.valueOf(iid));
            userLogService.addLog(user.getId(), projectId, UserLogType.WORKFLOW_REBASE, "", meta);
        });
    }

    @PostMapping(value = "/{projectId}/merge_requests", params = "action=close")
    public GitlabMergeRequest closeMergeRequest(@PathVariable long projectId, @RequestParam long iid) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return gitlabService.closeMergeRequest(project.getRepository(), iid);
    }

    @PostMapping(value = "/{projectId}/merge_requests", params = "action=direct_mr")
    public long directMergeRequest(@PathVariable long projectId, @RequestBody DirectMrParams params) {
        ProjectModel project = projectService.getNeverNull(projectId);
        GitlabMergeRequest mr = gitlabService.directMergeRequest(project.getRepository(), params.getBranch(),
                params.getRef(), params.getDescription());
        if (mr == null) {
            throw new InternalException("failed to create direct mr");
        }
        return mr.getIid();
    }

    @PostMapping(value = "/{projectId}/merge_requests", params = "action=merge")
    public String mergeMergeRequest(@PathVariable long projectId, @NonNull @RequestParam long iid,
            @Nullable @RequestBody MergeMrParams params) {
        UserModel user = UserUtils.getCurrentUser();
        ProjectModel project = projectService.getNeverNull(projectId);
        return projectService.mergeRequest(project, user, iid, params);
    }

    @PostMapping(value = "/{projectId}/branches", params = "action=prune_temporary")
    public void pruneTemporaryBranches(@RequireRole @PathVariable long projectId) {
        ProjectModel project = projectService.getNeverNull(projectId);
        gitlabService.pruneTemporaryBranches(project.getRepository());
    }

    @PostMapping(value = "/{projectId}/branches", params = "action=modify_lock_file")
    public GitlabMergeRequest modifyLockFile(@RequireRole @PathVariable long projectId,
            @Nullable @RequestParam String branch) {
        ProjectModel project = projectService.getNeverNull(projectId);
        return gitlabService.modifyLockFile(project, branch);
    }

    @GetMapping(value = "/{projectId}/workers", params = "info=find")
    public MetaModel getWorkerSDKPublishInfo(@PathVariable long projectId, @RequestParam long taskId) {
        return projectService.getWorkerPublishInfo(projectId, taskId);
    }

    @PostMapping(value = "/{projectId}/workers", params = "action=publish")
    public long publishWorker(@PathVariable long projectId, @RequestParam long taskId) {
        UserModel user = UserUtils.getCurrentUser();
        return projectService.publishWorker(projectId, taskId, user);
    }

    @PostMapping(value = "/{projectId}/config", params = "action=updateRepository")
    public void updateRepository(@RequireAdminRole @PathVariable long projectId, @RequestParam String repository) {
        projectService.updateRepository(projectId, repository);
    }


    @GetMapping(params = "info=findByName")
    public ProjectModel getByName(@RequestParam String projectName) {
        return projectService.getNeverNull(new IdOrName(projectName));
    }
}
