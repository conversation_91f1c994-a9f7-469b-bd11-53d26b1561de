package cn.huolala.van.api.provider;

import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.facade.service.UserLogService;
import cn.huolala.van.api.facade.service.dto.AddUserLogRequestDTO;
import cn.huolala.van.api.model.AuditLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;

@HermesService("/van/user_log")
@Service
public class UserLogServiceSOAImpl implements UserLogService {

    @Autowired
    private cn.huolala.van.api.service.UserLogService userLogService;

    @Override
    public Long add(AddUserLogRequestDTO o) {
        AuditLog log = new AuditLog(o.getUserId(), o.getProjectId(), o.getType(), o.getDescription(), o.getMeta());
        return userLogService.addLogs(Collections.singleton(log));
    }
}
