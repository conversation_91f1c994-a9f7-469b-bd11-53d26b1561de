package cn.huolala.van.api.provider;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.arch.hermes.api.annotation.HermesService;
import cn.huolala.van.api.facade.model.BuildTaskDTO;
import cn.huolala.van.api.facade.model.GetBuildTasksParams;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.facade.service.BuilderService;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.util.TaskUtils;

import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@HermesService("/van/builder")
@Service
public class BuilderServiceImpl implements BuilderService {
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private ProjectService projectService;

    @Override
    public long createBuildTask(BuildTaskDTO task) {
        return buildTaskService.createBuildTask(task);
    }

    @Nullable
    @Override
    public BuildTaskDTO getBuildTask(long projectId, long taskId) {
        return buildTaskService.get(projectId, taskId).map(TaskUtils::buildTaskModelToDTO).orElse(null);
    }

    /**
     * @param prevStatus Verify the previous status. If it does not match, throw a CONFLICT exception.
     *                   NOTE: If a null value is provided, that indicates nothing would be verified.
     */
    @Override
    public void updateBuildTaskStatus(long projectId, long taskId,
                                      @NonNull BuildTaskStatus status,
                                      @Nullable BuildTaskStatus prevStatus) {
        buildTaskService.updateBuildTaskStatus(projectId, taskId, status, prevStatus);
    }

    @Override
    public void updateBuildTaskBuildId(long projectId, long taskId, @Nullable String buildId) {
        buildTaskService.updateBuildTaskBuildId(projectId, taskId, buildId);
    }

    @Override
    public void updateBuildTaskBranch(long projectId, long taskId, String branch) {
        buildTaskService.updateBuildTaskBranch(projectId, taskId, branch);
    }

    @Override
    public List<BuildTaskDTO> getBuildTasksByIds(GetBuildTasksParams params) {
        ProjectModel project = projectService.getNeverNull(IdOrName.create(params.getProjectName()));
        return buildTaskService.batchGet(project.getId(), new HashSet<>(params.getTaskIds()))
                .map(TaskUtils::buildTaskModelToDTO).collect(Collectors.toList());
    }
}
