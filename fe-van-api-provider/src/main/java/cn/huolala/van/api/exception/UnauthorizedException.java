package cn.huolala.van.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(code = HttpStatus.UNAUTHORIZED)
public class UnauthorizedException extends RuntimeException {
    public UnauthorizedException() {
        super("The current session is unauthorized or expired");
    }

    public UnauthorizedException(String message) {
        super(message);
    }
}
