package cn.huolala.van.api.controller.dto;

import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.HistoryService;
import cn.huolala.van.api.service.ProjectService;
import cn.lalaframework.spring.ApplicationContextUtil;

import java.util.Map;
import java.util.function.Function;

public enum PortalCounter {
    counts(ProjectService.class, ProjectService::countEachTypes),
    builds(BuildTaskService.class, BuildTaskService::countEachTypes),
    releases(HistoryService.class, HistoryService::countCanaryEachTypes),
    canaries(HistoryService.class, HistoryService::countCanaryEachTypesLikeCanary);

    private final Class<?> service;
    private final Function<?, Map<ProjectType, Integer>> method;

    <T> PortalCounter(Class<T> service, Function<T, Map<ProjectType, Integer>> method) {
        this.service = service;
        this.method = method;
    }

    @SuppressWarnings("unchecked")
    public <T> Map<ProjectType, Integer> getData() {
        T instance = ApplicationContextUtil.getApplicationContext().getBean((Class<T>) service);
        return ((Function<T, Map<ProjectType, Integer>>) method).apply(instance);
    }
}
