package cn.huolala.van.api.controller.dto;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.projection.MedalRankingProjection;
import cn.huolala.van.api.model.UserBase;
import lombok.Getter;
import org.springframework.lang.NonNull;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public class MedalRanking extends UserBase {
    @NonNull
    private final List<MedalEnum> medals;

    public MedalRanking(MedalRankingProjection projection) {
        super(projection.getUserUniqId(), projection.getUserName());
        medals = Arrays.stream(projection.getMedals().split(","))
                .map(MedalEnum::valueOf).collect(Collectors.toList());
    }
}
