package cn.huolala.van.api.controller;

import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.model.VanResourceSummary;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.TaskResourceService;
import cn.lalaframework.soa.exception.BusinessException;
import org.apache.commons.io.IOUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MIME;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.AbstractMap.SimpleEntry;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/project/{projectId}/{taskId}")
@Validated
public class TaskResourceController {
    @Autowired
    private TaskResourceService taskResourceService;
    @Autowired
    private BuildTaskService buildTaskService;

    @Nullable
    private static String convertToDataUrl(@Nullable HttpEntity<InputStream> obj) {
        if (obj == null) return null;
        try {
            String type = Optional.ofNullable(obj.getHeaders().getContentType())
                .orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
            InputStream body = obj.getBody();
            String content = body == null ? "" : Base64.getEncoder().encodeToString(IOUtils.toByteArray(body));
            return String.format("data:%s;base64,%s", type, content);
        } catch (IOException e) {
            throw new BusinessException(e);
        }
    }

    @GetMapping(params = "info=resourceList")
    public List<VanResourceSummary> getResourceList(@PathVariable long projectId, @PathVariable long taskId) {
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        return taskResourceService.listTaskResources(task, null);
    }

    @PostMapping(params = "info=resource")
    public Map<String, String> findResource(@PathVariable long projectId,
                                            @PathVariable long taskId,
                                            @RequestParam List<String> paths) {
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        return paths.stream().filter(Objects::nonNull).parallel()
            .map(path -> new SimpleEntry<>(path, getResourceDataUrl(task, path))).filter(i -> i.getValue() != null)
            .collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue));
    }

    @Nullable
    private String getResourceDataUrl(@NonNull BuildTaskModel task, String path) {
        HttpEntity<InputStream> obj = taskResourceService.getTaskResource(task, path);
        return convertToDataUrl(obj);
    }

    @GetMapping(value = "/resource", params = "info=raw")
    public ResponseEntity<?> getRawResource(@PathVariable long projectId,
                                            @PathVariable long taskId,
                                            @RequestParam String path) {
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        HttpEntity<InputStream> entity = taskResourceService.getTaskResource(task, path);
        if (entity == null) {
            return ResponseEntity.status(404).contentType(MediaType.TEXT_PLAIN).body("Not Found");
        } else {
            InputStream body = entity.getBody();
            Objects.requireNonNull(body, "The body must not null here");
            return ResponseEntity.status(200).headers(entity.getHeaders()).body(new InputStreamResource(body));
        }
    }

    @GetMapping(value = "/meta", params = "info=raw")
    public void getRawMeta(@RequireRole @PathVariable long projectId,
                           @PathVariable long taskId,
                           @RequestParam String path,
                           HttpServletResponse res) throws IOException {
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        HttpEntity<InputStream> entity = taskResourceService.getTaskMeta(task, path);
        if (entity == null) {
            res.setStatus(404);
            res.setContentType(ContentType.TEXT_PLAIN.getMimeType());
            res.getWriter().write("Not Found");
        } else {
            res.setStatus(200);
            MediaType type = entity.getHeaders().getContentType();
            if (type != null) res.setHeader(MIME.CONTENT_TYPE, type.toString());
            InputStream body = entity.getBody();
            if (body != null) IOUtils.copy(body, res.getOutputStream());
        }
    }

    @Nullable
    private String getMetaDataUrl(long projectId, long taskId, String path) {
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        HttpEntity<InputStream> entity = taskResourceService.getTaskMeta(task, path);
        return convertToDataUrl(entity);
    }

    @PostMapping(params = "info=meta")
    public Map<String, String> findMeta(@RequireRole @PathVariable long projectId,
                                        @PathVariable long taskId,
                                        @RequestParam List<String> paths) {
        return paths.stream().filter(Objects::nonNull).parallel()
            .map(path -> new SimpleEntry<>(path, getMetaDataUrl(projectId, taskId, path)))
            .filter(i -> i.getValue() != null).collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue));
    }
}
