package cn.huolala.van.api.controller.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
@AllArgsConstructor
public class JsonRpcError {
    private final int code;
    @NonNull
    private final String message;
    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private final Object data;
}
