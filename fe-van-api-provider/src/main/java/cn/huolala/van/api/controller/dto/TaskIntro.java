package cn.huolala.van.api.controller.dto;

import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.CmdbAppRegion;
import cn.huolala.van.api.model.project.ProjectModel;
import com.lark.oapi.service.contact.v3.model.User;
import groovy.lang.Tuple2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Optional;

@Getter
public class TaskIntro {
    @NonNull
    private final Long projectId;

    @ApiModelProperty("A git repository path in format `${group}/${repo}`, such as 'hll-fe/van-web'.\n" +
            "If the project has been archived, this value is reset to an empty string.")
    @NonNull
    private final String repository;

    @Nullable
    private final UserBasic user;

    @NonNull
    private final String branch;
    @NonNull
    private final String commitHash;
    @NonNull
    private final String commitMessage;
    @NonNull
    private final List<Member> projectMembers;

    public TaskIntro(@NonNull ProjectModel project,
                     @NonNull BuildTaskModel task,
                     @Nullable UserBasic user,
                     @NonNull List<Member> projectMembers) {
        this.projectId = project.getId();
        this.repository = project.getRepository();
        this.user = user;
        this.branch = task.getBranch();
        this.commitHash = task.getHash();
        this.commitMessage = task.getCommitMessage();
        this.projectMembers = projectMembers;
    }

    @Getter
    public static class UserBasic {
        @NonNull
        private final String uniqId;
        @NonNull
        private final String name;
        @NonNull
        private final String avatar;

        public UserBasic(@NonNull User user) {
            // The enName may not be empty for LLM users !!!
            uniqId = Optional.of(user.getEnName()).orElseGet(user::getName);
            name = user.getName();
            avatar = user.getAvatar().getAvatar72();
        }
    }

    @Getter
    public static class Member extends UserBasic {
        @NonNull
        private final List<Tuple2<Role, CmdbAppRegion>> roles;

        public Member(@NonNull User user, @NonNull List<Tuple2<Role, CmdbAppRegion>> roles) {
            super(user);
            this.roles = roles;
        }
    }
}
