package cn.huolala.van.api.controller.dto;

import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
@AllArgsConstructor
public class ReleasableTasksDTO {
    @NonNull
    private List<BuildTaskModel> taskList;
    @NonNull
    private Map<Long, String> tagMap;
    @NonNull
    private Map<Long, Optional<CanaryRecord>> lastReleaseMap;
    @NonNull
    private Map<Long, List<DeployRecord>> deployMap;
}
