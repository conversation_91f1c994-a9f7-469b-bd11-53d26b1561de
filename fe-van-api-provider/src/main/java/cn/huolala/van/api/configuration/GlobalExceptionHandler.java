package cn.huolala.van.api.configuration;

import cn.huolala.van.api.annotation.RequireAdminRole;
import cn.huolala.van.api.annotation.RequireRole;
import cn.huolala.van.api.exception.BizErrorCode;
import cn.huolala.van.api.exception.BizException;
import cn.huolala.van.api.exception.UnauthorizedException;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import javax.annotation.Nullable;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.metadata.ConstraintDescriptor;
import java.lang.annotation.Annotation;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@ControllerAdvice("cn.huolala.van.api.controller")
public class GlobalExceptionHandler {

    @Value("${van.oauth.authUrl}")
    private String authUrl;

    /**
     * @deprecated TODO: Don't use BizException
     */
    @Deprecated
    @ExceptionHandler(BizException.class)
    public ResponseEntity<ErrorResponse> handleBizException(BizException e) {
        ErrorResponse errorResponse = new ErrorResponse();
        BeanUtils.copyProperties(e, errorResponse);
        if (BizErrorCode.USER_AUTH_FAILED.getCode().equals(e.getCode())) {
            return ResponseEntity.status(401).body(errorResponse);
        } else if (BizErrorCode.USER_NO_PERMISSION.getCode().equals(e.getCode())) {
            return ResponseEntity.status(403).body(errorResponse);
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    @ExceptionHandler(UnauthorizedException.class)
    public ResponseEntity<StandardErrorResponse> exceptionHandler(UnauthorizedException e) {
        StandardErrorResponse body = new StandardErrorResponse(HttpStatus.UNAUTHORIZED, e.getMessage());
        Map<String, String> meta = new HashMap<>();
        meta.put("login_url", authUrl);
        body.setMeta(meta);

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(body);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<StandardErrorResponse> exceptionHandler(ConstraintViolationException e) {
        HttpStatus status;
        String message;

        ConstraintViolation<?> cv = e.getConstraintViolations().stream()
                .filter(i -> Optional.of(i)
                        .map(ConstraintViolation::getConstraintDescriptor)
                        .map(ConstraintDescriptor::getAnnotation)
                        .map(Annotation::annotationType)
                        .map(a -> a == RequireRole.class || a == RequireAdminRole.class)
                        .orElse(false)
                ).findFirst().orElse(null);
        if (cv != null) {
            status = HttpStatus.FORBIDDEN;
            message = cv.getMessage();
        } else {
            status = HttpStatus.BAD_REQUEST;
            message = e.getMessage();
        }

        return ResponseEntity.status(status).body(new StandardErrorResponse(status, message));
    }

    @Getter
    @Setter
    public static class ErrorResponse {
        private int ret;
        private String code;
        private String msg;
        private Object data;
    }

    @Getter
    @Setter
    public static class StandardErrorResponse {
        @NonNull
        private final Date timestamp;
        @NonNull
        private final Integer status;
        @NonNull
        private final String error;
        @NonNull
        private final String message;

        @Nullable
        private Object meta;

        public StandardErrorResponse(@NonNull HttpStatus status, @NonNull String message) {
            this.timestamp = new Date();
            this.status = status.value();
            this.error = status.getReasonPhrase();
            this.message = message;
            this.meta = null;
        }
    }
}
