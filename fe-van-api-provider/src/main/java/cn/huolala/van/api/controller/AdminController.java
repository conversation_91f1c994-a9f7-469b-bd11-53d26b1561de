package cn.huolala.van.api.controller;

import cn.huolala.api.constants.enums.Env;
import cn.lalaframework.storage.adapter.ListResult;
import cn.lalaframework.storage.adapter.ObjectResult;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.facade.annotation.RequiredSuperAdmin;
import cn.huolala.van.api.model.ProjectAndTaskModel;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserLogModel;
import cn.huolala.van.api.model.UserLogSearchParams;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.StorageHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lark.oapi.service.contact.v3.model.User;
import groovy.lang.Tuple2;
import io.swagger.annotations.ApiOperation;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.time.OffsetDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/admin")
@Validated
public class AdminController {
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private UserLogService userLogService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private TaskResourceService taskResourceService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private ObjectMapper mapper;

    @GetMapping(value = "/storage", params = "info=list")
    @RequiredSuperAdmin
    public ListResult storageList(@RequestParam Region region,
                                  @RequestParam String prefix,
                                  @RequestParam(required = false) Integer maxKeys,
                                  @RequestParam(required = false) String nextToken) {
        return region.getStorage().list(prefix, "/", maxKeys, nextToken);
    }

    @GetMapping(value = "/storage", params = "info=listAll")
    @RequiredSuperAdmin
    public List<ObjectResult> storageListAll(@RequestParam Region region,
                                             @RequestParam String prefix,
                                             @RequestParam(defaultValue = "false") boolean deep,
                                             @NonNull HttpServletResponse res) throws IOException {
        PrintWriter out = res.getWriter();
        res.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
        res.setCharacterEncoding(StandardCharsets.UTF_8.toString());
        int count = 0;
        out.write("[");
        Iterator<ObjectResult> it = region.getStorage().listAll(prefix, deep ? null : "/").iterator();
        while (it.hasNext()) {
            ObjectResult obj = it.next();
            if (count++ > 0) out.write(',');
            out.write('\n');
            out.write("  ");
            out.write(mapper.writeValueAsString(obj));
            out.flush();
            res.flushBuffer();
        }
        out.write("\n]\n");
        res.flushBuffer();
        out.close();
        return null;
    }

    @GetMapping(value = "/storage", params = "info=get")
    @RequiredSuperAdmin
    public ResponseEntity<?> storageGet(@RequestParam Region region, @RequestParam String key) {
        HttpEntity<InputStream> entity = region.getStorage().get(key);
        if (entity == null) {
            return ResponseEntity.status(404).contentType(MediaType.TEXT_PLAIN).body("Not Found");
        } else {
            InputStream body = entity.getBody();
            Objects.requireNonNull(body, "The body must not null here");
            return ResponseEntity.status(200).headers(entity.getHeaders()).body(new InputStreamResource(body));
        }
    }

    @PostMapping(value = "/storage", params = "action=delete")
    @RequiredSuperAdmin
    public void storageDelete(@RequestParam Region region, @RequestParam String key) {
        region.getStorage().delete(key);
    }

    @Deprecated
    @ApiOperation("@deprecated Migrated to MiscController")
    @PostMapping(params = "action=touchLastModifiedFlag")
    @RequiredSuperAdmin
    public void touchLastModifiedFlag(@RequestParam Region region, @RequestParam Env env) {
        StorageHelper.touchLastModifiedFlag(region.getStorage(), env);
    }

    @GetMapping(value = "/projects")
    @RequiredSuperAdmin
    public Map<String, Long> findAllProjects() {
        return projectService.findAllProjects();
    }

    @PostMapping(value = "/activities", params = "info=search")
    @RequiredSuperAdmin
    public List<UserLogModel> searchUserLog(@RequestBody UserLogSearchParams params) {
        return userLogService.search(params);
    }

    @PostMapping(value = "/tools", params = "action=sendMessage")
    @RequiredSuperAdmin
    public void sendFeishuMessage(@RequestParam String message, @RequestParam String userUniqId) {
        User user = feishuService.getUserInfo(userUniqId);
        if (user == null) {
            throw ResourceNotFoundException.create("feishu user", "enName", userUniqId);
        }
        feishuService.sendTextToUser(message, user);
    }

    @PostMapping(value = "/resources", params = "action=purge")
    @RequiredSuperAdmin
    public Map<String, String> purgeBuildTaskResources(@RequestParam long projectId,
                                                       @RequestParam long taskId,
                                                       @RequestParam Region region) {
        ProjectAndTaskModel both = buildTaskService.getBoth(projectId, taskId);
        return taskResourceService.purgeBuildTaskResources(both.getProject(), both.getTask());
    }

    @GetMapping(params = "info=listTaskIds")
    public List<Tuple2<Long, Long>> listTaskIds(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startTime,
                                                @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endTime,
                                                @RequestParam(defaultValue = "10000") int limit) {
        return buildTaskService.listTaskIds(startTime, endTime, limit);
    }

    @PostMapping(value = "/projects", params = "action=updateDevDomain")
    @RequiredSuperAdmin
    public void updateProjectDevDomain(@RequestParam long projectId, @RequestParam @NonNull String devDomain) {
        projectService.partialUpdateConfig(projectId, p -> p.setDevDomain(devDomain));
    }
}
