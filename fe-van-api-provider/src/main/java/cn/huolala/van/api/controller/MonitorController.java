package cn.huolala.van.api.controller;

import cn.huolala.van.api.controller.dto.MonitorQueryParams;
import cn.huolala.van.api.model.IdDatePair;
import cn.huolala.van.api.model.MonitorRecordDetail;
import cn.huolala.van.api.model.SimpleMonitorRecordModel;
import cn.huolala.van.api.service.MonitorRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/monitor")
@Validated
public class MonitorController {
    @Autowired
    private MonitorRecordService monitorRecordService;

    @PostMapping
    public Map<Long, List<SimpleMonitorRecordModel>> query(@RequestBody MonitorQueryParams params) {
        return params.getProjectIds().parallelStream().collect(Collectors.toMap(
                i -> i,
                id -> monitorRecordService.query(
                        id,
                        params.getLimit(),
                        params.getMinimalPv(),
                        params.getStartTime(),
                        params.getEndTime())));
    }

    @PostMapping(params = "info=detail")
    public List<Optional<MonitorRecordDetail>> batchGetDetail(@RequestBody List<IdDatePair> idpList) {
        return monitorRecordService.batchGetDetail(idpList);
    }

    @PostMapping(params = "info=test")
    public void test() {
        try {
            monitorRecordService.saveLightAppProjectScoreByName("light-app");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}