package cn.huolala.van.api.controller.internal;

import cn.huolala.van.api.annotation.NoAuthentication;
import cn.huolala.van.api.controller.dto.CanaryAuditLogMeta;
import cn.huolala.van.api.controller.internal.dto.CardActionParams;
import cn.huolala.van.api.exception.BizException;
import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.feishu.FeishuActionValue.*;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.ActionElement;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.DivElement;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.HrElement;
import cn.huolala.van.api.model.feishu.FeishuEventResponse;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.service.feishu.HuolalaFeishuEnhanceService;
import cn.huolala.van.api.util.FeishuUtils;
import cn.huolala.van.api.util.UserUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lark.oapi.card.model.Action;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.api.constants.enums.UserLogType.FEISHU_DEPLOY_PROD;
import static cn.huolala.van.api.model.feishu.FeishuActionValue.ButtonType.DANGER;
import static cn.huolala.van.api.util.AuditLogUtil.*;
import static cn.huolala.van.api.util.MocNotificationUtil.notifyMocForRelease;
import static java.util.concurrent.CompletableFuture.supplyAsync;
import static java.util.stream.Collectors.toMap;

/**
 * @see <a href="https://open.feishu.cn/document/ukTMukTMukTM/uYzM3QjL2MzN04iNzcDN/configuring-card-callbacks/card-callback-structure">消息卡片回传交互（旧）</a>
 */
@Log4j2
@RestController
@RequestMapping("/internal/feishu")
public class FeishuCallbackController {

    @Autowired
    private DeployService deployService;
    @Autowired
    private UserService userService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private HuolalaFeishuEnhanceService feishuEnhanceService;
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private ObjectMapper objectMapper;

    @NonNull
    private static String findValueType(@Nullable Map<String, Object> value) {
        Objects.requireNonNull(value, "The `action.value` must not be null here");
        return Optional.ofNullable(value.get("type")).map(i -> i instanceof String ? (String) i : null)
            .orElseThrow(() -> new InternalMappingException("The `action.value.type` must be a string"));
    }

    @NoAuthentication
    @PostMapping(params = "action=cardActionDispatcher")
    public FeishuEventResponse cardActionDispatcher(@RequestBody CardActionParams params) {

        String larkUserId = params.getUserId();
        UserModel user = userService.getByUniqId(feishuEnhanceService.getUniqNameByLarkUserId(larkUserId));
        if (user == null) throw ResourceNotFoundException.create("feishu user", "id", larkUserId);
        UserUtils.setCurrentUser(user);

        try {
            Action action = params.getAction();
            if (action == null) return null;
            Map<String, Object> value = action.getValue();
            String type;
            switch (action.getTag()) {
                case "button":
                    type = findValueType(value);
                    switch (type) {
                        case "performRelease":
                            return performRelease(objectMapper.convertValue(value, PerformRelease.class));
                        case "performDeploy":
                            return performDeploy(objectMapper.convertValue(value, PerformDeploy.class));
                        case "confirmToRollback":
                            return confirmToRollback(objectMapper.convertValue(value, ConfirmToRollback.class));
                        case "performRollback":
                            return performRollback(objectMapper.convertValue(value, PerformRollback.class));
                        case "confirmToRelease":
                            return confirmToRelease(objectMapper.convertValue(value, ConfirmToRelease.class));
                        default:
                            throw new InternalException("Unsupported `action.value.type` '" + type + "'");
                    }
                case "overflow":
                    type = findValueType(value);
                    switch (type) {
                        case "performDeploy":
                            value.put("env", action.getOption());
                            return performDeploy(objectMapper.convertValue(value, PerformDeploy.class));
                        case "todo":
                            return null;
                        default:
                            throw new InternalException("Unsupported `action.value.type` '" + type + "'");
                    }
                default:
                    return null;
            }
        } catch (BizException exception) {
            return FeishuEventResponse.createErrorResponse(exception.getMsg());
        } catch (RuntimeException exception) {
            String message = exception.getMessage();
            if (message == null) throw exception;
            log.error(exception);
            return FeishuEventResponse.createErrorResponse(message);
        }
    }

    private FeishuEventResponse confirmToRelease(@NonNull ConfirmToRelease value) {
        Long projectId = value.getProjectId();
        VanProjectConfig vanConfig = projectService.getVanProjectConfig(Region.defaultRegion(), projectId);

        if (vanConfig.isInternational()) {
            throw new InternalException("The international project does not support this operation");
        }

        Region region = Region.defaultRegion();
        Optional<CanaryRecord> current = historyService.getHead(projectId, region);

        Long srcTaskId = current.map(c -> {
            if (c.getCanary().hasCanary()) {
                throw new InternalException(
                    "This operation can be used only if the current released version has only one default rule");
            }
            return c.getCanary().getDefaultTaskId();
        }).filter(id -> id > 0).orElse(null);

        Long dstTaskId = value.getTaskId();

        ProjectModel project = projectService.getNeverNull(projectId);

        Long headPoint = current.map(CanaryRecord::getId).orElse(null);

        FeishuCard card = FeishuUtils.buildFeishuReleaseConfirmCard(project, current.orElse(null), srcTaskId, dstTaskId,
            buildTaskService);


        ActionElement actions = new ActionElement().appendTo(card);
        new PerformRelease(projectId, dstTaskId, headPoint).chatId(value.getChatId()).sign()
            .wrapAsButton("执行发布", DANGER).appendTo(actions);

        feishuService.sendCard(card, value.getChatId());

        return null;
    }

    private FeishuEventResponse performRelease(@NonNull PerformRelease value) {
        Long projectId = value.getProjectId();

        ProjectAndTaskModel both = buildTaskService.getBoth(projectId, value.getTaskId());
        ProjectModel project = both.getProject();

        Region region = Region.defaultRegion();
        UserModel user = UserUtils.getCurrentUser();
        LegacyDeployConfig canary = new LegacyDeployConfig(both.getTask().getId());
        String message = String.format("Release #%d using Feishu", both.getTask().getId());
        DeployParams params = new DeployParams(user, project, region, canary, message, null);

        // TODO: check the window

        CanaryRecord cr = deployService.releaseForProd(params, null, value.getHeadPoint());

        addAuditLogForRelease(project, cr);
        notifyMocForRelease(project, cr);
        addAuditLog(project.getId(), FEISHU_DEPLOY_PROD, new CanaryAuditLogMeta(cr));

        return FeishuEventResponse.createSuccessResponse("操作成功");
    }

    private FeishuEventResponse performDeploy(PerformDeploy value) {
        ConfigEnv env = ConfigEnv.fromString(value.getEnv());
        if (env == null) throw new InternalException("The `env` must not null here");

        ProjectModel project = projectService.getNeverNull(value.getProjectId());
        UserModel user = UserUtils.getCurrentUser();

        DeployParams params = new DeployParams(user, project, env, value.getTaskId());

        DeployRecord cr = deployService.deployForDev(params);

        addAuditLogForDeployDev(cr);

        return FeishuEventResponse.createSuccessResponse("操作成功");
    }

    private FeishuEventResponse confirmToRollback(@NonNull ConfirmToRollback value) {
        long projectId = value.getProjectId();
        long canaryId = value.getCanaryId();
        CanaryRecord previous = historyService.getPreviousNeverNull(canaryId);
        previous.assertProjectId(projectId);
        CanaryRecord current = historyService.getHeadNeverNull(projectId, previous.getRegion());

        if (current.getId() != canaryId) {
            throw new InternalException("The canary record is not currently active");
        }

        if (!Objects.equals(previous.getRegion(), current.getRegion())) {
            throw new InternalException("The region does not match between the previous and current canary configs");
        }

        Map<String, CanaryRecord> sections = Maps.of("当前线上版本", current, "本次操作将回滚到", previous);

        List<Long> taskIds = sections.values().stream().flatMap(i -> i.getCanary().collectTaskIds().stream()).distinct()
            .collect(Collectors.toList());
        PreparedTaskInfoBundle bundle = getPreparedTaskInfoBundle(projectId, new LinkedHashSet<>(taskIds));
        ProjectModel project = bundle.getProject();
        Map<Long, BuildTaskModel> taskMap = bundle.getTaskMap();
        Map<Long, String> tagMap = bundle.getTagMap();

        FeishuCard card = FeishuUtils.buildConfirmCard("回滚发布", project.getName());

        sections.forEach((key, canaryRecord) -> {
            new DivElement().addField(FeishuCard.Element.Field.label(key, null))
                .addFields(FeishuUtils.buildCanaryRecordFields(canaryRecord, project, taskMap, tagMap)).appendTo(card);
            new HrElement().appendTo(card);
        });

        ActionElement actions = new ActionElement().appendTo(card);
        new PerformRollback(projectId, canaryId).chatId(value.getChatId()).sign().wrapAsButton("执行回滚", DANGER)
            .appendTo(actions);

        feishuService.sendCard(card, value.getChatId());

        return null;
    }

    @NonNull
    private PreparedTaskInfoBundle getPreparedTaskInfoBundle(long projectId, @NonNull Set<Long> taskIds) {
        CompletableFuture<Stream<BuildTaskModel>> f1 = supplyAsync(() -> buildTaskService.batchGet(projectId, taskIds));
        CompletableFuture<Map<Long, String>> f2 = supplyAsync(() -> buildTaskService.findTag(projectId, taskIds));
        return new PreparedTaskInfoBundle(projectService.getNeverNull(projectId),
            f1.join().collect(toMap(BuildTaskModel::getId, Function.identity())), f2.join());
    }

    private FeishuEventResponse performRollback(@NonNull PerformRollback value) {
        long projectId = value.getProjectId();
        long canaryId = value.getCanaryId();

        CompletableFuture<CanaryRecord> pPrevious = supplyAsync(() -> historyService.getPreviousNeverNull(canaryId));
        CompletableFuture<CanaryRecord> pCurrent = supplyAsync(() -> historyService.getCanaryRecordNeverNull(canaryId));
        ProjectModel project = projectService.getNeverNull(projectId);
        CanaryRecord previous = pPrevious.join();
        CanaryRecord current = pCurrent.join();

        previous.assertProjectId(projectId);
        current.assertProjectId(projectId);

        Region region = previous.getRegion();
        LegacyDeployConfig canary = previous.getCanary();
        UserModel user = UserUtils.getCurrentUser();

        String message = String.format("Rollback from (%s) to (%s) using Feishu", current.toSummary(),
            previous.toSummary());
        DeployParams params = new DeployParams(user, project, region, canary, message, null);

        // In scene of rolling back, the back point should not be set to the current head.
        // Because doing so will generate an alternating circular reference.
        // For example:
        // Now, the HEAD is A, and history chains is [ A => B, B => C, C => D, ... ].
        // STEP 1. Roll it back, HEAD is B, and history chains [ B => A, A => B, B => C, C => D, ... ]
        // STEP 2. Roll it back again, HEAD = A, and history is [ A => B, B => A, A => B, B => C, C => D, ... ].
        // Obviously, this behavior is not expected.
        //
        // Therefore, in scene of rolling back, the back point should be set to twice its previous value.
        // STEP 1. Roll it back, HEAD is B, and history chains [ B => C, A => B, B => C, C => D, ... ]
        // STEP 2. Roll it back again, HEAD is C, and history chains [ C => D, B => C, A => B, B => C, C => D, ... ]
        // This behavior is actually intuitive.
        //
        Long backPoint = historyService.getPreviousId(previous.getId()).orElse(null);

        CanaryRecord cr = deployService.releaseForProd(params, backPoint, current.getId());

        addAuditLogForRelease(project, cr);
        notifyMocForRelease(project, cr);
        addAuditLog(project.getId(), FEISHU_DEPLOY_PROD, new CanaryAuditLogMeta(cr));

        return FeishuEventResponse.createSuccessResponse("操作成功");
    }

    @Getter
    @AllArgsConstructor
    private static class PreparedTaskInfoBundle {
        @NonNull
        private final ProjectModel project;
        @NonNull
        private final Map<Long, BuildTaskModel> taskMap;
        @NonNull
        private final Map<Long, String> tagMap;
    }
}
