package cn.huolala.van.api.controller;

import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.SsoService;
import cn.huolala.van.api.service.UserLogService;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.UserUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/user")
@Validated
public class UserController {
    @Autowired
    private UserService userService;
    @Autowired
    private SsoService ssoService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private UserLogService userLogService;

    @GetMapping("/stars")
    public List<Long> getMyStaredProjectIds() {
        UserModel user = UserUtils.getCurrentUser();
        return userService.findStaredProjectIdListByUserId(user.getId());
    }

    @PostMapping(value = "/stars", params = "action=update")
    public int updateMyStaredProjectIds(@RequestBody Map<Long, Boolean> map) {
        final UserModel user = UserUtils.getCurrentUser();
        return map.entrySet().stream()
                .collect(Collectors.partitioningBy(
                        Map.Entry::getValue,
                        Collectors.mapping(Map.Entry::getKey, Collectors.toList())
                ))
                .entrySet().parallelStream().mapToInt(i -> {
                    if (i.getKey() == Boolean.TRUE) return userService.insertStars(user.getId(), i.getValue());
                    else if (i.getKey() == Boolean.FALSE) return userService.deleteStars(user.getId(), i.getValue());
                    else return 0;
                }).sum();
    }

    @GetMapping("info=searchUser")
    @Deprecated
    public List<UserBase.Simple> __deprecatedSearchUser(
            @RequestParam String keyword,
            @RequestParam(required = false) Long projectId,
            @RequestParam(defaultValue = "5") int limit) {
        return searchUser(keyword, projectId, limit);
    }

    @GetMapping(params = "info=searchUser")
    public List<UserBase.Simple> searchUser(
            @RequestParam String keyword,
            @RequestParam(required = false) Long projectId,
            @RequestParam(defaultValue = "5") int limit
    ) {
        if (keyword == null) return Collections.emptyList();
        keyword = keyword.trim();
        if (keyword.isEmpty()) return Collections.emptyList();
        Long searcherId = UserUtils.getCurrentUser().getId();

        return userService.searchUser(keyword, searcherId, projectId, limit)
                .map(UserBase::extractSimple)
                .collect(Collectors.toList());
    }

    @GetMapping(params = "info=searchUserFromSso")
    public List<UserBase.Simple> searchUserFromSso(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "5") int limit
    ) {
        if (keyword == null) return Collections.emptyList();
        keyword = keyword.trim();
        if (keyword.isEmpty()) return Collections.emptyList();
        return Arrays.stream(ssoService.searchUser(keyword))
                .map(i -> UserBase.create(i.getAccount(), i.getUserName()))
                .filter(Objects::nonNull)
                .limit(limit)
                .collect(Collectors.toList());
    }

    @GetMapping(params = "info=myRecentBuildTasks")
    public List<BuildTaskModel> findMyRecentBuildTasks(@RequestParam(defaultValue = "10") int limit) {
        UserModel user = UserUtils.getCurrentUser();
        return buildTaskService.findUserRecentBuildTasks(user, limit).collect(Collectors.toList());
    }

    @PostMapping(params = "info=myActivities")
    public List<UserLogModel> findMyActivities(@RequestBody UserLogSearchParamsWithoutUser params) {
        UserLogSearchParams searchParams = new UserLogSearchParams();
        searchParams.setUserUniqIds(Collections.singleton(UserUtils.getCurrentUser().getUniqId()));
        BeanUtils.copyProperties(params,searchParams );
        return userLogService.search(searchParams);
    }

    @GetMapping(params = "info=me")
    public UserModel whoAmI() {
        return UserUtils.getCurrentUser();
    }
}
