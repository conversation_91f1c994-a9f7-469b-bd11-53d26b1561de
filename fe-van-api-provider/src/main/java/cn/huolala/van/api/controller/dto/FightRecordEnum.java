package cn.huolala.van.api.controller.dto;

import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.HistoryService;
import cn.huolala.van.api.service.UserLogService;
import cn.lalaframework.spring.ApplicationContextUtil;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.lang.NonNull;

import java.util.function.BiFunction;

public enum FightRecordEnum {
    @ApiModelProperty("构建次数")
    PROJECT_BUILD(BuildTaskService.class, BuildTaskService::countByUser),
    @ApiModelProperty("发布次数")
    PROJECT_RELEASE(HistoryService.class, HistoryService::countCanariesByUser),
    @ApiModelProperty("组件发布次数")
    LIBRARY_RELEASE(HistoryService.class, HistoryService::countNpmPublishesByUser),
    @ApiModelProperty("小程序预览与上传次数")
    MINIPROGRAM_RELEASE(UserLogService.class, UserLogService::countMiniprogramByUser),
    @ApiModelProperty("组合分支次数")
    COMPOSITE_BUILD(UserLogService.class, UserLogService::countCompositeByUser),
    @ApiModelProperty("Workflow 相关操作次数")
    WORKFLOW(UserLogService.class, UserLogService::countWorkflowByUser);

    @NonNull
    private final Class<?> service;
    @NonNull
    private final BiFunction<?, UserModel, Integer> method;

    <T> FightRecordEnum(@NonNull Class<T> service, @NonNull BiFunction<T, UserModel, Integer> method) {
        this.service = service;
        this.method = method;
    }

    @SuppressWarnings("unchecked")
    public <T> int countByUser(UserModel user) {
        T bean = ApplicationContextUtil.getBean((Class<T>) service);
        return ((BiFunction<T, UserModel, Integer>) method).apply(bean, user);
    }
}
