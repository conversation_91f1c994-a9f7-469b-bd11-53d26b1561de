<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>cn.huolala</groupId>
    <artifactId>fe-van-api</artifactId>
    <version>1.0.1-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fe-van-api-provider</artifactId>

  <dependencies>
    <dependency>
      <groupId>cn.huolala</groupId>
      <artifactId>fe-van-api-facade</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.huolala</groupId>
      <artifactId>fe-van-api-service</artifactId>
      <version>${project.version}</version>
    </dependency>

    <!-- lala-launcher -->
    <dependency>
      <groupId>cn.lalaframework</groupId>
      <artifactId>lala-launcher</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-soa</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.lalaframework</groupId>
      <artifactId>ll-doc-client</artifactId>
      <version>1.2.2-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.lalamove.infra</groupId>
      <artifactId>lala-sso-java-sdk</artifactId>
      <version>1.0.8-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.auth0</groupId>
      <artifactId>java-jwt</artifactId>
      <version>4.2.1</version>
    </dependency>

    <dependency>
      <groupId>io.jsonwebtoken</groupId>
      <artifactId>jjwt-jackson</artifactId>
      <version>0.11.2</version>
      <scope>compile</scope>
    </dependency>

    <dependency>
      <groupId>com.github.victools</groupId>
      <artifactId>jsonschema-generator</artifactId>
      <version>4.38.0</version>
    </dependency>
  </dependencies>

  <build>
    <finalName>fe-van-api-svc</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
