package cn.huolala.api.constants.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;

import java.util.Arrays;

public enum ProjectType {
    Unknown("unknown_project_type"),
    Web("web_project_type"),
    Component("component_project_type"),
    Workers("workers_project_type"),
    Miniprogram("miniprogram_project_type");

    private final String golangStr;

    ProjectType(String golangStr) {
        this.golangStr = golangStr;
    }

    @JsonCreator
    public static ProjectType fromJsonValue(String golangStr) {
        if ("null".equals(golangStr) || "".equals(golangStr)) return null;
        return Arrays.stream(ProjectType.values())
                .filter(i -> i.golangStr.equals(golangStr)).findFirst()
                .orElse(Unknown);
    }

    public static ProjectType createFromOrdinal(int ordinal) {
        ProjectType[] values = values();
        if (ordinal < values.length) return values[ordinal];
        return Unknown;
    }

    @JsonValue
    public String toJsonValue() {
        return golangStr;
    }

    public static class SpringConverter implements Converter<String, ProjectType> {
        @Override
        public ProjectType convert(@NonNull String golangStr) {
            return ProjectType.fromJsonValue(golangStr);
        }
    }
}
