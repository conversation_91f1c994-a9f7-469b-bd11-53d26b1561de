package cn.huolala.api.constants.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

import java.util.List;

@Getter
@Setter
public class WatchDogFilterRule {
    private Exp exp;
    private String doc;
    private Level level;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum Level {
        warning, ignore
    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "funcName")
    @JsonSubTypes({
            @JsonSubTypes.Type(value = Exp.WithAnd.class, name = "and"),
            @JsonSubTypes.Type(value = Exp.WithOr.class, name = "or"),
            @JsonSubTypes.Type(value = Exp.WithNot.class, name = "not"),
            @JsonSubTypes.Type(value = Exp.WithEqual.class, name = "equal"),
            @JsonSubTypes.Type(value = Exp.WithContains.class, name = "contains"),
            @JsonSubTypes.Type(value = Exp.WithPrefix.class, name = "prefix"),
            @JsonSubTypes.Type(value = Exp.WithSuffix.class, name = "suffix"),
            @JsonSubTypes.Type(value = Exp.WithSmaller.class, name = "smaller"),
            @JsonSubTypes.Type(value = Exp.WithSmaller.class, name = "<"),
    })
    public interface Exp {
        @NonNull
        String getFuncName();

        @Getter
        @Setter
        abstract class GpExp implements Exp {
            private List<Exp> exps;
        }

        @Getter
        @Setter
        abstract class FnExp implements Exp {
            private String key;
            private List<String> args;
        }

        @Getter
        @Setter
        class WithAnd extends GpExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "and")
            public String getFuncName() {
                return "and";
            }
        }

        @Getter
        @Setter
        class WithOr extends GpExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "or")
            public String getFuncName() {
                return "or";
            }
        }

        @Getter
        @Setter
        class WithNot extends GpExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "not")
            public String getFuncName() {
                return "not";
            }
        }

        @Getter
        @Setter
        class WithEqual extends FnExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "equal")
            public String getFuncName() {
                return "equal";
            }
        }

        @Getter
        @Setter
        class WithContains extends FnExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "contains")
            public String getFuncName() {
                return "contains";
            }
        }

        @Getter
        @Setter
        class WithPrefix extends FnExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "prefix")
            public String getFuncName() {
                return "prefix";
            }
        }

        @Getter
        @Setter
        class WithSuffix extends FnExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "suffix")
            public String getFuncName() {
                return "suffix";
            }
        }

        @Getter
        @Setter
        class WithSmaller extends FnExp {
            @Override
            @NonNull
            @ApiModelProperty(allowableValues = "smaller")
            public String getFuncName() {
                return "smaller";
            }
        }
    }
}
