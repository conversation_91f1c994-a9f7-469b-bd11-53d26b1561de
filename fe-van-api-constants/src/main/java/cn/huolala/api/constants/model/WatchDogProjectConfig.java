package cn.huolala.api.constants.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class WatchDogProjectConfig {
    private String appId;
    private String host;
    private String description;

    private Integer collectAPIPercent;
    private Integer collectPerfPercent;
    private Integer collectErrorPercent;
    private Integer collectResourcePercent;
    private Integer collectPageViewPercent;
    private Integer collectMetricPercent;

    private Integer logRateLimit;

    private Boolean isCollectIP;
    private Boolean isCollectUserAgent;
    private Boolean isCollectSourceMap;

    private Boolean degrade;

    private String token;

    private List<Metric> metrics;

    private List<WatchDogFilterRule> errorIgnoreConfigs;

    @Getter
    @Setter
    public static class Metric {
        private String name;
        private String description;
        private List<GroupTags> groupTags;

        @Getter
        @Setter
        public static class GroupTags {
            private List<Tag> tags;
        }

        @Getter
        @Setter
        public static class Tag {
            private String name;
            private String rename;
            private List<String> values;
        }
    }
}
