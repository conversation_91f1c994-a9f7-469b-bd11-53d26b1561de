package cn.huolala.api.constants.enums;

public enum MetaType {
    WorkersSDKPublished,
    AutoDeployFailedLog,
    MiniprogramInfo,
    @Deprecated
    MiniprogramDeployTask,
    @Deprecated
    MiniprogramDeployHistory,

    /**
     * To control the build notification for users.
     * By default, all users subscribe to build notifications.
     * This meta stores a BLACKLIST which projects are unsubscribed.
     * In this type, the metaId means userId, and the value is a JSON array containing all UNSUBSCRIBED project IDs.
     */
    BuildTaskNotificationFeishuSubscribe,
    LLMSyncTaskStatus,
    VanAPIGlobalLock,
    VanPublishLock,
    VanCanaryChain,
    VanPublishMetaInfo,
    VanFeishuNotificationFilterConfiguration,

    ProjectBuildCacheStatus,
    ProjectHookConfiguration,

    // 用来管理项目部署时的钩子配置
    ProjectDeployHookConfiguration,

    // 用来管理项目 webhook 配置
    ProjectWebhookConfiguration,

    // TaskMultiBuildTaskInfo 记录当前 task 对应的构建 tag 信息
    TaskMultiBuildTaskInfo,

    // 记录项目的生产发布配置
    ProjectProdReleaseConfig,

    // 记录 UD 的数据同步状态
    UdSyncTaskStatus,

    BuildTaskFeishuCardId;


    public static MetaType createFromInt(int ordinal) {
        MetaType[] values = values();
        if (ordinal < values.length) return values[ordinal];
        throw new IllegalArgumentException("MetaType not found by " + ordinal);
    }
}
