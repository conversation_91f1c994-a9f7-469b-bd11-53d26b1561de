package cn.huolala.api.constants.model;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
public class WatchDogGlobalConfig {
    private Map<MetricType, Integer> percentage;

    @JsonProperty("metric_percentage")
    private Map<MetricType, Integer> metricPercentage;

    @JsonProperty("enable_resource_white_list")
    private Boolean enableResourceWhiteList;

    @JsonProperty("resource_white_list")
    private List<String> resourceWhiteList;

    @JsonProperty("custom_black_list")
    private List<String> customBlackList;

    private List<WatchDogFilterRule> errorIgnoreConfigs;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum MetricType {
        @JsonEnumDefaultValue
        unknown,
        api, resource, perf, custom, error
    }
}
