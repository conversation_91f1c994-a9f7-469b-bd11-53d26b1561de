package cn.huolala.api.constants.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.util.Arrays;

@JsonFormat(shape = JsonFormat.Shape.STRING)
public enum Env {
    stg("stg"),
    pre("pre"),
    prd("prod");

    @Getter
    private final String longName;

    Env(String longName) {
        this.longName = longName;
    }

    @Nullable
    @JsonCreator
    public static Env fromString(String value) {
        return Arrays.stream(values())
                .filter(i -> i.name().equalsIgnoreCase(value) || i.longName.equalsIgnoreCase(value))
                .findFirst().orElse(null);
    }


    public static class LongNameSerializer extends JsonSerializer<Env> {
        @Override
        public void serialize(Env value, JsonGenerator gen, SerializerProvider provider) throws IOException {
            gen.writeString(value.getLongName());
        }
    }
}
