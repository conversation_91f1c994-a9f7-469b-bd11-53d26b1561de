package cn.huolala.api.constants.model;

import cn.huolala.api.constants.enums.UserLogType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
@AllArgsConstructor
public class UserLogRequest {
    @NonNull
    private Long userId;
    @NonNull
    private Long projectId;
    @NonNull
    private UserLogType type;
    @Nullable
    private Object meta;
    @Nullable
    private String description;
}
