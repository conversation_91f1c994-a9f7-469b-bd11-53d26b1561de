package cn.huolala.api.constants.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * https://huolala.feishu.cn/docx/NexjdRrEcozyPMxywSecuzAqn1f?from=space_home_recent
 */
@JsonFormat(shape = JsonFormat.Shape.NUMBER)
public enum UserLogType {
    CREATE_PROJECT(0, "创建项目"),
    DEPLOY_DEV(1, "测试环境发布"),
    DEPLOY_PROD(2, "DEPLOY_PROD"),
    CANARY_DEPLOY(3, "灰度发布"),
    FEISHU_DEPLOY_DEV(4, "飞书测试环境发布"),
    FEISHU_DEPLOY_PROD(5, "飞书生产发布"),
    ROLLBACK_DEPLOY(6, "回滚发布"),
    FEISHU_ROLLBACK(7, "飞书一键回滚"),
    MANUAL_BUILD(8, "手动构建"),
    ADD_USER(9, "手动添加用户"),
    BUILD_CACHE(10, "构建缓存"),
    VIEW_PROD_DEPLOY(11, "查看生产发布记录"),
    VIEW_DEV_DEPLOY(12, "查看测试环境发布记录"),
    APPLY_ENTRYPOINT(13, "申请入口"),
    SERVICE_VCONSOLE(14, "开启 vConsole"),
    SERVICE_TUNNEL(15, "开启 tunnel"),
    SERVICE_VERSION_SWITCH(16, "开启 Version Switch"),
    SERVICE_SSO_INJECT(17, "开启 SSO 信息注入"),
    SERVICE_SSO_GUARD(18, "开启 SSO 守护"),
    SERVICE_WATERMARK(19, "开启水印"),
    COMPOSITE_BUILD(20, "联合构建"),
    WORKFLOW_CREATE(21, "使用 workflow"),
    WORKFLOW_MERGE(22, "Workflow merge"),
    WORKFLOW_REBASE(23, "Workflow rebase"),
    WORKFLOW_COMPARE(24, "Workflow Compare"),
    SERVICE_OFFWEB(25, "离线包"),
    ONE_KEY_RELEASE(26, "一键发布"),
    WORKERS_RELEASE(27, "Workers 发布"),
    MINIPROGRAM_PREVIEW(28, "小程序预览"),
    MINIPROGRAM_UPLOAD(29, "小程序上传"),
    NPM_RELEASE(30, "NPM 发布"),
    USER_TRACE_SERVICE(31, "使用用户轨迹"),
    AUTO_TRACE_SERVICE(32, "使用自动埋点"),
    APPLY_WINDOW(33, "申请窗口"),
    RELEASE_WITH_PDM_PLAN(34, "PDM 计划发布"),
    AI_LOG_ANALYSIS(35, "AI 日志分析"),
    UPDATE_PROJECT_CONFIG(36, "更新项目配置"),
    UPDATE_PROJECT_SERVICE(37, "Update Project Service Config");

    UserLogType(int i, String name) {
    }
}
