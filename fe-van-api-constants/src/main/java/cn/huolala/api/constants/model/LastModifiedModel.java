package cn.huolala.api.constants.model;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;

public interface LastModifiedModel {
    @NonNull
    static ComResult compare(@Nullable LastModifiedModel model, @Nullable OffsetDateTime target) {
        if (model == null || target == null) return ComResult.Null;
        OffsetDateTime source = model.getLastModified();
        if (source == null) return ComResult.Null;
        long r1 = source.toEpochSecond() - target.toEpochSecond();
        if (r1 > 0L) return ComResult.After;
        if (r1 < 0L) return ComResult.Before;
        int r2 = source.getNano() - target.getNano();
        if (r2 > 0L) return ComResult.After;
        if (r2 < 0L) return ComResult.Before;
        return ComResult.Equal;
    }

    OffsetDateTime getLastModified();

    enum ComResult {
        Null, Before, After, Equal
    }
}
