package cn.huolala.van.api.configuration;

import com.unfbx.chatgpt.OpenAiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenAIClientConfiguration {

    @Value("${openai.key}")
    private String key;

    @Value("${openai.host}")
    private String host;

    @Bean
    public OpenAiClient openAIClient() {
        return OpenAiClient.builder()
                .apiKey(key)
                .connectTimeout(50)
                .writeTimeout(50)
                .readTimeout(50)
                .apiHost(host.endsWith("/") ? host : (host + "/"))
                .build();
    }
}
