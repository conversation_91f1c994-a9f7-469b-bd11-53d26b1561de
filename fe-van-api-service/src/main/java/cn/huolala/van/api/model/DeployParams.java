package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.project.ProjectModel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class DeployParams {
    @NonNull
    private final UserModel user;
    @NonNull
    private final ProjectModel project;
    @NonNull
    private final ConfigEnv env;
    @NonNull
    private final Region region;
    @NonNull
    private final LegacyDeployConfig config;

    @Nullable
    private String message;
    @Nullable
    private PdmInfo pdmInfo;

    /**
     * For development environment
     */
    public DeployParams(@NonNull UserModel user,
                        @NonNull ProjectModel project,
                        @NonNull ConfigEnv env,
                        @NonNull Long taskId) {
        this.user = user;
        this.project = project;
        this.env = env;
        this.region = Region.defaultRegion();
        this.config = new LegacyDeployConfig(taskId);
    }

    /**
     * For production environment
     */
    public DeployParams(@NonNull UserModel user,
                        @NonNull ProjectModel project,
                        @NonNull Region region,
                        @NonNull LegacyDeployConfig config,
                        @Nullable String message,
                        @Nullable PdmInfo pdmInfo) {
        this.user = user;
        this.project = project;
        this.env = new ConfigEnv(Env.prd, 0);
        this.region = region;
        this.config = config;
        this.message = message;
        this.pdmInfo = pdmInfo;
    }
}
