package cn.huolala.van.api.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;

@Getter
@Setter
@NoArgsConstructor
public class LastModifiedData<T> {
    @Nullable
    private OffsetDateTime lastModified;
    @Nullable
    private T data;

    public LastModifiedData(@Nullable OffsetDateTime lastModified, @Nullable T data) {
        this.lastModified = lastModified;
        this.data = data;
    }
}
