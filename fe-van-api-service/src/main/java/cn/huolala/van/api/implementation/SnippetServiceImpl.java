package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.EnvRegionName;
import cn.huolala.van.api.model.EnvRegionSnippet;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.Snippet;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.service.SnippetService;
import cn.huolala.van.api.util.StorageHelper;
import cn.lalaframework.storage.adapter.Storage;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class SnippetServiceImpl implements SnippetService {
    @Autowired
    private ProjectService projectService;

    @NonNull
    private static Stream<Snippet> getSnippetStream(@NonNull String projectName,
                                                    @NonNull Env env,
                                                    @NonNull Region region) {
        String keyPath = buildKeyPath(projectName, env, region);
        return region.getStorage().getOptionalValue(keyPath, Snippet[].class).map(Arrays::stream)
            .orElseGet(Stream::empty).filter(Objects::nonNull);
    }

    private static String buildKeyPath(@NonNull String projectName, @NonNull Env env, @NonNull Region region) {
        StringBuilder sb = new StringBuilder();
        sb.append(projectName);
        sb.append("/config/snippet-");
        sb.append(env.getLongName());
        if (region != Region.cn) {
            sb.append("-");
            sb.append(region.name());
        }
        sb.append(".json");
        return sb.toString();
    }

    private static void updateSnippets(@NonNull String projectName,
                                       @NonNull Env env,
                                       @NonNull Region region,
                                       @NonNull List<Snippet.Update> updates) {
        String keyPath = buildKeyPath(projectName, env, region);
        Map<String, Boolean> actions = updates.stream()
            .collect(Collectors.toMap(Snippet.Update::getName, Snippet.Update::getRemove, (a, b) -> b));
        final Storage storage = region.getStorage();
        StorageHelper.partialUpdate(storage, keyPath, Snippet[].class, current -> Stream
            // Append the updates following the current snippets in a stream.
            .concat(current == null ? Stream.empty() : Arrays.stream(current),
                updates.stream().map(Snippet.Update::getSnippet))
            // Distinct by 'name' and retain the latter value (The latter value means updated value).
            // NOTE: Use the LinkedHashMap to preserve the original order.
            .collect(Collectors.toMap(Snippet::getName, i -> i, (a, b) -> b, LinkedHashMap::new)).values()
            // Now, the steam contains union types in both Snippet and Snippet.Update.
            .stream().map(union -> {
                if (union != null && Boolean.TRUE.equals(actions.get(union.getName()))) {
                    return null;
                }
                // It's a Snippet.
                return union;
            }).filter(Objects::nonNull)
            // Convert to array.
            .toArray(Snippet[]::new));
        StorageHelper.touchLastModifiedFlag(storage, env);
    }

    @NonNull
    @Override
    public List<EnvRegionSnippet> findInProject(long projectId, @NonNull List<EnvRegionName> requests) {
        String projectName = projectService.getNameById(projectId);
        return getSnippets(projectName, requests);
    }

    private List<EnvRegionSnippet> getSnippets(@NonNull String projectName, @NonNull List<EnvRegionName> requests) {
        return requests.stream().collect(Collectors.groupingBy(i -> Pair.of(i.getEnv(), i.getRegion()),
            Collectors.mapping(EnvRegionName::getName, Collectors.toSet()))).entrySet().parallelStream().flatMap(i -> {
            Env env = i.getKey().getKey();
            Region region = i.getKey().getValue();
            Set<String> names = i.getValue();
            return getSnippetStream(projectName, env, region).filter(s -> names.contains(s.getName()))
                .map(snippet -> new EnvRegionSnippet(env, region, snippet));
        }).collect(Collectors.toList());
    }

    @Override
    public void updateInProject(long projectId,
                                @NonNull Env env,
                                @NonNull Region region,
                                @NonNull List<Snippet.Update> updates) {
        String projectName = projectService.getNameById(projectId);
        updateSnippets(projectName, env, region, updates);
    }

    @Override
    @NonNull
    public List<EnvRegionSnippet> findInGlobal(@NonNull List<EnvRegionName> requests) {
        return getSnippets(".van", requests);
    }

    @Override
    public void updateInGlobal(@NonNull Env env, @NonNull Region region, @NonNull List<Snippet.Update> updates) {
        updateSnippets(".van", env, region, updates);
    }
}
