package cn.huolala.van.api.model;

import cn.huolala.api.constants.XLEnv;
import cn.lalaframework.storage.adapter.Storage;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.lang.NonNull;

import java.util.stream.Stream;

@JsonFormat(shape = JsonFormat.Shape.STRING)
public enum Region {
    cn(RegionStorage.main, "ps-cn.json", "prod.json"),

    sin(RegionStorage.oversea, "ps-sin.json", "prod-sin.json"),

    sao(RegionStorage.oversea, "ps-sao.json", "prod-sao.json"),

    bom(RegionStorage.main, "ps-bom.json", "prod-bom.json"),
    de(RegionStorage.main, "ps-de.json", "prod-de.json");

    @NonNull
    private final RegionStorage regionStorage;
    @NonNull
    private final String canaryFileName;
    @NonNull
    private final String legacyCanaryFileName;

    Region(@NonNull RegionStorage regionStorage, @NonNull String canaryFileName, @NonNull String legacyCanaryFileName) {
        this.regionStorage = regionStorage;
        this.canaryFileName = canaryFileName;
        this.legacyCanaryFileName = legacyCanaryFileName;
    }

    @NonNull
    public static Region defaultRegion() {
        return isUd() ? Region.bom : Region.cn;
    }

    @NonNull
    public static Storage defaultStorage() {
        return defaultRegion().getStorage();
    }

    @NonNull
    public static Stream<Region> supportedRegions() {
        if (isUd()) {
            return Stream.of(bom);
        }
        if (isDE()) {
            return Stream.of(de);
        }
        if (XLEnv.isXL()) {
            return Stream.of(cn);
        }
        return Stream.of(cn, sin, sao);
    }

    private static boolean isUd() {
        return "3".equals(System.getProperty("hll.region"));
    }

    private static boolean isDE() {
        return "6".equals(System.getProperty("hll.region"));
    }

    @NonNull
    public Storage getStorage() {
        return regionStorage.getService();
    }

    @NonNull
    public String fetchCanaryPath(@NonNull String projectName) {
        return String.format("%s/%s", projectName, canaryFileName);
    }

    @NonNull
    public String fetchLegacyCanaryPath(@NonNull String projectName) {
        return String.format("%s/%s", projectName, legacyCanaryFileName);
    }
}
