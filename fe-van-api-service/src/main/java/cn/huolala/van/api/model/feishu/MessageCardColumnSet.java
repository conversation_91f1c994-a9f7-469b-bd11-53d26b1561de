package cn.huolala.van.api.model.feishu;

import com.google.gson.annotations.SerializedName;
import com.lark.oapi.card.model.IMessageCardElement;
import com.lark.oapi.card.model.MessageCardElement;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class MessageCardColumnSet extends MessageCardElement implements IMessageCardElement {

    @SerializedName("flex_mode")
    @Builder.Default
    private String flexMode = "none";
    @SerializedName("background_style")
    private String backgroundStyle;
    @SerializedName("horizontal_spacing")
    private String horizontalSpacing;
    private Column[] columns;

    public MessageCardColumnSet() {
        this.tag = "column_set";
    }

    public MessageCardColumnSet(String flexMode, String backgroundStyle, String horizontalSpacing, Column[] columns) {
        this.tag = "column_set";
        this.flexMode = flexMode;
        this.backgroundStyle = backgroundStyle;
        this.horizontalSpacing = horizontalSpacing;
        this.columns = columns;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @Builder
    public static class Column extends MessageCardElement {
        private String width;
        private Integer weight;
        @SerializedName("vertical_align")
        private String verticalAlign;

        private MessageCardElement[] elements;

        public Column() {
            this.tag = "column";
        }

        public Column(String width, Integer weight, String verticalAlign, MessageCardElement[] elements) {
            this.tag = "column";
            this.width = width;
            this.weight = weight;
            this.verticalAlign = verticalAlign;
            this.elements = elements;
        }
    }
}
