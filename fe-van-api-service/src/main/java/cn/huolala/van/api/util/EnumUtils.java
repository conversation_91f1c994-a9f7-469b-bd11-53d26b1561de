package cn.huolala.van.api.util;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public class EnumUtils {
    private EnumUtils() {
    }

    @Nullable
    @SuppressWarnings("unchecked")
    public static <T extends Enum<T>> T fromString(@NonNull Class<T> clz, @Nullable String what) {
        if (what == null) return null;
        try {
            Enum<T>[] values = clz.getEnumConstants();
            return (T) values[Integer.parseInt(what)];
        } catch (NumberFormatException e) {
            return Enum.valueOf(clz, what);
        }
    }
}
