package cn.huolala.van.api.model.roles;

import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.model.CmdbAppRegion;

import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collector;

/**
 * Regional info map.
 * Example: { cn: { roles: [] }, over_sea: { roles: [] } }
 */
public class RegionalInfoMap extends TreeMap<CmdbAppRegion, RegionalInfoItem> {
    public static <T extends Map.Entry<CmdbAppRegion, Role>> Collector<T, RegionalInfoMap, RegionalInfoMap> collect() {
        return Collector.of(
                RegionalInfoMap::new,
                (rim, v) -> rim.add(v.getKey(), v.getValue()),
                (a, b) -> {
                    a.addAll(b);
                    return a;
                },
                Collector.Characteristics.IDENTITY_FINISH);
    }

    public boolean deepEquals(@Nullable RegionalInfoMap o) {
        if (o == null) return false;
        return Arrays.stream(CmdbAppRegion.values())
                .allMatch(region -> get(region).getRoles().deepEquals(o.get(region).getRoles()));
    }

    @JsonValue
    public Map<String, RegionalInfoItem> toJson() {
        Map<String, RegionalInfoItem> res = new LinkedHashMap<>();
        entrySet().stream()
                .filter(i -> i.getValue().getRoles().notEmpty())
                .forEach(i -> res.put(i.getKey().golangEnumStr, i.getValue()));
        return res;
    }

    @NonNull
    public RegionalInfoItem get(CmdbAppRegion key) {
        return super.computeIfAbsent(key, i -> new RegionalInfoItem());
    }

    public int getRoleBitmap() {
        return values().stream()
                .flatMap(i -> i.getRoles().stream())
                .map(i -> i.bitValue)
                .reduce(0, (a, b) -> a | b);
    }

    /**
     * TODO: Use `computeRoleBitmap` instead.
     */
    public Role getMaxRoleValue() {
        return values().stream()
                .map(i -> i.getRoles().getMaxRoleValue())
                .reduce(Role.NoRole, (a, i) -> a.ordinal() > i.ordinal() ? a : i);
    }

    /**
     * TODO: Use `computeRoleBitmap` instead.
     */
    public boolean checkRole(Role requiredRole) {
        return values().stream()
                .flatMap(i -> i.getRoles().stream())
                .anyMatch(i -> i == requiredRole);
    }

    public void add(CmdbAppRegion region, Role role) {
        compute(region, (k, item) -> {
            if (item == null) item = new RegionalInfoItem();
            item.getRoles().add(role);
            return item;
        });
    }

    public void addAll(RegionalInfoMap value) {
        value.forEach((region, rim) -> get(region).getRoles().addAll(rim.getRoles()));
    }
}
