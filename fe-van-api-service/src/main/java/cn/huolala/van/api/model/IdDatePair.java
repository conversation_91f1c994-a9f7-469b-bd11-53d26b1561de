package cn.huolala.van.api.model;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class IdDatePair {
    private long id;

    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private long epochSecond;

    @NonNull
    public OffsetDateTime getDate() {
        return Instant.ofEpochSecond(epochSecond).atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    public void setDate(@NonNull OffsetDateTime date) {
        epochSecond = date.toEpochSecond();
    }
}
