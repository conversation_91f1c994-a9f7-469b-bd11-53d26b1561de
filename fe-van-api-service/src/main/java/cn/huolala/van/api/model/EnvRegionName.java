package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

@Getter
@Setter
public class EnvRegionName extends EnvRegion {
    @NonNull
    private String name;

    private EnvRegionName(ObjectNode node) {
        super(node);
        name = node.get("name").asText();
    }

    @JsonCreator
    public static EnvRegionName create(ObjectNode node) {
        return new EnvRegionName(node);
    }
}
