package cn.huolala.van.api.util;

import org.springframework.lang.NonNull;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;

public class WithSwr<K, V> implements Function<K, V> {
    @NonNull
    private final ConcurrentHashMap<K, Item<V>> storage;
    @NonNull
    private final Function<K, V> load;
    private final long maxAge;
    private final long stale;

    public WithSwr(@NonNull Function<K, V> load, @NonNull Duration stale, @NonNull Duration maxAge) {
        storage = new ConcurrentHashMap<>();
        this.load = load;
        this.stale = stale.toMillis();
        this.maxAge = maxAge.toMillis();
    }

    public V apply(K key) {
        Item<V> item = storage.computeIfAbsent(key, k -> new Item<>(load.apply(k)));
        long v = item.timestamp.get();
        long n = System.currentTimeMillis();
        if (n - v > maxAge) {
            if (item.timestamp.compareAndSet(v, n)) {
                // Update synchronously.
                item.value.set(load.apply(key));
            }
        } else if (n - v > stale) {
            if (item.timestamp.compareAndSet(v, n)) {
                // Update in background.
                CompletableFuture.runAsync(() -> item.value.set(load.apply(key)));
            }
        } else {
            return item.value.get();
        }
        return item.value.get();
    }

    public void clear() {
        this.storage.clear();
    }

    private static class Item<T> {
        @NonNull
        public final AtomicLong timestamp;
        @NonNull
        public final AtomicReference<T> value;

        public Item(T value) {
            this.timestamp = new AtomicLong(System.currentTimeMillis());
            this.value = new AtomicReference<>(value);
        }
    }
}
