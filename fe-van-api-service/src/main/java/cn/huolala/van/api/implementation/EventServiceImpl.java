package cn.huolala.van.api.implementation;

import cn.huolala.van.api.dao.entity.ProjectEventsEntity;
import cn.huolala.van.api.dao.repository.ProjectEventsRepository;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.events.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.EventService;
import cn.huolala.van.api.service.MetaService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@EnableScheduling
public class EventServiceImpl implements EventService {
    private ProjectEventPolling[] polls;
    @Autowired
    private ProjectEventsRepository projectEventsRepository;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private MetaService metaService;

    @PostConstruct
    private void initPolls() {
        polls = new ProjectEventPolling[]{
                new ProjectEventPolling(this::findProjectEvents),
                new ProjectEventPolling(buildTaskService::findBuildTaskEvents),
                new ProjectEventPolling(metaService::findMetaEvents)
        };
    }

    @NonNull
    private Stream<ProjectEvent> findProjectEvents(long maxKey, int limit) {
        return projectEventsRepository.findAfter(maxKey, limit).stream().map(ProjectEvent::create);
    }

    @Override
    @Deprecated
    public void add(@NonNull UserModel user, @NonNull ProjectModel project, @NonNull ProjectEventContent content) {
        ProjectEventsEntity entity = new ProjectEventsEntity();
        entity.setProject(project.getId());
        entity.setAppId(project.getConfig().getAppId());
        entity.setCreator(user.getUniqId());
        entity.setName(content.fetchType().name());
        try {
            entity.setContent(mapper.writeValueAsString(content));
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
        projectEventsRepository.save(entity);
    }

    @Override
    @NonNull
    public List<ProjectEvent> findEvents(Long projectId, ProjectEventType type) {
        Pageable pageable = PageRequest.of(0, 10);
        return projectEventsRepository.findByNameAndProjectOrderByUpdatedAtDesc(type.name(), projectId, pageable)
                .stream().map(ProjectEvent::create).collect(Collectors.toList());
    }

    @Override
    @NonNull
    public ProjectEventListener createListener(long projectId, @Nullable String lastEventId) {
        return new ProjectEventListener(projectId, lastEventId, polls);
    }

    @Scheduled(fixedRate = 1000)
    public synchronized void pollDatabaseScheduled() {
        Arrays.stream(polls).forEach(ProjectEventPolling::poll);
    }
}
