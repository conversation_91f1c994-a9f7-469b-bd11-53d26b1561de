package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.model.AuditLog;
import cn.huolala.van.api.model.UserLogModel;
import cn.huolala.van.api.model.UserLogSearchParams;
import cn.huolala.van.api.model.UserModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;

public interface UserLogService {
    long addLogs(@NonNull Collection<AuditLog> requests);

    int countMiniprogramByUser(@NonNull UserModel user);

    int countCompositeByUser(@NonNull UserModel user);

    int countWorkflowByUser(@NonNull UserModel user);

    @NonNull
    List<UserLogModel> search(@NonNull UserLogSearchParams searchParams);

    long addLog(
            @NonNull Long userId,
            @NonNull Long projectId,
            @NonNull UserLogType type,
            @Nullable String description,
            @Nullable Object meta);
}
