package cn.huolala.van.api.model.deploy;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import groovy.lang.Tuple1;
import groovy.lang.Tuple2;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Map;

interface CanaryPredicate {
    @JsonCreator
    static CanaryPredicate createFromJson(Map<Object, Object> dirty) {
        if (dirty == null) throw new InternalMappingException("The CanaryPredicate cannot create from a null");
        Class<? extends CanaryPredicate> impl = Type.create(dirty.get("type")).getImpl();
        try {
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            return mapper.convertValue(dirty, impl);
        } catch (IllegalArgumentException e) {
            throw new InternalMappingException(e);
        }
    }

    @NonNull
    Type getType();

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    enum Type {
        unknown(Unknown.class),
        equal(Equal.class),
        in(In.class),
        like(Like.class),
        random(Random.class),
        ip(Ip.class),
        http(Http.class);

        private final Class<? extends CanaryPredicate> impl;

        Type(Class<? extends CanaryPredicate> impl) {
            this.impl = impl;
        }

        @NonNull
        public static Type create(Object mayBeString) {
            return Arrays.stream(values()).filter(i -> i.name().equals(mayBeString)).findFirst().orElse(unknown);
        }

        @NonNull
        public static Type toType(Class<? extends CanaryPredicate> impl) {
            return Arrays.stream(values()).filter(i -> i.getImpl().equals(impl)).findFirst().orElse(unknown);
        }

        public Class<? extends CanaryPredicate> getImpl() {
            return impl;
        }
    }

    interface VariableKey {
        @JsonCreator
        static VariableKey create(Object[] args) {
            if (args == null) return null;
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            switch (args.length) {
                case 1:
                    return new ForString(mapper.convertValue(args[0], ForString.Kind.class));
                case 2:
                    return new ForMap(
                            mapper.convertValue(args[0], ForMap.Kind.class),
                            mapper.convertValue(args[1], String.class));
                default:
                    return new ForUnknown();
            }
        }

        @Nullable
        static VariableKey fromLegacyKey(@Nullable String s) {
            VariableKey vk = ForString.fromLegacyKey(s);
            if (vk != null) return vk;
            return ForMap.fromLegacyKey(s);
        }

        class ForMap extends Tuple2<ForMap.Kind, String> implements VariableKey {
            public ForMap(Kind first, String second) {
                super(first, second);
            }

            @Nullable
            public static ForMap fromLegacyKey(@Nullable String s) {
                if (s == null) return null;
                for (Kind i : Kind.values()) {
                    int il = i.name().length();
                    if (s.length() > il && ':' == s.charAt(il) && s.startsWith(i.name())) {
                        return new ForMap(i, s.substring(il + 1));
                    }
                }
                return null;
            }

            @JsonFormat(shape = JsonFormat.Shape.STRING)
            public enum Kind {
                header, query, cookie;

                @NonNull
                public ForMap tail(@NonNull String name) {
                    return new ForMap(this, name);
                }
            }
        }

        class ForString extends Tuple1<ForString.Kind> implements VariableKey {
            public ForString(ForString.Kind first) {
                super(first);
            }

            @Nullable
            public static ForString fromLegacyKey(@Nullable String s) {
                if (s == null) return null;
                for (ForString.Kind i : ForString.Kind.values()) {
                    if (i.name().equals(s)) {
                        return new ForString(i);
                    }
                }
                return null;
            }

            @JsonFormat(shape = JsonFormat.Shape.STRING)
            enum Kind {
                path, host
            }
        }

        class ForUnknown extends Tuple1<ForUnknown.Kind> implements VariableKey {
            public ForUnknown() {
                super(Kind.unknown);
            }

            @JsonFormat(shape = JsonFormat.Shape.STRING)
            enum Kind {
                unknown
            }
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    class Unknown implements CanaryPredicate {
        @Nullable
        private Object raw;

        public Unknown(@NonNull Object raw) {
            this.raw = raw;
        }

        @Override
        @NonNull
        @ApiModelProperty(allowableValues = "unknown")
        public Type getType() {
            return Type.unknown;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class Equal implements CanaryPredicate {
        @NonNull
        private VariableKey key = new VariableKey.ForUnknown();
        @NonNull
        private String value = "";
        private boolean invert;

        @Override
        @NonNull
        @ApiModelProperty(allowableValues = "equal")
        public Type getType() {
            return Type.equal;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class In implements CanaryPredicate {
        @NonNull
        private VariableKey key = new VariableKey.ForUnknown();
        @NonNull
        private String[] value = new String[0];

        private boolean invert;

        @Override
        @NonNull
        @ApiModelProperty(allowableValues = "in")
        public Type getType() {
            return Type.in;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class Like implements CanaryPredicate {
        @NonNull
        private VariableKey key = new VariableKey.ForUnknown();
        @NonNull
        private String value = "";
        private boolean invert;

        @Override
        @NonNull
        @ApiModelProperty(allowableValues = "like")
        public Type getType() {
            return Type.like;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class Random implements CanaryPredicate {
        private Number value = 0;

        @Override
        @NonNull
        @ApiModelProperty(allowableValues = "random")
        public Type getType() {
            return Type.random;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class Ip implements CanaryPredicate {
        @NonNull
        private String value = "0.0.0.0/0";
        private boolean invert = false;

        @Override
        @NonNull
        @ApiModelProperty(allowableValues = "ip")
        public Type getType() {
            return Type.ip;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Http implements CanaryPredicate {
        @NonNull
        private String url = "";
        @Nullable
        private Object extParams;

        @Override
        @NonNull
        @ApiModelProperty(allowableValues = "http")
        public Type getType() {
            return Type.http;
        }

        public static class Regions {

        }
    }
}
