package cn.huolala.van.api.model;

import cn.lalaframework.logging.LoggerFactory;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.function.Consumer;

@Builder
@Getter
@Setter
public class IconResultModel implements Consumer<HttpServletResponse> {
    private static final Logger LOGGER = LoggerFactory.getLogger();

    private Long projectId;
    private Long taskId;
    private String location;
    private int status;
    private String type;
    private byte[] body;

    @Override
    public void accept(HttpServletResponse res) {
        res.setStatus(status);
        if (type != null) res.setContentType(type);
        if (location != null) res.setHeader("Location", location);
        res.setHeader("Cache-Control", "max-age=30");
        if (body != null) {
            try {
                res.getOutputStream().write(body);
            } catch (IOException e) {
                LOGGER.error(e.getMessage());
            }
        }
    }
}
