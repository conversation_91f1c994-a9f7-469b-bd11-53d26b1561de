package cn.huolala.van.api.model.deploy;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.deploy.CanaryPredicate.VariableKey;
import cn.huolala.van.api.model.deploy.CanaryPredicate.VariableKey.ForMap.Kind;
import cn.huolala.van.api.util.EncodingUtils;
import cn.huolala.van.api.util.RegExpToLikeConverter;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.lang.String.format;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
@AllArgsConstructor
public class LegacyDeployConfig {
    @Nullable
    private Long defaultTaskId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Nullable
    private List<Rule> canary;

    public LegacyDeployConfig(@NonNull Long taskId) {
        defaultTaskId = taskId;
    }

    public Set<Long> collectTaskIds() {
        Set<Long> list = new LinkedHashSet<>();
        if (canary != null) canary.stream().map(Rule::getTaskId).filter(Objects::nonNull).forEach(list::add);
        if (defaultTaskId != null) list.add(defaultTaskId);
        return list;
    }

    @NonNull
    public List<CanaryRule> toCanaryRules() {
        return toCanaryRules(false);
    }

    @NonNull
    public List<CanaryRule> toCanaryRules(boolean neverThrow) {
        List<CanaryRule> list = new ArrayList<>();
        if (canary != null)
            canary.stream().filter(Objects::nonNull).map(r -> r.toCanaryRule(neverThrow)).forEach(list::add);
        if (defaultTaskId != null)
            list.add(new CanaryRule("", CanaryRule.Mode.every, new ArrayList<>(), defaultTaskId, ""));
        return list;
    }

    public boolean hasCanary() {
        return canary != null && !canary.isEmpty();
    }

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Rule {
        private Long taskId;
        private String canaryId;
        private List<Operator> operatorChain;
        private String description;

        public CanaryRule toCanaryRule(boolean neverThrow) {
            if (operatorChain.size() == 1 && operatorChain.get(0).getLeft().equals("city-v2")) {
                ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
                String[] value = mapper.convertValue(operatorChain.get(0).getRight(), String[].class);
                String xHllCityId = "x-hll-city-id";
                List<CanaryPredicate.In> predicates = Stream.of(
                        Kind.cookie.tail(xHllCityId),
                        Kind.header.tail(xHllCityId),
                        Kind.query.tail(xHllCityId),
                        Kind.query.tail("city_id"),
                        Kind.query.tail("city-id")
                ).map(key -> new CanaryPredicate.In(key, value, false)).collect(Collectors.toList());
                return new CanaryRule(canaryId, CanaryRule.Mode.some, predicates, taskId, description);
            } else {
                List<? extends CanaryPredicate> predicates = operatorChain.stream().map(o -> {
                    try {
                        return o.toCanaryPredicate();
                    } catch (RuntimeException e) {
                        if (neverThrow) return new CanaryPredicate.Unknown(o);
                        throw e;
                    }
                }).collect(Collectors.toList());
                // Never match empty rule
                CanaryRule.Mode mode = predicates.isEmpty() ? CanaryRule.Mode.some : CanaryRule.Mode.every;
                return new CanaryRule(canaryId, mode, predicates, taskId, description);
            }
        }
    }

    @Getter
    @Setter
    public static class Operator {
        private static final String pageServerCanaryBase = "http://gateway-inner.myhll.cn/fe-tools-svc/page-server-canary";

        private String left;
        private String op;
        private Object right;
        private String type;

        public CanaryPredicate toCanaryPredicate() {
            if ("region".equals(left)) return toRegionPredicate();
            if ("sso".equals(left)) return toSsoPredicate();
            if ("lalaplat".equals(left)) return toLalaplatPredicate();

            if ("random".equals(left)) {
                assertOperator("eq");
                assertRightType(Number.class);
                return new CanaryPredicate.Random((Number) right);
            }

            if ("ip".equals(left)) {
                assertOperator("cidr");
                assertRightType(String.class);
                return new CanaryPredicate.Ip((String) right, false);
            }

            if ("user-agent".equals(left)) {
                return CompareOp.valueOf(op).convert(Kind.header.tail(left), right);
            }

            VariableKey vk = VariableKey.fromLegacyKey(left);
            if (vk != null) return CompareOp.valueOf(op).convert(vk, right);

            throw new InternalMappingException("Failed to convert " + this + " to CanaryPredicate");
        }

        @NonNull
        private CanaryPredicate.Http toLalaplatPredicate() {
            assertOperator("in");
            assertRightType(List.class);
            List<String> versions = ((List<?>) right).stream()
                    .map(i -> i instanceof String ? (String) i : null)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            return new CanaryPredicate.Http("http://127.0.0.1:8081/lalaplat", Maps.of("versions", versions));
        }

        @NonNull
        private CanaryPredicate.Http toSsoPredicate() {
            assertOperator("in");
            assertRightType(List.class);
            List<String> accounts = ((List<?>) right).stream()
                    .map(i -> i instanceof String ? (String) i : null)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            Map<String, List<String>> params = Maps.of("accounts", accounts);
            return new CanaryPredicate.Http(pageServerCanaryBase + "/sso-canary", params);
        }

        private void assertOperator(@NonNull String expected) {
            assertWhile(
                    expected.equals(op),
                    format("The operator must be '%s' but got '%s'", expected, op));
        }

        private void assertRightType(@NonNull Class<?> type) {
            Objects.requireNonNull(right, "The right must not be null");
            String expected = right.getClass().getSimpleName();
            assertWhile(
                    type.isAssignableFrom(right.getClass()),
                    format("The right must be '%s' but got '%s'", type.getSimpleName(), expected));
        }

        private void assertWhile(boolean condition, @NonNull String message) {
            if (condition) return;
            throw new VanBadRequestException(format("%s while the left is '%s'", message, left));
        }

        @NonNull
        private CanaryPredicate.Http toRegionPredicate() {
            assertRightType(String.class);
            String[] regions = null;
            if ("re".equals(op)) {
                String s = EncodingUtils.trimLeftAndRight((String) right, "^(", ")");
                assertWhile(s != null, format("The RegExp /%s/ is not support", right));
                regions = StringUtils.split(s, '|');
            }
            if ("eq".equals(op)) {
                regions = new String[]{(String) right};
            }
            if (regions == null) {
                throw new VanBadRequestException(format("Unsupported operation '%s' while the left is '%s'", op, left));
            }
            return new CanaryPredicate.Http(pageServerCanaryBase + "/ip-canary", Maps.of("regions", regions));
        }

        @Override
        public String toString() {
            return format("Operator(%s, %s, %s)", left, op, right);
        }

        enum CompareOp {
            eq, neq, in, nin, re;

            @NonNull
            public CanaryPredicate convert(VariableKey key, Object value) {
                ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
                switch (this) {
                    case eq:
                        return new CanaryPredicate.Equal(key, mapper.convertValue(value, String.class), false);
                    case neq:
                        return new CanaryPredicate.Equal(key, mapper.convertValue(value, String.class), true);
                    case in:
                        return new CanaryPredicate.In(key, mapper.convertValue(value, String[].class), false);
                    case nin:
                        return new CanaryPredicate.In(key, mapper.convertValue(value, String[].class), true);
                    case re:
                        String pattern = mapper.convertValue(value, String.class);

                        String sps = EncodingUtils.trimLeftAndRight(pattern, "^(", ")$");
                        if (sps != null) {
                            // NOTE: Just simply split the value, some special characters have not been processed.
                            String[] values = StringUtils.split(sps, '|');
                            return new CanaryPredicate.In(key, values, false);
                        }

                        String likeExpression = new RegExpToLikeConverter(pattern).toString();
                        return new CanaryPredicate.Like(key, likeExpression, false);
                }
                throw new InternalMappingException("?");
            }
        }
    }
}

