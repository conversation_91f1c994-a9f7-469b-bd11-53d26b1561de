package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class VanHook {
    @NonNull
    private String name = "";

    @Nullable
    private List<String> branch;
    @Nullable
    private Integer timeout;
    @Nullable
    private List<Timing> timing;
    @Nullable
    private String version;

    @Nullable
    private Boolean throwError;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum Timing {
        @JsonEnumDefaultValue
        unknown, before, after
    }
}
