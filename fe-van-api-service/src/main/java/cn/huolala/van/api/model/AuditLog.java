package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.UserLogType;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.LinkedHashMap;

@Getter
public class AuditLog {
    @NonNull
    private final Long userId;
    @NonNull
    private final Long projectId;
    @NonNull
    private final UserLogType type;
    @NonNull
    private String description;
    @NonNull
    private Object meta;

    public AuditLog(long userId,
                    long projectId,
                    @NonNull UserLogType type,
                    @Nullable String description,
                    @Nullable Object meta) {
        this.userId = userId;
        this.projectId = projectId;
        this.type = type;
        this.description = StringUtils.defaultString(description);
        this.meta = meta == null ? new Meta() : meta;
    }

    @NonNull
    private static Meta convertToMeta(@NonNull Object object) {
        if (object instanceof Meta) return (Meta) object;
        return ApplicationContextUtil.getBean(ObjectMapper.class).convertValue(object, Meta.class);
    }

    @NonNull
    @CanIgnoreReturnValue
    public AuditLog message(@NonNull String description) {
        this.description = description;
        return this;
    }

    @NonNull
    @CanIgnoreReturnValue
    public AuditLog put(@NonNull String key, @Nullable Object value) {
        if (!(meta instanceof Meta)) meta = convertToMeta(meta);
        ((Meta) meta).put(key, value);
        return this;
    }

    @NonNull
    @CanIgnoreReturnValue
    public AuditLog putObject(@Nullable Object object) {
        if (object == null) return this;
        if (!(meta instanceof Meta)) meta = convertToMeta(meta);
        if (((Meta) meta).isEmpty()) {
            meta = object;
        } else {
            ((Meta) meta).putAll(convertToMeta(object));
        }
        return this;
    }

    public static class Meta extends LinkedHashMap<String, Object> {
    }
}
