package cn.huolala.van.api.model.meta;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.hooks.HookParamsBase;
import cn.huolala.van.api.model.hooks.HookParamsForDev;
import cn.huolala.van.api.model.hooks.HookParamsForPrd;
import cn.huolala.van.api.model.hooks.HookResult;
import cn.huolala.van.api.util.WorkersHelper;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Set;

@Getter
@Setter
public class DeployHookModel {
    @Nullable
    private Meta meta;
    @Nullable
    private Set<Timing> timing;
    @Nullable
    private Set<DeployHookEnv> env;
    @Nullable
    private Integer timeout;
    @Nullable
    private Boolean ignoreError;

    @Nullable
    public DeployHookRunner match(@NonNull Timing timing, @NonNull Env env) {
        if (this.timing == null || this.env == null || this.meta == null) return null;
        if (!this.timing.contains(timing) || !this.env.contains(DeployHookEnv.fromEnv(env))) return null;
        String methodName = timing.getWorkersMethodName();
        if (methodName == null) return null;
        String serviceName = this.meta.getName();
        if (serviceName == null) return null;
        return new DeployHookRunner(
                serviceName, methodName, timing,
                timeout == null ? 2000 : timeout,
                ignoreError != null && ignoreError);
    }

    public void execute(Timing timing, HookParamsForPrd hookParams) {
        DeployHookRunner runner = match(timing, Env.prd);
        if (runner != null) runner.execute(hookParams);
    }

    public void execute(Timing timing, HookParamsForDev hookParams) {
        DeployHookRunner runner = match(timing, hookParams.getEnv());
        if (runner != null) runner.execute(hookParams);
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Getter
    public enum Timing {
        @JsonEnumDefaultValue
        unknown(null, Void.class),
        before("beforeDeployHook", HookResult.class),
        after("afterDeployHook", Void.class);

        @Nullable
        private final String workersMethodName;
        private final Class<?> resultType;

        Timing(@Nullable String workersMethodName, @NonNull Class<?> resultType) {
            this.workersMethodName = workersMethodName;
            this.resultType = resultType;
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Getter
    public enum DeployHookEnv {
        @JsonEnumDefaultValue
        unknown(Sets.newHashSet()),
        dev(Sets.newHashSet(Env.stg, Env.pre)),
        prod(Sets.newHashSet(Env.prd));

        @NonNull
        private final Set<Env> envs;

        DeployHookEnv(@NonNull Set<Env> envs) {
            this.envs = envs;
        }

        @NonNull
        public static DeployHookEnv fromEnv(Env env) {
            return Arrays.stream(DeployHookEnv.values())
                    .filter(i -> i.getEnvs().contains(env))
                    .findFirst().orElse(unknown);
        }
    }

    @Setter
    @Getter
    public static class Meta {
        private String name;
    }

    @Getter
    @AllArgsConstructor
    public static class DeployHookRunner {
        @NonNull
        private final String serviceName;
        @NonNull
        private final String methodName;
        @NonNull
        private final Timing timing;

        private final int timeoutInMs;
        private final boolean ignoreError;

        public <T extends HookParamsBase> void execute(T params) {
            Object result;
            try {
                result = WorkersHelper.invoke(serviceName, methodName, params, timeoutInMs, timing.getResultType());
            } catch (InternalRequestException e) {
                if (!ignoreError) throw e;
                result = null;
            }
            if (result instanceof HookResult && ((HookResult) result).isBlockPublish()) {
                throw new VanBadRequestException(String.format(
                        "Operation has blocked by hook: %s: %s",
                        serviceName,
                        ((HookResult) result).getMessage()));
            }
        }
    }
}
