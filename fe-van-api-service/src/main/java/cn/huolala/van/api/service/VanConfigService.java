package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.model.WatchDogAllConfigView;
import cn.huolala.van.api.model.config.VanConfig;
import cn.huolala.van.api.model.config.VanConfigFile;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.project.ProjectModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

public interface VanConfigService {
    @NonNull
    List<ProjectEvent> getProjectFileEvent(@NonNull ProjectModel project);

    void createConfig(@NonNull ProjectModel project, @NonNull VanConfig config);

    @NonNull
    VanConfig updateConfig(@NonNull ProjectModel project, @NonNull VanConfig config);

    @NonNull
    VanConfig getAndAssertConfig(@NonNull Env env, @NonNull String projectName, @NonNull String fileName);

    @Nullable
    VanConfig getConfig(@NonNull Env env, @NonNull String projectName, @NonNull String fileName);

    @NonNull
    List<VanConfigFile> listConfigName(@NonNull Env env, @NonNull ProjectModel project);

    @NonNull
    WatchDogAllConfigView getWatchDogAllConfigView(@NonNull Env env);
}
