package cn.huolala.van.api.implementation;

import cn.huolala.van.api.dao.entity.MonitorRecordEntity;
import cn.huolala.van.api.dao.entity.MonitorRecordProjectEntity;
import cn.huolala.van.api.dao.entity.UserEntity;
import cn.huolala.van.api.dao.projection.SimpleMonitorRecordProjection;
import cn.huolala.van.api.dao.repository.MonitorRecordProjectRespository;
import cn.huolala.van.api.dao.repository.UserRepository;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.MiniProgramDetail.FieldUpperDetail;
import cn.huolala.van.api.model.coupe.CoupeVanConfig;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.elatic.ApiData;
import cn.huolala.van.api.model.elatic.FieldData;
import cn.huolala.van.api.model.elatic.MetricData;
import cn.huolala.van.api.model.elatic.PageViewInfo;
import cn.huolala.van.api.model.elatic.TimeRange;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.FeishuService;
import cn.huolala.van.api.service.HistoryService;
import cn.huolala.van.api.service.MonitorRecordService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.util.ModelUtils;
import cn.huolala.van.api.util.WithSwr;
import cn.lalaweb.coupe.Coupe;
import cn.lalaweb.tools.facade.params.GroupProjectErrorStatus;
import cn.lalaweb.tools.facade.services.ErrorService;
import cn.lalaweb.tools.params.ErrorTimeRangeName;
import cn.lalaweb.tools.params.IssueStatus;
import co.elastic.clients.elasticsearch._types.ElasticsearchException;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.criteria.*;

import java.io.IOException;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class MonitorRecordServiceImpl implements MonitorRecordService {
    @NonNull
    private final WithSwr<ImportantProjectsParams, List<ImportantProjectView>> importantProjectsCache;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private MonitorRecordProjectRespository monitorRecordRespository;
    @Autowired
    private FeishuService feishuService;
    @Value("${van.feishu.secret}")
    private String vanFeishuSecret;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private ErrorService errorService;
    @Resource
    private Coupe<CoupeVanConfig> coupeVan;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private ElaticSearchServiceImpl elaticSearchService;

    public MonitorRecordServiceImpl() {
        importantProjectsCache = new WithSwr<>(
                this::getImportantProjectsWithoutCache,
                Duration.ofMinutes(10),
                Duration.ofDays(5));
    }

    private Date getYesterDayStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    private Date getLastWeekYesterDayStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -8);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    private void sendUserProjectsYesterDayMonitorRecordMessageByUser(UserEntity userEntity) {
        List<MonitorRecordProjectEntity> projects = monitorRecordRespository
                .findMonitorRecordsByUser(userEntity.getId(), getYesterDayStart());
        List<Long> projectIds = projects.stream().map(MonitorRecordProjectEntity::getId).collect(Collectors.toList());
        List<MonitorRecordProjectEntity> lastWeekProjects = monitorRecordRespository
                .findMonitorRecordsByProjectIds(projectIds, getLastWeekYesterDayStart());
        if (!projects.isEmpty()) {
            MonitorRecordProjectsMessage render = new MonitorRecordProjectsMessage();
            MonitorRecordProjectsMessage.Message message = render.getMessage(projects, lastWeekProjects,
                    userEntity.getUniqId(), vanFeishuSecret);
            String text;
            try {
                text = mapper.writeValueAsString(message);
            } catch (JsonProcessingException e) {
                throw new InternalMappingException(e);
            }
            feishuService.sendVanCardByUser(text, UserModel.wrap(userEntity));
        }
    }

    @Async
    public void sendUserProjectsYesterDayMonitorRecordMessage(List<String> userUniqIds) {
        List<UserEntity> users = userRepository.findByUniqIds(userUniqIds);

        for (UserEntity userEntity : users) {
            try {
                sendUserProjectsYesterDayMonitorRecordMessageByUser(userEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Async
    public void sendProjectsYesterDayMonitorRecordMessage() {
        List<UserEntity> users = userRepository.findMonitorRecordUsersByRecordAt(getYesterDayStart());
        if (users == null) {
            return;
        }

        for (UserEntity userEntity : users) {
            try {
                sendUserProjectsYesterDayMonitorRecordMessageByUser(userEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @NonNull
    @Override
    public List<ImportantProjectView> getImportantProjects(@NonNull ImportantProjectsParams params) {
        return importantProjectsCache.apply(params);

    }

    @Override
    public void clearImportantProjectsCache() {
        importantProjectsCache.clear();
    }

    @Override
    @NonNull
    public List<SimpleMonitorRecordModel> query(
            long projectId,
            int limit,
            double minimalPv,
            @Nullable OffsetDateTime startTime,
            @Nullable OffsetDateTime endTime) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<SimpleMonitorRecordProjection> query = cb.createQuery(SimpleMonitorRecordProjection.class);
        Root<MonitorRecordEntity> root = query.from(MonitorRecordEntity.class);

        Path<LocalDateTime> xRecordAt = root.get("recordAt");
        Path<Long> xProjectId = root.get("projectId");
        Path<Double> xPageViewTotal = root.get("pageViewTotal");

        query.select(cb.construct(
                SimpleMonitorRecordProjection.Impl.class,
                xProjectId,
                root.get("score"),
                xPageViewTotal,
                xRecordAt));

        ZoneId z = ZoneId.systemDefault();

        query.where(
                Stream.of(
                        cb.equal(xProjectId, projectId),
                        cb.greaterThan(xPageViewTotal, minimalPv),
                        startTime == null
                                ? null
                                : cb.greaterThan(xRecordAt, startTime.atZoneSameInstant(z).toLocalDateTime()),
                        endTime == null
                                ? null
                                : cb.lessThanOrEqualTo(xRecordAt, endTime.atZoneSameInstant(z).toLocalDateTime()))
                        .filter(Objects::nonNull).toArray(Predicate[]::new));

        query.orderBy(cb.desc(xRecordAt));

        return entityManager.createQuery(query).setMaxResults(limit).getResultList().stream()
                .map(SimpleMonitorRecordModel::from).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @NonNull
    @Override
    public List<ImportantProjectView> getImportantProjectsWithoutCache(@NonNull ImportantProjectsParams params) {
        CompletableFuture<List<String>> tBlueChipProjects = CompletableFuture.supplyAsync(this::getBlueChipProjects);
        List<String> excludedProjects = getExcludedProjects();
        List<String> blueChipProjects = tBlueChipProjects.join();

        if (params.getDate().toEpochDay() > LocalDate.now().toEpochDay()) {
            throw new VanBadRequestException("The `date` parameter must be prior to the present time");
        }

        HashMap<String, Object> args = new HashMap<>();
        StringBuilder sql = new StringBuilder("/* MiscController.getImportantProjects */\n");

        sql.append("SELECT\n");
        sql.append("  p.name AS name,\n");
        sql.append("  SUM(page_view_total) AS pv,\n");
        if (blueChipProjects.isEmpty()) {
            sql.append("  false AS blue_chip\n");
        } else {
            sql.append("  p.name IN :blueChipProjects AS blue_chip\n");
            args.put("blueChipProjects", getBlueChipProjects());
        }

        sql.append("FROM monitor_record m, projects p\n");

        sql.append("WHERE\n");
        sql.append("  p.id = m.project_id\n");
        sql.append("  AND m.created_at >= :startDate\n");
        sql.append("  AND m.created_at < :endDate\n");
        args.put("startDate", params.getDate().minusMonths(3));
        args.put("endDate", params.getDate());

        if (!excludedProjects.isEmpty()) {
            sql.append("  AND p.name NOT IN :excludedProjects\n");
            args.put("excludedProjects", excludedProjects);
        }

        sql.append("GROUP BY name\n");
        sql.append("ORDER BY blue_chip DESC, pv DESC\n");

        Query query = entityManager.createNativeQuery(sql.toString(), ImportantProjectView.class);
        args.forEach(query::setParameter);
        query.setMaxResults(params.getLimit());

        @SuppressWarnings("unchecked")
        List<ImportantProjectView> resultList = query.getResultList();

        return resultList;
    }

    private List<String> getBlueChipProjects() {
        // https://huolala.feishu.cn/wiki/P2tkw8Bzdixyr4kg36ncRWHmnqh
        return feishuService
                .getSheet("UaOGs0NzshYBhUtTr8tcaayRnmb", "RaTuB2", null)
                .stream().skip(1)
                .map(i -> i instanceof List ? ((List<?>) i).get(0) : null)
                .map(i -> i instanceof String ? (String) i : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private List<String> getExcludedProjects() {
        // https://huolala.feishu.cn/wiki/XrGowX2G5i6SHrky4eTc1zYen8g
        return feishuService
                .getSheet("Tnamsrawahh1PZt4bhQcQtqOnUd", "upG5dF", null)
                .stream().skip(1)
                .map(i -> i instanceof List ? ((List<?>) i).get(0) : null)
                .map(i -> i instanceof String ? (String) i : null)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    @NonNull
    public List<Optional<MonitorRecordDetail>> batchGetDetail(@NonNull List<IdDatePair> idpList) {
        final int length = idpList.size();
        if (length == 0)
            return Collections.emptyList();
        final StringBuilder sql = new StringBuilder("/* batchGetDetail */\n");
        final List<Object> args = new ArrayList<>();
        sql.append("SELECT project_id, record_at, content FROM monitor_record m\n");
        sql.append("WHERE (project_id, record_at) in (\n");
        for (int i = 0; i < length; i++) {
            IdDatePair pair = idpList.get(i);
            args.add(pair.getId());
            args.add(pair.getDate().atZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime().toString());
            sql.append(i > 0 ? "  ," : "  ");
            sql.append("(?, ?)\n");
        }
        sql.append(")\n");
        Query query = entityManager.createNativeQuery(sql.toString());
        for (int i = 0; i < args.size(); i++) {
            query.setParameter(i + 1, args.get(i));
        }

        @SuppressWarnings("unchecked")
        List<Object[]> resultList = query.getResultList();

        return resultList.stream().map(row -> {
            MonitorRecordDetail value = MonitorRecordDetail.fromRaw(row[2]);
            if (value == null)
                return null;
            return Pair.of(
                    new IdDatePair(
                            ((BigInteger) row[0]).longValue(),
                            ((Timestamp) row[1]).toInstant().getEpochSecond()),
                    value);
        }).filter(Objects::nonNull).collect(ModelUtils.toAlignedList(idpList));
    }

    private TimeRange getYesterdayTimeRange() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS");
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDate yesterday = LocalDate.now(zoneId).minusDays(1);
        LocalDateTime startOfDay = yesterday.atStartOfDay();
        String startTime = startOfDay.atZone(zoneId).format(formatter) + "Z";
        LocalDateTime endOfDay = yesterday.atTime(23, 59, 59);
        String endTime = endOfDay.atZone(zoneId).format(formatter) + "Z";
        TimeRange timeRange = new TimeRange();
        timeRange.setGte(startTime);
        timeRange.setLte(endTime);
        return timeRange;
    }

    public void getLightAppProjectScoreById(Long projectId) throws Exception {
        Optional<ProjectModel> projectResult = projectService.get(projectId);
        if (!projectResult.isPresent()) {
            return;
        }

        ProjectModel project = projectResult.get();
        getLightAppProjectScoreByName(project.getName());
    }

    public MiniProgramDetail.Error getProjectNotResolveIssue(String projectName) {
        List<IssueStatus> status = new ArrayList<>();
        status.add(IssueStatus.NEW);
        status.add(IssueStatus.IN_PROGRESS);
        status.add(IssueStatus.WONT_FIX);
        status.add(IssueStatus.FIXED);
        status.add(IssueStatus.DELAY);
        status.add(IssueStatus.REOPEN);
        status.add(IssueStatus.DUPLICATE);

        List<String> projects = new ArrayList<>();
        projects.add(projectName);

        List<Long> taskIds = null;
        String env = "prod";
        List<String> level = new ArrayList<>();
        level.add("info");

        ErrorTimeRangeName timeRangeName = ErrorTimeRangeName.INCEPTION_TIME;
        Date from = null;
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -14);
        Date sevenDaysAgo = calendar.getTime();
        try {
            List<GroupProjectErrorStatus> results = errorService.groupProjectErrorDetailByStatus(status, projects,
                    taskIds, env, level, timeRangeName, from,
                    sevenDaysAgo);
            if (results == null || results.isEmpty()) {
                return null;
            }

            MiniProgramDetail.Error error = new MiniProgramDetail.Error();
            int notResolveErrorTotal = 0;
            List<FieldData> detail = new ArrayList<>();
            for (GroupProjectErrorStatus r : results) {
                notResolveErrorTotal = notResolveErrorTotal + r.getTotal().intValue();
                FieldData d = new FieldData(mapper.writeValueAsString(r.getStatus()), r.getTotal());
                detail.add(d);
            }
            error.setDetail(detail);
            error.setNotResolveErrorTotal(notResolveErrorTotal);
            return error;
        } catch (Exception e) {
            return null;
        }
    }

    public MonitorRecordDetail getLightAppProjectScoreByDetail(MiniProgramDetail miniProgramDetail)
            throws ElasticsearchException, IOException {
        MonitorRecordDetail detail = new MonitorRecordDetail();
        MiniProgramScoreSummary miniProgramScoreSummary = coupeVan.getDefault().getMiniProgramScoreSummary().clone();

        if (miniProgramDetail.getApi() != null) {
            miniProgramScoreSummary.getApiDurationUpperTime()
                    .addValue(miniProgramDetail.getApi().getDurationUpperTime());

            double total = miniProgramDetail.getApi().getTotal();
            double failTotal = miniProgramDetail.getFailApi().getTotal();
            if (total != 0) {
                miniProgramScoreSummary.getApiSuccessRate().addValue((total - failTotal) / total);
            } else {
                miniProgramScoreSummary.getApiSuccessRate().addValue(null);
            }
        } else {
            miniProgramScoreSummary.getApiDurationUpperTime().addValue(null);
        }

        if (miniProgramDetail.getLoadPackage() != null) {
            miniProgramScoreSummary.getLoadPackageDurationUpperTime()
                    .addValue(miniProgramDetail.getLoadPackage().getUpperValue());
        } else {
            miniProgramScoreSummary.getLoadPackageDurationUpperTime().addValue(null);
        }

        if (miniProgramDetail.getAppLaunch() != null) {
            miniProgramScoreSummary.getAppLaunchDurationUpperTime()
                    .addValue(miniProgramDetail.getAppLaunch().getUpperValue());
        } else {
            miniProgramScoreSummary.getAppLaunchDurationUpperTime().addValue(null);
        }

        if (miniProgramDetail.getPageLoad() != null) {
            miniProgramScoreSummary.getPageLoadDurationUpperTime()
                    .addValue(miniProgramDetail.getPageLoad().getUpperValue());
        } else {
            miniProgramScoreSummary.getPageLoadDurationUpperTime().addValue(null);
        }

        if (miniProgramDetail.getEvaluateScript() != null) {
            miniProgramScoreSummary.getEvaluateScriptDurationUpperTime()
                    .addValue(miniProgramDetail.getEvaluateScript().getUpperValue());
        } else {
            miniProgramScoreSummary.getEvaluateScriptDurationUpperTime().addValue(null);
        }

        if (miniProgramDetail.getMetric() != null) {
            miniProgramScoreSummary.getFcpDurationUpperTime()
                    .addValue(miniProgramDetail.getMetric().getFcpUpperTime());
            miniProgramScoreSummary.getFcpDurationUpperTime()
                    .addValue(miniProgramDetail.getMetric().getFcpUpperTime());
            miniProgramScoreSummary.getLcpDurationUpperTime()
                    .addValue(miniProgramDetail.getMetric().getLcpUpperTime());
            miniProgramScoreSummary.getFirstInteractionDurationUpperTime()
                    .addValue(miniProgramDetail.getMetric().getFirstInteractionTimeUpperTime());
            miniProgramScoreSummary.getPageFirstRenderDurationUpperTime()
                    .addValue(miniProgramDetail.getMetric().getPageFirstRenderTimeUpperTime());
            miniProgramScoreSummary.getFirstPaintDurationUpperTime()
                    .addValue(miniProgramDetail.getMetric().getFirstPaintUpperTime());
        } else {
            miniProgramScoreSummary.getFcpDurationUpperTime().addValue(null);
            miniProgramScoreSummary.getLcpDurationUpperTime().addValue(null);
            miniProgramScoreSummary.getFirstInteractionDurationUpperTime().addValue(null);
            miniProgramScoreSummary.getPageFirstRenderDurationUpperTime().addValue(null);
            miniProgramScoreSummary.getFirstPaintDurationUpperTime().addValue(null);
        }

        if (miniProgramDetail.getError() != null) {
            miniProgramScoreSummary.getNotResolveError()
                    .addValue((double) miniProgramDetail.getError().getNotResolveErrorTotal());
        } else {
            miniProgramScoreSummary.getNotResolveError().addValue(null);
        }

        miniProgramScoreSummary.caculate();
        detail.setMiniProgramScoreSummary(miniProgramScoreSummary);
        detail.setMiniProgramDetail(miniProgramDetail);
        return detail;
    }

    public void saveLightAppProjectScoreByName(String projectName) throws Exception {
        Long projectId = projectService.getIdByName(projectName);
        if (projectId == null) {
            return;
        }

        MonitorRecordDetail detail = getLightAppProjectScoreByName(projectName);
        long pv = detail.getMiniProgramDetail().getPv();
        double score = detail.getMiniProgramScoreSummary().getScore();
        String content = mapper.writeValueAsString(detail);
        monitorRecordRespository.insert(projectId, score, pv,
                content, getYesterDayStart());
    }

    public MonitorRecordDetail getLightAppProjectScoreByName(String projectName)
            throws Exception {
        MiniProgramDetail miniProgramDetail = new MiniProgramDetail();
        PageViewInfo pageViewInfo = elaticSearchService.getProjectViewInfo(projectName, getYesterdayTimeRange());
        miniProgramDetail.setPv(pageViewInfo.getPv());
        miniProgramDetail.setUv(pageViewInfo.getUv());
        miniProgramDetail.setSystemInfo(pageViewInfo.getSystemInfo());

        ApiData apiData = elaticSearchService.getProjectNetworkData(projectName, "api", getYesterdayTimeRange());
        ApiData failApiData = elaticSearchService.getFailProjectNetworkData(
                projectName, "api",
                getYesterdayTimeRange());
        miniProgramDetail.setApi(apiData);
        miniProgramDetail.setFailApi(failApiData);

        MetricData metricData = elaticSearchService.getProjectMetricData(
                projectName,
                getYesterdayTimeRange());
        miniProgramDetail.setMetric(metricData);
        FieldUpperDetail pageLoadData = elaticSearchService.getFieldDurationPercentileByType(
                projectName,
                "pageload",
                "data.navigationType",
                getYesterdayTimeRange());
        miniProgramDetail.setPageLoad(pageLoadData);
        FieldUpperDetail loadPackageData = elaticSearchService.getFieldPercentileByType(
                projectName,
                "loadpackage",
                "data.name",
                "data.size",
                getYesterdayTimeRange());

        miniProgramDetail.setLoadPackage(loadPackageData);
        FieldUpperDetail appLaunchData = elaticSearchService.getFieldDurationPercentileByType(
                projectName,
                "applaunch",
                "data.path",
                getYesterdayTimeRange());

        miniProgramDetail.setAppLaunch(appLaunchData);
        FieldUpperDetail evaluateScriptData = elaticSearchService.getFieldDurationPercentileByType(
                projectName,
                "evaluatescript",
                "data.file",
                getYesterdayTimeRange());
        miniProgramDetail.setEvaluateScript(evaluateScriptData);
        MiniProgramDetail.Error error = this.getProjectNotResolveIssue(projectName);
        miniProgramDetail.setError(error);

        return getLightAppProjectScoreByDetail(miniProgramDetail);
    }

    public void getWebProjectScore(Long projectId, Region region) {
        Optional<CanaryRecord> latestCanaryRecord = historyService.getHead(projectId, null);
        if (!latestCanaryRecord.isPresent()) {
            return;
        }

        Long taskId = latestCanaryRecord.get().getCanary().getDefaultTaskId();
        Optional<BuildTaskModel> task = buildTaskService.get(projectId, taskId);
        if (!task.isPresent()) {
            return;
        }

        // InputStreamEntity meta = taskResourceService.getTaskMeta(task,
        // AnalyseReport.fileName);
    }
}
