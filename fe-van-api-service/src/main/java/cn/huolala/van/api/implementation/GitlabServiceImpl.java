package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.facade.model.gitlab.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.roles.UserRoleModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.PagesIterable;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.service.GitlabService;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import cn.lalaframework.logging.LoggerFactory;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.huolala.van.api.util.EncodingUtils.buildQueryString;
import static java.util.concurrent.CompletableFuture.supplyAsync;
import static org.apache.http.client.utils.URLEncodedUtils.formatSegments;

@Service
public class GitlabServiceImpl implements GitlabService {

    private static final String PROJECTS = "projects";
    private static final String MEMBERS = "members";
    private static final String REPOSITORY = "repository";
    private static final String WEBHOOKS = "hooks";
    private static final String GROUPS = "groups";
    private static final String FILES = "files";
    private static final String MERGE_REQUESTS = "merge_requests";

    private static final String npmLockFile = "package-lock.json";
    private static final String yarnLockFile = "yarn.lock";
    private static final String rcFile = ".npmrc"; // yarn pnpm npm share same file

    private static final String authorEmail = "<EMAIL>";
    private static final String authorName = "van-bot";
    private static final Logger LOGGER = LoggerFactory.getLogger();

    private final Cache<String, byte[]> cache;

    @Value("${van.repository.gitlab}")
    private String httpUrl;
    @Value("${van.repository.token}")
    private String token;

    @Value("${van.repository.official_group}")
    private String gitlabAutoCreateGroup;

    @Value("${van.builder.test_branch_prefix:van/temporary}")
    private String temporaryBranchPrefix;

    @Value("${van.repository.gitlab_ssh}")
    private String sshUrl;
    @Value("${van.repository.ssh_password}")
    private String sshPassword;
    @Value("${van.repository.private_key}")
    private String sshPrivateKey;
    @Value("${van.nextRegistry:http://registry-npm-next.huolala.work}")
    private String nextRegistry;
    @Value("${van.npm.scope:hll}")
    private String npmScope;

    @Autowired
    private CloseableHttpClient httpClient;

    @Autowired
    private ObjectMapper mapper;

    public GitlabServiceImpl() {
        cache = CacheBuilder.newBuilder().maximumSize(500).build();
    }

    @Nullable
    private static GitlabMember.Role toGitLabRole(@Nullable Role role) {
        if (role == null) return null;
        switch (role) {
            case NoRole:
                return GitlabMember.Role.No;
            case TestRole:
                return GitlabMember.Role.Reporter;
            case DevRole:
                return GitlabMember.Role.Developer;
            case AdminRole:
                return GitlabMember.Role.Maintainer;
        }
        return null;
    }

    @NonNull
    private static List<BasicNameValuePair> buildPaginationParams(int page, int size) {
        ArrayList<BasicNameValuePair> list = new ArrayList<>(2);
        // https://docs.gitlab.com/ee/api/rest/index.html#offset-based-pagination
        list.add(new BasicNameValuePair("per_page", String.valueOf(size)));
        list.add(new BasicNameValuePair("page", String.valueOf(page)));
        return list;
    }

    private static Stream<String> getGroupNames(String repo) {
        int i = -1;
        Stream.Builder<String> sb = Stream.builder();
        while (true) {
            i = repo.indexOf("/", i + 1);
            if (i == -1) break;
            sb.accept(repo.substring(0, i));
        }
        return sb.build();
    }

    @Nullable
    private <T> T request(HttpUriRequest req, @NonNull Class<T> dataType) {
        try {
            req.setHeader("Private-Token", token);
            CloseableHttpResponse res = httpClient.execute(req);
            int status = res.getStatusLine().getStatusCode();
            // NOTE: gitlab some creating apis, return code 201
            if (status == 200 || status == 201) return mapper.readValue(res.getEntity().getContent(), dataType);
            if (status < 300) return null;
            if (status >= 400 && status < 500) {
                GitLabError ge = mapper.readValue(res.getEntity().getContent(), GitLabError.class);
                throw new InternalRequestException(status, req.getURI(), ge.getMessage());
            }
            throw new InternalRequestException(status, req.getURI());
        } catch (IOException exception) {
            throw new InternalRequestException(exception);
        }
    }

    @Nullable
    private <T> T post(@NonNull String path,
                       @Nullable Collection<? extends NameValuePair> params,
                       @NonNull Class<T> dataType) {
        HttpPost req = new HttpPost(httpUrl + path);
        if (params != null) req.setEntity(new UrlEncodedFormEntity(params));
        return request(req, dataType);
    }

    @Nullable
    private <T> T post(@NonNull String path,
                       @Nullable String params,
                       @NonNull Class<T> dataType) {
        HttpPost req = new HttpPost(httpUrl + path);
        req.setHeader("Content-Type", "application/json");
        if (params != null) req.setEntity(new StringEntity(params, ContentType.APPLICATION_JSON));
        return request(req, dataType);
    }

    @Nullable
    private <T> T put(@NonNull String path,
                      @Nullable List<? extends NameValuePair> params,
                      @NonNull Class<T> dataType) {
        return request(new HttpPut(httpUrl + path + buildQueryString(params)), dataType);
    }

    @Nullable
    @CanIgnoreReturnValue
    private <T> T delete(@NonNull String path,
                      @Nullable List<? extends NameValuePair> params,
                      @NonNull Class<T> dataType) {
        return request(new HttpDelete(httpUrl + path + buildQueryString(params)), dataType);
    }

    @NonNull
    private <T> Optional<T> get(@NonNull String path,
                                @Nullable List<? extends NameValuePair> params,
                                @NonNull Class<T> dataType) {
        return Optional.ofNullable(request(new HttpGet(httpUrl + path + buildQueryString(params)), dataType));
    }

    @NonNull
    private <T> T getNeverNull(@NonNull String path,
                               @Nullable List<? extends NameValuePair> params,
                               @NonNull Class<T> dataType) {
        return requestNeverNull(new HttpGet(httpUrl + path + buildQueryString(params)), dataType);
    }

    @NonNull
    private <T> T requestNeverNull(HttpUriRequest req, @NonNull Class<T> dataType) {
        T result = request(req, dataType);
        if (result == null) throw new InternalRequestException("Got unexpected null data from " + req.getURI());
        return result;
    }

    @NonNull
    private Optional<GitlabUser> findUserByUniqId(@NonNull String uniqId) {
        List<BasicNameValuePair> params = Arrays.asList(
            new BasicNameValuePair("search", uniqId),
            new BasicNameValuePair("active", "true"),
            new BasicNameValuePair("exclude_external", "true")
        );
        String path = formatSegments("api", "v4", "users");
        return get(path, params, GitlabUser[].class)
            .map(Arrays::stream).orElseGet(Stream::empty)
            .filter(i -> uniqId.equals(i.getUsername())).findFirst();
    }

    @Nullable
    private GitlabMember updateRepoMember(@NonNull String repo,
                                          @NonNull Long gitlabUserId,
                                          @NonNull GitlabMember.Role role) {
        List<BasicNameValuePair> params = Collections.singletonList(
            new BasicNameValuePair("access_level", String.valueOf(role.toValue()))
        );
        // https://docs.gitlab.com/ee/api/members.html#edit-a-member-of-a-group-or-project
        String path = formatSegments("api", "v4", PROJECTS, repo, MEMBERS, gitlabUserId.toString());
        return put(path, params, GitlabMember.class);
    }

    @Nullable
    private GitlabMember addRepoMember(@NonNull String repo, @NonNull String uniqId, @NonNull GitlabMember.Role role) {
        return findUserByUniqId(uniqId).map(u -> addRepoMember(repo, u.getId(), role)).orElse(null);
    }

    @Nullable
    private GitlabMember addRepoMember(@NonNull String repo,
                                       @NonNull Long gitlabUserId,
                                       @NonNull GitlabMember.Role role) {
        List<BasicNameValuePair> params = Arrays.asList(
            new BasicNameValuePair("user_id", gitlabUserId.toString()),
            new BasicNameValuePair("access_level", String.valueOf(role.toValue()))
        );
        // https://docs.gitlab.com/ee/api/members.html#add-a-member-to-a-group-or-project
        String path = formatSegments("api", "v4", PROJECTS, repo, MEMBERS);
        return post(path, params, GitlabMember.class);
    }

    @NonNull
    private Stream<GitlabMember> getGroupMembers(@NonNull String groupName) {
        return PagesIterable.createStream((page, size) -> getGroupMembers(groupName, page, size), 100);
    }

    @Nullable
    private GitlabMember[] getGroupMembers(@NonNull String groupName, int page, int size) {
        List<BasicNameValuePair> params = buildPaginationParams(page, size);
        // https://docs.gitlab.com/ee/api/members.html#list-all-members-of-a-group-or-project
        String path = formatSegments("api", "v4", "groups", groupName, MEMBERS);
        return get(path, params, GitlabMember[].class).orElse(null);
    }

    @NonNull
    private Stream<GitlabMember> getProjectMembers(@NonNull String repo) {
        return PagesIterable.createStream((page, size) -> getProjectMembers(repo, page, size), 100);
    }

    @NonNull
    private GitlabCommit getProjectCommit(@NonNull String repo, @NonNull String refName) {
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "commits", refName);
        try {
            return getNeverNull(path, null, GitlabCommit.class);
        } catch (InternalRequestException e) {
            if (e.getStatus() == 404) {
                String msg = String.format("The commit is not found by ref name '%s' in repo '%s'", refName, repo);
                throw new ResourceNotFoundException(msg, e);
            }
            throw e;
        }
    }

    @NonNull
    private Stream<GitlabCommit> getProjectCommits(@NonNull String repo, @Nullable String refName) {
        return PagesIterable.createStream((page, size) -> getProjectCommits(repo, refName, page, size), 100);
    }

    @Nullable
    private GitlabCommit[] getProjectCommits(@NonNull String repo, @Nullable String refName, int page, int size) {
        List<BasicNameValuePair> params = buildPaginationParams(page, size);
        if (refName != null) params.add(new BasicNameValuePair("ref_name", refName));
        // https://docs.gitlab.com/api/commits/#list-repository-commits
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "commits");
        return get(path, params, GitlabCommit[].class).orElse(null);
    }

    @Nullable
    private GitlabMember[] getProjectMembers(@NonNull String repo, int page, int size) {
        List<BasicNameValuePair> params = buildPaginationParams(page, size);
        // https://docs.gitlab.com/ee/api/members.html#list-all-members-of-a-group-or-project
        String path = formatSegments("api", "v4", PROJECTS, repo, MEMBERS);
        return get(path, params, GitlabMember[].class).orElse(null);
    }

    @Nullable
    @Override
    public GitlabBranch getBranchInfo(@NonNull String repo, @NonNull String branchName) {
        List<BasicNameValuePair> params = Collections.emptyList();
        // https://docs.gitlab.com/ee/api/branches.html#get-single-repository-branch
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "branches", branchName);
        return get(path, params, GitlabBranch.class).orElse(null);
    }

    @NonNull
    @Override
    public List<GitlabBranch> searchBranches(@NonNull String repo, @Nullable String keyword) {
        List<BasicNameValuePair> params = new ArrayList<>();
        if (keyword != null) params.add(new BasicNameValuePair("search", keyword));
        // https://docs.gitlab.com/ee/api/branches.html#list-repository-branches
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "branches");
        return get(path, params, GitlabBranch[].class).map(Arrays::asList).orElseGet(Collections::emptyList);
    }

    @NonNull
    @Override
    public Stream<GitlabMergeRequest> findMergeRequests(@NonNull String repo,
                                                        @Nullable GitlabMergeRequest.SearchParams searchParams) {
        final Map<?, ?> searchParamMap = searchParams == null
            ? Collections.emptyMap()
            : mapper.convertValue(searchParams, HashMap.class);

        final List<BasicNameValuePair> baseParams = searchParamMap.entrySet().stream().map(i -> {
            Object value = i.getValue();
            if (value == null) return null;
            return new BasicNameValuePair(i.getKey().toString(), value.toString());
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // https://docs.gitlab.com/ee/api/merge_requests.html#list-project-merge-requests
        final String path = formatSegments("api", "v4", PROJECTS, repo, "merge_requests");

        return PagesIterable.createStream((page, size) -> {
            List<BasicNameValuePair> params = buildPaginationParams(page, size);
            params.addAll(baseParams);
            return get(path, params, GitlabMergeRequest[].class).orElse(null);
        }, 100);
    }

    @Override
    @NonNull
    public List<GitlabMember> ********************(@NonNull String repo, @NonNull List<UserRoleModel> roles) {
        if (roles.isEmpty()) return Collections.emptyList();

        // Load project and groups user roles concurrently from GitLab.
        CompletableFuture<List<GitlabMember>> tProjectMembers =
            supplyAsync(() -> getProjectMembers(repo).collect(Collectors.toList()));
        Map<String, GitlabMember> groupMembers = getGroupNames(repo).parallel().flatMap(this::getGroupMembers)
            .collect(Collectors.toMap(
                GitlabMember::getUsername, i -> i,
                (a, b) -> a.hasSufficientPermission(b.getAccessLevel()) ? a : b)
            );
        Map<String, GitlabMember> projectMembers = tProjectMembers.join()
            .stream().collect(Collectors.toMap(GitlabMember::getUsername, i -> i));

        // Load project user roles from GitLab.
        return roles
            .stream()
            .map(i -> Pair.of(i.getUserUniqId(), toGitLabRole(i.getRole())))
            .filter(i -> i.getValue() != null)
            .collect(Collectors.toMap(Pair::getKey, Pair::getValue, GitlabMember.Role::compare))
            .entrySet().stream().<Supplier<GitlabMember>>map(e -> {
                String uniqId = e.getKey();
                GitlabMember.Role role = e.getValue();
                GitlabMember gMember = groupMembers.get(uniqId);

                // If the user has sufficient permissions in the group, no action is required.
                if (gMember != null && gMember.hasSufficientPermission(role)) return null;

                GitlabMember pMember = projectMembers.get(uniqId);

                // If the user is not a project member, add them later.
                if (pMember == null) return () -> addRepoMember(repo, uniqId, role);

                // If the user is a project member but lacks sufficient permissions, update them later.
                if (!pMember.hasSufficientPermission(role))
                    return () -> updateRepoMember(repo, pMember.getId(), role);

                // Otherwise, the user is a project member and has sufficient permissions, no action is required.
                return null;
            }).filter(Objects::nonNull)
            // Call gitlab API concurrently.
            .parallel().map(Supplier::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    @Nullable
    @CanIgnoreReturnValue
    public GitlabProject updateRepositoryDescription(@NonNull String repo, @NonNull String description) {
        // Do not edit project information if repo is not ours.
        if (!repo.startsWith("group-van/")) return null;

        List<BasicNameValuePair> params =
            Collections.singletonList(new BasicNameValuePair("description", description));
        // https://docs.gitlab.com/ee/api/projects.html#edit-project
        String path = formatSegments("api", "v4", PROJECTS, repo);
        return put(path, params, GitlabProject.class);
    }

    @Override
    @NonNull
    public GitlabProject getProjectInfo(@NonNull String repo) {
        List<BasicNameValuePair> params = Collections.emptyList();
        // https://docs.gitlab.com/ee/api/projects.html#edit-project
        String path = formatSegments("api", "v4", PROJECTS, repo);
        return getNeverNull(path, params, GitlabProject.class);
    }

    @Override
    @NonNull
    public GitlabCompare compare(@NonNull String repo, @NonNull String from, @NonNull String to) {
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("from", from));
        params.add(new BasicNameValuePair("to", to));
        // https://docs.gitlab.com/ee/api/repositories.html#compare-branches-tags-or-commits
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "compare");
        try {
            return getNeverNull(path, params, GitlabCompare.class);
        } catch (InternalRequestException e) {
            if (e.getStatus() == 404) {
                throw new ResourceNotFoundException(String.format(
                    "At least one of the ref names '%s' and '%s' is not found in repo '%s'",
                    from, to, repo), e);
            }
            throw e;
        }
    }


    // check if user has at least developer role
    @Override
    public boolean hasProjectDevPermission(@NonNull String repo, @Nullable String email) {
        List<GitlabMember> members = *********************(repo, email);
        if (members == null || members.isEmpty())
            return false;
        GitlabMember member = members.get(0);
        return member.getAccessLevel() == GitlabMember.Role.Developer ||
                member.getAccessLevel() == GitlabMember.Role.Maintainer ||
                member.getAccessLevel() == GitlabMember.Role.Owner;
    }

    @Override
    @NonNull
    public List<GitlabWebhook> getProjectWebhooks(@NonNull String repo) {
        // https://docs.gitlab.com/api/project_webhooks/
        String path = formatSegments("api", "v4", PROJECTS, repo, WEBHOOKS);
        return get(path, Collections.emptyList(), GitlabWebhook[].class).map(Arrays::asList)
                .orElse(Collections.emptyList());
    }

    @Override
    public void addProjectWebhook(@NonNull String repo, @NonNull String url) {
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("url", url));
        // https://docs.gitlab.com/api/project_webhooks/
        String path = formatSegments("api", "v4", PROJECTS, repo, WEBHOOKS);
        post(path, params, GitlabWebhook[].class);
    }

    @Override
    @Nullable
    public Stream<GitlabProject> getGroupProjects(int groupId) {
        // https://docs.gitlab.com/api/groups/#list-projects
        String path = formatSegments("api", "v4", GROUPS, String.valueOf(groupId), PROJECTS);
        return PagesIterable.createStream((page, size) -> {
            List<BasicNameValuePair> params = buildPaginationParams(page, size);
            return get(path, params, GitlabProject[].class).orElse(null);
        }, 100);
    }

    private byte[] getRepositoryRawFileWithoutCache(@NonNull String repo, @NonNull String filePath,
            @NonNull String ref) {
        // https://docs.gitlab.com/api/repository_files/#get-raw-file-from-repository
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("ref", ref));
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, FILES, filePath, "raw");
        String qs = buildQueryString(params);
        HttpGet req = new HttpGet(httpUrl + path + qs);
        req.setHeader("Private-Token", token);
        // try-with-resources close response
        try (CloseableHttpResponse res = httpClient.execute(req)) {
            int status = res.getStatusLine().getStatusCode();
            if (status >= 200 && status < 400) {
                HttpEntity entity = res.getEntity();
                if (entity == null) {
                    return null;
                }
                return EntityUtils.toByteArray(entity);
            }
            if (status == 404) {
                throw new ResourceNotFoundException(filePath + " not found in " + ref);
            }
            if (status >= 400 && status < 500) {
                GitLabError ge = mapper.readValue(res.getEntity().getContent(), GitLabError.class);
                throw new InternalRequestException(String.format(
                        "Got a %d status code from %s, and message=%s",
                        status,
                        req.getURI().toString(),
                        ge.getMessage()));
            }
            throw new InternalRequestException("Got a " + status + " status code from " + req.getURI().toString());
        } catch (IOException exception) {
            throw new InternalRequestException(exception);
        }
    }

    @Override
    @Nullable
    public byte[] getRepositoryRawFile(@NonNull String repo, @NonNull String filePath, @NonNull String ref) {
        return getRepositoryRawFileWithoutCache(repo, filePath, ref);
    }

    @Override
    @Nullable
    public byte[] getRepositoryLogoIcon(@NonNull String repo, @NonNull String filePath, @NonNull String ref) {
        String cacheKey = String.format("%s:%s:logo", "boilerplate", repo);
        try {
            // set cache automatically, cool
            return cache.get(cacheKey, () -> getRepositoryRawFileWithoutCache(repo, filePath, ref));
        } catch (Exception e) {
            throw new InternalRequestException(e);
        }
    }

    @Override
    @NonNull
    public boolean repositoryAlreadyExists(@NonNull String repo) {
        try {
            getProjectInfo(repo);
            return true;
        } catch (InternalRequestException e) {
            return false;
        }
    }

    @Override
    @Nullable
    public void createCommitDiscussion(@NonNull ProjectModel project, @NonNull BuildTaskModel task,
            @NonNull String comment) {
        // https://doc.gitlab.com/ee/api/discussions.html#create-new-commit-thread
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("body", comment));
        String path = formatSegments("api", "v4", PROJECTS, project.getRepository(), "repository", "commits",
                task.getHash(), "discussions");
        post(path, params, GitlabDiscussion.class);
    }

    @Override
    public void rebaseMergeRequest(@NonNull String repo, @NonNull long iid) {
        // https://docs.gitlab.com/api/merge_requests/#rebase-a-merge-request
        String path = formatSegments("api", "v4", PROJECTS, repo, MERGE_REQUESTS, String.valueOf(iid), "rebase");
        put(path, Collections.emptyList(), GitlabMergeRequest.class);
    }

    @Override
    @Nullable
    public GitlabMergeRequest closeMergeRequest(@NonNull String repo, @NonNull long iid) {
        // https://docs.gitlab.com/api/merge_requests/#update-mr
        String path = formatSegments("api", "v4", PROJECTS, repo, MERGE_REQUESTS, String.valueOf(iid));
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("state_event", "close"));
        return put(path, params, GitlabMergeRequest.class);
    }

    @Override
    @Nullable
    public GitlabBranch createBranch(@NonNull String repo, @NonNull String branch, @NonNull String ref) {
        // https://docs.gitlab.com/api/branches/#create-repository-branch
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "branches");
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("branch", branch));
        params.add(new BasicNameValuePair("ref", ref));
        return post(path, params, GitlabBranch.class);
    }

    @Override
    @Nullable
    public GitlabMergeRequest createMergeRequest(@NonNull String repo, @NonNull String title, @NonNull String source,
            @NonNull String target, @NonNull boolean removeSource) {
        // https://docs.gitlab.com/api/merge_requests/#create-mr
        String path = formatSegments("api", "v4", PROJECTS, repo, MERGE_REQUESTS);
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("title", title));
        params.add(new BasicNameValuePair("source_branch", source));
        params.add(new BasicNameValuePair("target_branch", target));
        params.add(new BasicNameValuePair("remove_source_branch", String.valueOf(removeSource)));
        return post(path, params, GitlabMergeRequest.class);
    }

    @Override
    @Nullable
    public GitlabMergeRequest directMergeRequest(@NonNull String repo, @NonNull String branch, @NonNull String ref,
            @Nullable String description) {
        GitlabBranch gitlabBranch = createBranch(repo, branch, ref);
        if (gitlabBranch == null)
            return null;
        String title = gitlabBranch.getName();
        if (description != null && !description.isEmpty()) {
            title = description;
        }
        return createMergeRequest(repo, title, gitlabBranch.getName(), ref, true);
    }

    @Override
    @NonNull
    public GitlabMergeRequest getMergeRequest(@NonNull String repo, @NonNull long iid) {
        // https://docs.gitlab.com/api/merge_requests/#get-single-mr
        String path = formatSegments("api", "v4", PROJECTS, repo, MERGE_REQUESTS, String.valueOf(iid));
        return getNeverNull(path, null, GitlabMergeRequest.class);
    }

    @Override
    public List<GitlabProtectedBranch> listProtectedBranches(@NonNull String repo, @Nullable String search) {
        // https://docs.gitlab.com/ee/api/protected_branches.html#list-protected-branches
        String path = formatSegments("api", "v4", PROJECTS, repo, "protected_branches");
        List<BasicNameValuePair> params = new ArrayList<>();
        if (search != null && !search.isEmpty()) {
            params.add(new BasicNameValuePair("search", search));
        }
        return get(path, params, GitlabProtectedBranch[].class).map(Arrays::asList).orElse(Collections.emptyList());
    }

    @Override
    public List<GitlabMember> *********************(@NonNull String repo, @Nullable String query) {
        // https://docs.gitlab.com/ee/api/members.html#list-all-members-of-a-group-or-project
        List<BasicNameValuePair> params = new ArrayList<>();
        if (query != null && !query.isEmpty()) {
            params.add(new BasicNameValuePair("query", query));
        }
        String path = formatSegments("api", "v4", PROJECTS, repo, MEMBERS, "all");
        return get(path, params, GitlabMember[].class).map(Arrays::asList).orElse(Collections.emptyList());
    }

    @Override
    public boolean checkUserMergePermission(@NonNull String repo, @NonNull long iid,
            @NonNull String uniqId) {
        GitlabMergeRequest mr = getMergeRequest(repo, iid);
        List<GitlabProtectedBranch> protectedBranchs = listProtectedBranches(repo, mr.getTargetBranch());
        if (protectedBranchs.isEmpty()) {
            return true;
        }
        GitlabProtectedBranch pb = protectedBranchs.get(0);
        List<GitlabMember> members = *********************(repo, uniqId);
        if (members.isEmpty()) {
            return false;
        }
        GitlabMember member = members.get(0);
        // at least >= level
        return pb.getMergeAccessLevels().stream()
                .anyMatch(al -> member.getAccessLevel().toValue() >= al.getAccessLevel().toValue());
    }

    @Override
    public GitlabMergeRequest mergeMergeRequest(@NonNull String repo, @NonNull long iid,
            @Nullable String mergeCommitMessage, @Nullable boolean shouldRemoveSourceBranch) {
        // https://docs.gitlab.com/api/merge_requests/#merge-a-merge-request
        String path = formatSegments("api", "v4", PROJECTS, repo, MERGE_REQUESTS, String.valueOf(iid), "merge");
        List<BasicNameValuePair> params = new ArrayList<>();
        if (mergeCommitMessage != null && !mergeCommitMessage.isEmpty()) {
            params.add(new BasicNameValuePair("merge_commit_message", mergeCommitMessage));
        }
        if (shouldRemoveSourceBranch) {
            params.add(new BasicNameValuePair("should_remove_source_branch",
                    String.valueOf(true)));
        }
        return put(path, params, GitlabMergeRequest.class);
    }

    @Override
    public void deleteBranch(@NonNull String repo, @NonNull String branch) {
        // https://docs.gitlab.com/ee/api/branches.html#delete-repository-branch
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "branches", branch);
        delete(path, null, GitlabBranch.class);
    }

    private static long extractId(GitlabBranch branch) {
        String[] ids = branch.getName().split("-");
        try {
            return Long.parseLong(ids[ids.length - 1]);
        } catch (NumberFormatException e) {
            LOGGER.info("extract task id failed. branch name is {}", branch.getName());
            return 0;
        }
    }

    public static void sortBranches(List<GitlabBranch> branches) {
        branches.sort((b1, b2) -> {
            long id1 = extractId(b1);
            long id2 = extractId(b2);
            return Long.compare(id2, id1); // Descending order
        });
    }

    @Override
    public void pruneTemporaryBranches(@NonNull String repo) {
        List<GitlabBranch> branches = searchBranches(repo, temporaryBranchPrefix);
        if (branches.size() > 5) {
            sortBranches(branches);
            // keep only first 5 branches
            branches.stream().skip(5).forEach(b -> {
                deleteBranch(repo, b.getName());
            });
        }
    }

    @Override
    public GitlabCommit createCommit(@NonNull String repo, @NonNull String branch, @NonNull String commitMessage,
            @Nullable String startBranch, @Nullable String authorEmail, @Nullable String authorName,
            @Nullable List<GitlabCommitAction> actions) {
        // https://docs.gitlab.com/api/commits/#create-a-commit-with-multiple-files-and-actions
        String path = formatSegments("api", "v4", PROJECTS, repo, REPOSITORY, "commits");
        CreateCommitParams params = new CreateCommitParams();
        params.setBranch(branch);
        params.setCommitMessage(commitMessage);
        if (startBranch != null && !startBranch.isEmpty()) {
            params.setStartBranch(startBranch);
        }
        if (authorEmail != null && !authorEmail.isEmpty()) {
            params.setAuthorEmail(authorEmail);
        }
        if (authorName != null && !authorName.isEmpty()) {
            params.setAuthorName(authorName);
        }
        if (actions != null && !actions.isEmpty()) {
            params.setActions(actions);
        }
        try {
            String serdeParams = mapper.writeValueAsString(params);
            LOGGER.info("serdeParams {}", serdeParams);
            return post(path, serdeParams, GitlabCommit.class);
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
    }


    private String modifyNpmLockFile(byte[] content) {
        String pkgLockContent = new String(content);
        // 匹配 resolved 字段，捕获路径部分
        Pattern pattern = Pattern.compile("\"resolved\":\\s*\"https?://[^/]+(/.*?)\"");
        Matcher matcher = pattern.matcher(pkgLockContent);

        // 替换域名为
        StringBuffer updatedContent = new StringBuffer();
        while (matcher.find()) {
            String pathPart = matcher.group(1);
            String newUrl = "\"resolved\": \"" + nextRegistry + pathPart + "\"";
            matcher.appendReplacement(updatedContent, Matcher.quoteReplacement(newUrl));
        }
        matcher.appendTail(updatedContent);
        return updatedContent.toString();
    }

    private String modifyYarnLockFile(byte[] content) {
        String yarnLockContent = new String(content);
        // 正则匹配 resolved 字段的 URL
        Pattern pattern = Pattern.compile("resolved \"https?://[^/]+(/.*?)\"");
        Matcher matcher = pattern.matcher(yarnLockContent);
        // 替换域名为
        StringBuffer updatedContent = new StringBuffer();
        while (matcher.find()) {
            String newUrl = "resolved \""+ nextRegistry + matcher.group(1) + "\"";
            matcher.appendReplacement(updatedContent, Matcher.quoteReplacement(newUrl));
        }
        matcher.appendTail(updatedContent);
        return updatedContent.toString();
    }

    private byte[] getLockfileContent(@NonNull ProjectModel project, @NonNull String lockname, @NonNull String branch) {
        byte[] content = null;
        try {
            content = getRepositoryRawFile(project.getRepository(), lockname, branch);
        } catch (ResourceNotFoundException e) {
            LOGGER.error("{} not found in branch {} of project {}", lockname, branch, project.getRepository());
        }
        return content;
    }

    @Override
    public GitlabMergeRequest modifyLockFile(@NonNull ProjectModel project, @Nullable String branch) {
        if (branch == null || branch.isEmpty()) {
            GitlabProject gitlabProject = getProjectInfo(project.getRepository());
            branch = gitlabProject.getDefaultBranch();
        }
        String lockName = npmLockFile;
        byte[] content = getLockfileContent(project, lockName, branch);
        String modifiedContent;
        if (content == null || content.length == 0) {
            lockName = yarnLockFile;
            content = getLockfileContent(project, lockName, branch);
        }
        if (content == null || content.length == 0) {
            throw new InternalRequestException(
                    String.format("can't get %s or %s in branch %s", npmLockFile, yarnLockFile, branch));
        }
        // NOTE: use regex to modify file content
        // should test it seriously
        if (lockName.equals(npmLockFile)) {
            modifiedContent = modifyNpmLockFile(content);
        } else {
            modifiedContent = modifyYarnLockFile(content);
        }
        if (modifiedContent.isEmpty()) {
            throw new InternalRequestException("modify " + lockName + " file failed");
        }
        List<GitlabCommitAction> actions = new ArrayList<>();
        String sourceBranch = String.format("van/lockfile-%s",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy_MM_dd'T'HH_mm_ss")));
        String message = String.format("modify dependencies resolved url in %s", lockName);
        // lockfile action
        GitlabCommitAction action = new GitlabCommitAction(GitlabCommitAction.Action.Update, lockName);
        action.setContent(modifiedContent);
        actions.add(action);

        // .npmrc action
        byte[] npmrcContent = null;
        String modifiedNpmrcContent = "";
        GitlabCommitAction action1 = new GitlabCommitAction(GitlabCommitAction.Action.Update, rcFile);
        try {
            npmrcContent = getRepositoryRawFile(project.getRepository(), rcFile, branch);
        } catch (ResourceNotFoundException e) {
            LOGGER.error(".npmrc not found in branch {} of project {}", branch, project.getRepository());
            action1.setAction(GitlabCommitAction.Action.Create);
        }
        String host = "huolala.work";
        if (!npmScope.equals("hll")) {
            host = "xlcx.work";
        }
        // maybe duplicate registry field, but we ignore this time
        String registryLink = String.format("registry=http://registry-npm-next.%s\n", host);
        if (npmrcContent == null) {
            modifiedContent = registryLink;
        } else {
            modifiedContent = Arrays.toString(npmrcContent) + "\n" + registryLink;
        }
        action1.setContent(modifiedContent);
        actions.add(action1);
        // create a commit
        createCommit(project.getRepository(), sourceBranch, message, branch, authorEmail, authorName, actions);
        // create a merge request
        return createMergeRequest(project.getRepository(), message, sourceBranch, branch, true);
    }

    @Getter
    public static class GitLabError {
        private String message;

        public void setMessage(Object message) {
            if (message instanceof String) {
                this.message = (String) message;
            } else {
                ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
                try {
                    this.message = mapper.writeValueAsString(message);
                } catch (JsonProcessingException e) {
                    throw new InternalMappingException(e);
                }
            }
        }
    }


    @Override
    public ResponseEntity<?> simpleProxy(@NonNull String repo, @NonNull BuildTaskModel task,
            Map<String, String> query) {
        if (task.getStatus() != BuildTaskStatus.Running) {
            throw new InternalRequestException("invalid task status");
        }

        String path = query.get("path");
        if (path == null) {
            path = "";
        }
        List<BasicNameValuePair> params = new ArrayList<>();
        query.forEach((k, v) -> {
            if (!k.equals("path")) {
                params.add(new BasicNameValuePair(k, v));
            }
        });
        String gitlabPath = formatSegments("api", "v4", PROJECTS, repo, path);
        String qs = buildQueryString(params);
        String url = httpUrl + gitlabPath + qs;
        HttpGet req = new HttpGet(url);
        req.setHeader("Private-Token", token);
        // try-with-resources close response
        try (CloseableHttpResponse res = httpClient.execute(req)) {
            int status = res.getStatusLine().getStatusCode();
            HttpEntity entity = res.getEntity();
            if (entity == null) {
                return ResponseEntity.status(status).contentType(MediaType.TEXT_PLAIN).body(null);
            } else {
                return ResponseEntity.status(status).contentType(
                        MediaType.valueOf(entity.getContentType().getValue())).body(entity.getContent());
            }
        } catch (IOException exception) {
            throw new InternalRequestException(exception);
        }
    }
}
