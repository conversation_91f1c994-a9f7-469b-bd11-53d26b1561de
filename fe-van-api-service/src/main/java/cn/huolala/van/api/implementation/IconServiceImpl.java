package cn.huolala.van.api.implementation;

import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.model.IconResultModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.IconService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.service.TaskResourceService;
import cn.lalaframework.logging.LoggerFactory;
import cn.lalaframework.soa.exception.BusinessException;
import cn.lalaframework.utils.IOUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.Getter;
import org.apache.commons.lang.ArrayUtils;
import org.apache.http.entity.ContentType;
import org.apache.logging.log4j.util.Strings;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
@EnableAsync
public class IconServiceImpl implements IconService {
    private static final Logger LOGGER = LoggerFactory.getLogger();

    private static String defaultIconTemplate;
    private final Cache<String, IconResultModel> cache;
    @Autowired
    private TaskResourceService taskResourceService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private ProjectService projectService;

    public IconServiceImpl() {
        cache = CacheBuilder.newBuilder().maximumSize(500).build();
    }

    @NonNull
    private static String getDefaultIconTemplate() {
        if (defaultIconTemplate != null) return defaultIconTemplate;
        synchronized (IconServiceImpl.class) {
            if (defaultIconTemplate == null) {
                InputStream is = IconServiceImpl.class.getResourceAsStream("/defaultIconTemplate.svg");
                if (is == null) {
                    @SuppressWarnings("java:S112") RuntimeException e = new RuntimeException(
                        "Cannot load default icon template.");
                    throw e;
                }
                defaultIconTemplate = IOUtils.toString(is);
            }
        }
        return defaultIconTemplate;
    }

    @Override
    @NonNull
    public List<IconResultModel> getCachedItems() {
        return new ArrayList<>(cache.asMap().values());
    }

    @Override
    @NonNull
    public List<String> cleanCache() {
        List<String> keys = cache.asMap().keySet().stream().sorted().collect(Collectors.toList());
        cache.invalidateAll();
        return keys;
    }

    @NonNull
    private IconResultModel getIconWithoutCache(long projectId, @Nullable BuildTaskModel task) {
        if (task != null) {
            String href = getIconPathFromHtml(task)
                // If no favicon is specified in the HTML, try using /favicon.ico.
                .orElse("favicon.ico");

            // For absolute URLs, it's better to use a redirect than to access them directly from the server.
            if (href.startsWith("http:") || href.startsWith("https:") || href.startsWith("//")) {
                return IconResultModel.builder().projectId(projectId).taskId(task.getId()).location(href).status(302)
                    .build();
            }

            // Attempting to load the icon resource from OSS and send back a standard image response.
            HttpEntity<InputStream> entity = taskResourceService.getTaskResource(task, preProcessUrl(href));
            if (entity != null) {
                try {
                    InputStream body = entity.getBody();
                    MediaType type = entity.getHeaders().getContentType();
                    if (body != null && type != null) {
                        return IconResultModel.builder().projectId(projectId).taskId(task.getId()).status(200)
                            .type(type.toString()).body(StreamUtils.copyToByteArray(body)).build();
                    }
                } catch (IOException e) {
                    throw new InternalRequestException(e);
                }
            }
        }

        // Otherwise, we cannot load any icon from the task, attempt to generate svg icon by project name.
        return generateProjectSvgIcon(projectId);
    }

    @NonNull
    private IconResultModel generateProjectSvgIcon(long projectId) {
        String kw = projectService.getNameById(projectId).replaceAll("[^a-zA-Z0-9]", "").substring(0, 2)
            .replaceAll("^$", "-");
        byte[] content = String.format(getDefaultIconTemplate(), kw).getBytes(StandardCharsets.UTF_8);
        return IconResultModel.builder().projectId(projectId).status(200).type(ContentType.IMAGE_SVG.getMimeType())
            .body(content).build();
    }

    @NonNull
    private String preProcessUrl(@NonNull String href) {
        href = href.replace("__VAN_STATIC_BASE_PATH__", "");
        if (href.startsWith("./")) href = href.substring(2);
        if (href.startsWith("/")) href = href.substring(1);
        return href;
    }

    @NonNull
    private Optional<String> getIconPathFromHtml(@NonNull BuildTaskModel task) throws BusinessException {
        return Optional.ofNullable(getDocument(task))
            // Get link elements from document.
            .map(doc -> doc.getElementsByTag("link"))
            // Find all <link rel="icon" /> or <link rel="van-icon" /> elements, sort by their priority, and get the highest.
            .map(PriorityElement::fromElements).orElseGet(Stream::empty).filter(Objects::nonNull)
            // Get minimal item.
            .min((a, b) -> b.getPriority() - a.getPriority()).map(PriorityElement::getValue);
    }

    @Nullable
    private Document getDocument(@NonNull BuildTaskModel task) {
        try {
            HttpEntity<InputStream> entity = null;
            for (String n : Arrays.asList("200.html", "index.html")) {
                entity = taskResourceService.getTaskResource(task, n);
                if (entity != null) break;
            }
            if (entity != null) {
                InputStream body = entity.getBody();
                if (body != null) {
                    return Jsoup.parse(body, "utf-8", "");
                }
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
        return null;
    }

    @NonNull
    @Override
    public IconResultModel getIcon(long projectId, @Nullable Long taskId) throws ExecutionException {
        if (taskId == null) taskId = buildTaskService.getLatestSuccessTaskIdsByProjectId(projectId);
        BuildTaskModel task = Optional.ofNullable(taskId).flatMap(i -> buildTaskService.get(projectId, i)).orElse(null);
        return cache.get(String.format("%d:%d", projectId, taskId), () -> getIconWithoutCache(projectId, task));
    }

    @Getter
    private static class PriorityElement {
        private final int priority;

        @NonNull
        private final String value;

        public PriorityElement(int priority, @NonNull String value) {
            this.priority = priority;
            this.value = value;
        }

        @Nullable
        public static PriorityElement fromElement(@NonNull Element link, int index) {
            if (!link.hasAttr("rel")) return null;
            String[] rel = link.attr("rel").split("\\s+");
            String value = link.attr("href");
            if (Strings.isBlank(value)) return null;
            if (ArrayUtils.contains(rel, "van-icon")) {
                return new PriorityElement(1000_000 + index, value);
            } else if (ArrayUtils.contains(rel, "icon")) {
                return new PriorityElement(1000 + index, value);
            }
            return null;
        }

        @Nullable
        public static Stream<PriorityElement> fromElements(Elements elements) {
            return IntStream.range(0, elements.size())
                .mapToObj(index -> PriorityElement.fromElement(elements.get(index), index));
        }
    }
}
