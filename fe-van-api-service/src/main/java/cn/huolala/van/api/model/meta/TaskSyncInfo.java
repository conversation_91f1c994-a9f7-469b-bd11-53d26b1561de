package cn.huolala.van.api.model.meta;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class TaskSyncInfo {
    @Nullable
    @ApiModelProperty(hidden = true)
    private Long userId;
    private Status status;
    private int total;
    private int current;

    @ApiModelProperty(hidden = true)
    public void setUserID(Long userId) {
        this.userId = userId;
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public
    enum Status {
        PENDING,
        COMPLETED,
        FAILED
    }
}
