package cn.huolala.van.api.model.lone;

import cn.huolala.van.api.model.Region;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum LoneRegion {
    Unknown(null),
    China(Region.cn),
    Singapore(Region.sin),
    India(Region.bom),
    LatinAmerica(Region.sao),
    LostRegion(null), // this is a placeholder for regions to skip 5 for Europe
    Europe(Region.de);

    @Nullable
    private final Region region;

    LoneRegion(@Nullable Region region) {
        this.region = region;
    }

    public static Stream<LoneRegion> supportedRegions() {
        Set<Region> s = Region.supportedRegions().collect(Collectors.toSet());
        return Arrays.stream(values()).filter(i -> s.contains(i.region));
    }

    public static LoneRegion fromRegion(@Nullable Region region) {
        for (LoneRegion lr : LoneRegion.values()) {
            if (lr.region.equals(region)) {
                return lr;
            }
        }
        return LoneRegion.Unknown;
    }
}
