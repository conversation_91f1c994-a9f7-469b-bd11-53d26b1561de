package cn.huolala.van.api.model.npm;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NpmPackageRemoteInfo {

    @JsonProperty("_id")
    private String id;

    private String name;

    private String description;

    @JsonProperty("dist-tags")
    private Map<String, String> distTags;

    private Map<String, NpmVersion> versions;

    private String readme;

    private List<NpmMaintainer> maintainers;

    @JsonProperty("_attachments")
    private Map<String, NpmAttachment> attachments;

    // not used for now
    // private Map<String, LocalDateTime> time;

}
