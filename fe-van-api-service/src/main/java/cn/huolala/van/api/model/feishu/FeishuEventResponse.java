package cn.huolala.van.api.model.feishu;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FeishuEventResponse {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Toast toast;

    public static FeishuEventResponse createErrorResponse(@NonNull String content) {
        return new FeishuEventResponse(new Toast(Type.error, content));
    }

    public static FeishuEventResponse createSuccessResponse(@NonNull String content) {
        return new FeishuEventResponse(new Toast(Type.success, content));
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum Type {info, success, error, warning}

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Toast {
        private Type type;
        private String content;
    }
}
