package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;

import javax.persistence.Entity;
import javax.persistence.Id;

@Getter
@Setter
@Entity
@NoArgsConstructor
public class ImportantProjectView {
    @Id
    @NonNull
    private String name = "";

    @JsonIgnore
    private long pv;

    private boolean blueChip;
}
