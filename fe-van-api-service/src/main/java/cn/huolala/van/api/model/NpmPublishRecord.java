package cn.huolala.van.api.model;

import cn.huolala.van.api.dao.entity.ComponentDeployHistoryEntity;
import cn.huolala.van.api.dao.enums.ComponentPublishStatus;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

@Getter
public class NpmPublishRecord extends UserBase {
    @NonNull
    private final Long projectId;
    @NonNull
    private final Long taskId;
    @NonNull
    private final String version;

    @NonNull
    private final ComponentPublishStatus status;

    @Nullable
    private final String message;

    @NonNull
    private final OffsetDateTime createdAt;

    public NpmPublishRecord(@NonNull ComponentDeployHistoryEntity entity, @NonNull UserModel user) {
        super(user.getUniqId(), user.getName());
        projectId = entity.getProjectId();
        taskId = entity.getTaskId();
        version = StringUtils.defaultString(entity.getVersion());
        status = entity.getStatus();
        message = entity.getMessage();
        createdAt = entity.getCreatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }

    @Nullable
    public static NpmPublishRecord create(@Nullable ComponentDeployHistoryEntity entity, @Nullable UserModel user) {
        if (user == null || entity == null) return null;
        return new NpmPublishRecord(entity, user);
    }
}
