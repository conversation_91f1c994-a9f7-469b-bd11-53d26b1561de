package cn.huolala.van.api.model.deploy;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collections;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class CanaryRule {
    @Nullable
    private String id;
    @NonNull
    private Mode mode;
    @NonNull
    private List<? extends CanaryPredicate> predicates;
    @Nullable
    @ApiModelProperty("A taskId whose associated version will be returned when the rule is met.\n" +
            "If it is a null or zero, a 404 page will be returned when the rule is met.")
    private Long version;
    @Nullable
    private String description;

    public CanaryRule() {
        mode = Mode.every;
        predicates = Collections.emptyList();
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum Mode {
        every, some
    }
}
