package cn.huolala.van.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalException extends RuntimeException {
    public InternalException(Exception exception) {
        super(exception);
    }

    public InternalException(String message) {
        super(message);
    }

    public InternalException(String message, Exception reason) {
        super(message, reason);
    }
}
