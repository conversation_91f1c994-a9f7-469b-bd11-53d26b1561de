package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.entity.UserMedalEntity;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

@Getter
@AllArgsConstructor
public class UserMedalModel {
    private final Long id;
    private final Long userId;

    private final MedalEnum uniqName;
    private final String remark;

    private final OffsetDateTime createdAt;
    private final OffsetDateTime updatedAt;

    @Nullable
    public static UserMedalModel create(@Nullable UserMedalEntity entity) {
        if (entity == null) return null;
        ZoneId zone = ZoneId.systemDefault();
        return new UserMedalModel(
                entity.getId(),
                entity.getUserId(),
                entity.getUniqName(),
                entity.getRemark(),
                entity.getCreatedAt().atZone(zone).toOffsetDateTime(),
                entity.getUpdatedAt().atZone(zone).toOffsetDateTime()
        );
    }
}
