package cn.huolala.van.api.model.events;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.van.api.model.PdmInfo;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.config.VanConfig;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.meta.TaskSyncInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;

public interface ProjectEventContent {
    default ProjectEventType fetchType() {
        return Arrays.stream(ProjectEventType.values())
                .filter(i -> this.getClass().equals(i.getContentType()))
                .findFirst().orElse(ProjectEventType.UnknownEvent);
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class LaunchTest implements ProjectEventContent {
        @Nullable
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long launchId;
        @Nullable
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long taskId;

        @Nullable
        @Deprecated
        @JsonProperty("launch_id")
        public Long getLongLaunchId() {
            return this.launchId;
        }

        @Deprecated
        @JsonProperty("launch_id")
        public void setLongLaunchId(@Nullable Long id) {
            this.launchId = id;
        }

        @Deprecated
        @Nullable
        @JsonProperty("task_id")
        public Long getLongTaskId() {
            return this.taskId;
        }

        @Deprecated
        @JsonProperty("task_id")
        public void getLongTaskId(@Nullable Long id) {
            this.taskId = id;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class LaunchProd implements ProjectEventContent {
        @Nullable
        private Long launchId;
        @Nullable
        private Region region;
        @Nullable
        private LegacyDeployConfig canary;
        @Nullable
        private String pdmId;
        @Nullable
        private String message;

        public LaunchProd(@NonNull CanaryRecord after, @Nullable PdmInfo pdmInfo, @Nullable String message) {
            this.launchId = after.getId();
            this.region = after.getRegion();
            this.canary = after.getCanary();
            this.pdmId = pdmInfo == null ? null : pdmInfo.getId();
            this.message = message;
        }

        @Nullable
        @Deprecated
        @JsonProperty("launch_id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        public Long getLongLaunchId() {
            return this.launchId;
        }

        @Deprecated
        @JsonProperty("launch_id")
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        public void setLongLaunchId(@Nullable Long id) {
            this.launchId = id;
        }

        @Nullable
        @Deprecated
        @JsonProperty("pdm_id")
        public String getStringPdmId() {
            return this.pdmId;
        }

        @Deprecated
        @JsonProperty("pdm_id")
        public void setStringPdmId(@Nullable String pdmId) {
            this.pdmId = pdmId;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class EditProjectDetail implements ProjectEventContent {
        @Nullable
        private Object oldDetail;
        @Nullable
        private Object newDetail;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class EditProjectConfig implements ProjectEventContent {
        @Nullable
        private VanConfig config;
        @Nullable
        private VanConfig oldConfig;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class TaskEvent implements ProjectEventContent {
        @Nullable
        private Long taskId;
        @Nullable
        private BuildTaskStatus status;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class TaskSyncEvent implements ProjectEventContent {
        @Nullable
        private Long taskId;
        @Nullable
        private String region;
        @Nullable
        private TaskSyncInfo info;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class Unknown implements ProjectEventContent {
        @Nullable
        private String message;
    }
}
