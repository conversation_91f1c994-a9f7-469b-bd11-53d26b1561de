package cn.huolala.van.api.implementation;

import cn.huolala.van.api.model.PhpResponse;
import cn.huolala.van.api.model.SsoUserForSearch;
import cn.huolala.van.api.service.SsoService;
import cn.huolala.van.api.util.PhpUtils;
import cn.lalaframework.soa.exception.BusinessException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Service
public class SsoServiceImpl implements SsoService {
    @Value("${hll.sso.client.app-id}")
    private String appId;
    @Value("${hll.sso.client.app-secret}")
    private String secret;

    @Value("${sso.ucenter.url}")
    private String url;

    @Autowired
    private CloseableHttpClient httpClient;

    @Autowired
    private ObjectMapper mapper;

    /**
     * @deprecated Use FeishuService.getUserInfo to instead
     */
    @Deprecated
    @Override
    @Nullable
    public SsoUserForSearch getUserByAccount(@Nullable String account) {
        if (account == null) return null;
        return Arrays.stream(searchUser(account))
                .filter(i -> account.equals(i.getAccount())).findFirst().orElse(null);
    }

    @Override
    public SsoUserForSearch[] searchUser(String keyword) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("_c", "open_api");
            params.put("_m", "user");
            params.put("_a", "search");
            params.put("_t", String.valueOf(System.currentTimeMillis() / 1000));
            params.put("key", keyword);
            params.put("appid", appId);
            params.put("version", "1.0");

            URIBuilder ub = new URIBuilder(url).setPath("/index.php");
            params.forEach(ub::addParameter);
            ub.addParameter("_sign", PhpUtils.sign(secret, params));
            HttpGet req = new HttpGet(ub.build());
            CloseableHttpResponse res = httpClient.execute(req);

            int code = res.getStatusLine().getStatusCode();
            if (code != 200) throw new BusinessException("Failed to request UCenter, got an HTTP code " + code);

            PhpResponse<?> pr = mapper.readValue(res.getEntity().getContent(), PhpResponse.class);

            if (pr.getRet() != 0)
                throw new BusinessException("UCenter: ret=" + pr.getRet() + ", message=" + pr.getMsg());

            return mapper.convertValue(pr.getData(), SsoUserForSearch[].class);
        } catch (IOException | URISyntaxException e) {
            throw new BusinessException(e);
        }
    }
}
