package cn.huolala.van.api.service;

import cn.huolala.van.api.facade.model.gitlab.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.roles.UserRoleModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;

import com.google.errorprone.annotations.CanIgnoreReturnValue;

import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

public interface GitlabService {

    @Nullable
    GitlabBranch getBranchInfo(@NonNull String repo, @NonNull String branchName);

    @NonNull
    List<GitlabBranch> searchBranches(@NonNull String repo, @Nullable String keyword);

    @NonNull
    Stream<GitlabMergeRequest> findMergeRequests(@NonNull String repo,
                                                 @Nullable GitlabMergeRequest.SearchParams searchParams);

    @NonNull
    List<GitlabMember> ********************(@NonNull String repo, @NonNull List<UserRoleModel> roles);

    @Nullable
    @CanIgnoreReturnValue
    GitlabProject updateRepositoryDescription(@NonNull String repo, @NonNull String description);

    @NonNull
    GitlabProject getProjectInfo(@NonNull String repo);

    @NonNull
    GitlabCompare compare(@NonNull String repo, @NonNull String sourceBranch, @NonNull String targetBranch);

    @NonNull
    boolean hasProjectDevPermission(@NonNull String repo, @Nullable String email);

    @NonNull
    List<GitlabWebhook> getProjectWebhooks(@NonNull String repo);

    @Nullable
    void addProjectWebhook(@NonNull String repo, @NonNull String url);

    @Nullable
    Stream<GitlabProject> getGroupProjects(@NonNull int groupId);

    @Nullable
    byte[] getRepositoryRawFile(@NonNull String repo, @NonNull String filePath, @NonNull String ref);

    @Nullable
    byte[] getRepositoryLogoIcon(@NonNull String repo, @NonNull String filepath, @NonNull String ref);

    @NonNull
    boolean repositoryAlreadyExists(@NonNull String repo);

    void createCommitDiscussion(@NonNull ProjectModel project, @NonNull BuildTaskModel task, @NonNull String comment);

    void rebaseMergeRequest(@NonNull String repo, @NonNull long iid);

    @Nullable
    GitlabMergeRequest closeMergeRequest(@NonNull String repo, @NonNull long iid);

    @Nullable
    GitlabMergeRequest directMergeRequest(@NonNull String repo, @NonNull String branch, @NonNull String ref,
            @Nullable String description);

    @Nullable
    GitlabBranch createBranch(@NonNull String repo, @NonNull String branch, @NonNull String ref);

    void deleteBranch(@NonNull String repo, @NonNull String branch);

    @Nullable
    GitlabMergeRequest createMergeRequest(@NonNull String repo, @NonNull String title, @NonNull String source,
            @NonNull String target, @NonNull boolean removeSource);

    @NonNull
    GitlabMergeRequest getMergeRequest(@NonNull String repo, @NonNull long iid);

    List<GitlabProtectedBranch> listProtectedBranches(@NonNull String repo, @Nullable String search);

    boolean checkUserMergePermission(@NonNull String repo, @NonNull long iid, @NonNull String uniqId);

    List<GitlabMember> *********************(@NonNull String repo, @Nullable String query);

    GitlabMergeRequest mergeMergeRequest(@NonNull String repo, @NonNull long iid, @Nullable String mergeCommitMessage,
            @Nullable boolean shouldRemoveSourceBranch);

    void pruneTemporaryBranches(@NonNull String repo);

    GitlabMergeRequest modifyLockFile(@NonNull ProjectModel project, @Nullable String branch);

    @CanIgnoreReturnValue
    GitlabCommit createCommit(@NonNull String repo, @NonNull String branch, @NonNull String commitMessage,
            @Nullable String startBranch, @Nullable String authorEmail, @Nullable String authorName,
            @Nullable List<GitlabCommitAction> actions);

    ResponseEntity<?> simpleProxy(@NonNull String repo, @NonNull BuildTaskModel task, Map<String, String> query);
}
