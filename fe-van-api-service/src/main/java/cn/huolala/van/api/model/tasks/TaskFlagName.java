package cn.huolala.van.api.model.tasks;

import lombok.Getter;
import org.springframework.lang.NonNull;

@Getter
public enum TaskFlagName implements TaskPath {
    Released("%s/tasks_config/%d/.van", "released flag"), Purged("%s/tasks_config/%d/.purged", "purged flag");

    @NonNull
    private final String template;
    @NonNull
    private final String name;

    TaskFlagName(@NonNull String template, @NonNull String name) {
        this.template = template;
        this.name = name;
    }
}
