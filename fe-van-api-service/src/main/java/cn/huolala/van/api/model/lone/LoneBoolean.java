package cn.huolala.van.api.model.lone;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import org.apache.commons.lang.math.NumberUtils;

public enum LoneBoolean {
    FALSE, TRUE;

    @JsonCreator
    public static LoneBoolean create(Object raw) {
        if (raw instanceof String) {
            String str = ((String) raw).toLowerCase();
            if (str.equals("yes") || str.equals("true")) {
                return TRUE;
            } else if (NumberUtils.isDigits(str)) {
                try {
                    if (!Long.valueOf(str).equals(0L)) {
                        return TRUE;
                    }
                } catch (NumberFormatException ignored) {
                    // noop
                }
            }
        } else if (raw instanceof Integer && (int) raw != 0) {
            return TRUE;
        }
        return FALSE;
    }

    @JsonValue
    public boolean toJson() {
        return this == TRUE;
    }
}
