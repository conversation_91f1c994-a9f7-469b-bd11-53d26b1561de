package cn.huolala.van.api.model.roles;

import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.model.CmdbAppRegion;
import cn.huolala.van.api.model.UserBase;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collector;

@Getter
public class UserRoleModel extends UserBase {
    /**
     * May be null if the Role is granted by Van.
     */
    @Nullable
    private final CmdbAppRegion region;

    @NonNull
    private final Role role;

    public UserRoleModel(@Nullable String userUniqId,
                         @Nullable String userName,
                         @Nullable CmdbAppRegion region,
                         @NonNull Role role) {
        super(userUniqId, userName);
        this.region = region;
        this.role = role;
    }

    public static Collector<ProjectUserModel, List<UserRoleModel>, List<UserRoleModel>> collectToList() {
        return Collector.of(
                ArrayList::new,
                (s, a) -> a.flatRoles().forEach(s::add),
                (a, b) -> {
                    a.addAll(b);
                    return b;
                },
                Collector.Characteristics.IDENTITY_FINISH);
    }
}
