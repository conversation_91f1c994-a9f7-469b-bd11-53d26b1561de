package cn.huolala.van.api.service;

import cn.huolala.van.api.model.DeployParams;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.CanaryRule;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.meta.PublishMetaInfo;
import cn.huolala.van.api.model.project.ProjectModel;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Optional;

public interface DeployService {

    @NonNull
    @CanIgnoreReturnValue
    DeployRecord deployForDev(@NonNull DeployParams deployParams);

    @NonNull
    @CanIgnoreReturnValue
    CanaryRecord releaseForProd(@NonNull DeployParams params, @Nullable Long backPoint, @Nullable Long headPoint);

    @NonNull
    Optional<PublishMetaInfo> getPublishMetaInfo(long launchId);

    @NonNull
    Optional<CanaryRule[]> getCurrentReleased(long projectId, @NonNull Region region);

    @NonNull
    Optional<LegacyDeployConfig> getCurrentLegacyReleased(long projectId, @NonNull Region region);

    @NonNull
    String fixPageServerCanaryFile(@NonNull Long projectId, @NonNull Region region, boolean overwrite);

    @Deprecated
    void releaseForProd(@NonNull UserModel user,
                        @NonNull ProjectModel project,
                        @NonNull Region region,
                        @NonNull LegacyDeployConfig config,
                        @Nullable String message);
}
