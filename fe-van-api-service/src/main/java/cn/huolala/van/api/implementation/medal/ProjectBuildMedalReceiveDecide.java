package cn.huolala.van.api.implementation.medal;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.entity.BuildTaskEntity;
import cn.huolala.van.api.dao.entity.UserEntity;
import cn.huolala.van.api.dao.repository.BuildTaskRepository;
import cn.huolala.van.api.service.MedalReceiveDecideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

@Component
public class ProjectBuildMedalReceiveDecide implements MedalReceiveDecideService {
    @Autowired
    private BuildTaskRepository buildTaskJPARepository;

    @Override
    public boolean receive(UserEntity user, LocalDateTime end) {
        Optional<BuildTaskEntity> firstByUsername = buildTaskJPARepository.getFirstByUsername(user.getUniqId());
        return firstByUsername.isPresent();
    }

    @Override
    public MedalEnum medal() {
        return MedalEnum.PROJECT_BUILD;
    }
}
