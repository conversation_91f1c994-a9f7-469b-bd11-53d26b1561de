package cn.huolala.van.api.exception;

import org.springframework.lang.NonNull;

public class FeishuServiceException extends RuntimeException {
    public FeishuServiceException(int code, String msg, String requestId) {
        super(String.format("Feishu: %s, code: %d, logId: %s%n", msg, code, requestId));
    }

    public FeishuServiceException(Exception exception) {
        super(String.format("Feishu: %s", exception.getMessage()), exception);
    }

    public FeishuServiceException(String message) {
        super(String.format("Feishu: %s", message));
    }

    @NonNull
    public static FeishuServiceException create(int code, String msg, String requestId) {
        switch (code) {
            case 131006:
                return new FeishuForbiddenException(code, msg, requestId);
            case 131005:
            case 91402:
                return new FeishuNotFoundException(code, msg, requestId);
            default:
                return new FeishuServiceException(code, msg, requestId);
        }
    }
}
