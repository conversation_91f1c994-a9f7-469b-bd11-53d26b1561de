package cn.huolala.van.api.implementation;

import cn.huolala.arch.hermes.common.util.JsonUtils;
import cn.huolala.van.api.service.HuolalaAIService;
import cn.lalaframework.config.core.PropertyConfigurer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Service
@Log4j2
public class HuolalaAIServiceImpl implements HuolalaAIService {

    @Value("${huolala.ai.token}")
    private String token;
    @Value("${huolala.ai.url}")
    private String baseUrl;

    @Autowired
    private CloseableHttpClient httpClient;

    private RequestConfig getRequestConfig() {
        return RequestConfig.custom().
                setConnectionRequestTimeout(PropertyConfigurer.getInteger("huolala.ai.http.timeout.connection", 100)).
                setConnectTimeout(PropertyConfigurer.getInteger("huolala.ai.http.timeout.connect", 5000)).
                setSocketTimeout(PropertyConfigurer.getInteger("huolala.ai.http.timeout.socket", 5000)).
                build();
    }

    private <P, T> T call(String account, String path, P params, TypeReference<T> type) {
        String url = baseUrl + path;
        HttpPost httpPost = new HttpPost(url);
        httpPost.setHeader("Authorization", "Bearer " + token);
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("hller", account);
        httpPost.setConfig(getRequestConfig());
        try {
            String jsonString = JsonUtils.toJSONString(params);
            httpPost.setEntity(new StringEntity(jsonString, ContentType.APPLICATION_JSON));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        CloseableHttpResponse response;
        try {
            response = httpClient.execute(httpPost);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (response.getStatusLine().getStatusCode() != HttpStatus.SC_OK) {
            String body = null;
            try {
                body = IOUtils.toString(response.getEntity().getContent(), StandardCharsets.UTF_8);
            } catch (IOException e) {
                log.error("read response error", e);
            }
            log.error(
                    "call huolala.ai failed, {}, {}",
                    response.getStatusLine().getStatusCode(),
                    body
            );
            throw new RuntimeException("call huolala.ai failed");
        }
        try {
            return JsonUtils.deserialize(response.getEntity().getContent(), type);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ChatCompletionResponse azure35ChatCompletion(String account, ChatCompletion chatCompletion) {
        return call(account, "/azure35", chatCompletion, new TypeReference<ChatCompletionResponse>() {
        });
    }
}
