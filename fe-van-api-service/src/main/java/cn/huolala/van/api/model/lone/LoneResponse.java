package cn.huolala.van.api.model.lone;

import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.model.PhpResponse;

public class LoneResponse<T> extends PhpResponse<T> {
    private static final long SUCCESS = 0L;

    public boolean ok() {
        return getRet() == SUCCESS;
    }

    public void assertOk() {
        if (!ok()) {
            throw new InternalRequestException(
                    String.format("Failed to request Lone, with ret=%d, msg=%s", getRet(), getMsg())
            );
        }
    }
}
