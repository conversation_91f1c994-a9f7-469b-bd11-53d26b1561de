package cn.huolala.van.api.service;

import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.VanResourceSummary;
import cn.huolala.van.api.model.project.ProjectModel;
import org.springframework.http.HttpEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

public interface TaskResourceService {
    @NonNull
    List<VanResourceSummary> listTaskResources(@NonNull BuildTaskModel task, @Nullable String path);

    @Nullable
    HttpEntity<InputStream> getTaskResource(@NonNull BuildTaskModel task, @Nullable String path);

    @NonNull
    Stream<VanResourceSummary> listTaskMeta(BuildTaskModel task, String tail);

    @Nullable
    HttpEntity<InputStream> getTaskMeta(@NonNull BuildTaskModel task, String path);

    Map<String, String> purgeBuildTaskResources(@NonNull ProjectModel project,
                                                @NonNull BuildTaskModel task);
}
