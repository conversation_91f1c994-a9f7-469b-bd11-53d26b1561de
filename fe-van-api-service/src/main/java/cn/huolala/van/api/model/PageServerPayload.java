package cn.huolala.van.api.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nonnull;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PageServerPayload {
    private String project;

    private String path;

    private String query;

    private String version;

    private String clientIp;

    private String env;

    private Map<String, String> headers;

    private Map<String, String> versionFiles;

    @Nonnull
    public String getCoupeFileContent() {
        Objects.requireNonNull(versionFiles, "for coupe request, \"version_files\" field is required");
        String content = versionFiles.get(".van-coupe.json");
        Objects.requireNonNull(content, ".van-coupe.json file content can't be null");
        return content;
    }

    public void insertHeader(@Nonnull String key, @Nonnull String value) {
        if (this.headers == null) {
            this.headers = new HashMap<>();
        }
        this.headers.put(key, value);
    }

}
