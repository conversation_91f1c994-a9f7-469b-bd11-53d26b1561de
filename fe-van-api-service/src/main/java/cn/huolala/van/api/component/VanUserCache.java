package cn.huolala.van.api.component;

import cn.huolala.van.api.model.UserModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.LongFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class VanUserCache {
    private final ConcurrentHashMap<Object, UserModel> cache;

    public VanUserCache() {
        cache = new ConcurrentHashMap<>();
    }

    public boolean containsKey(Object what) {
        return cache.containsKey(what);
    }

    @Nullable
    public UserModel get(Object what) {
        return cache.get(what);
    }

    public void put(@NonNull UserModel u) {
        cache.put(u.getId(), u);
        cache.put(u.getUniqId(), u);
    }

    @Nullable
    public UserModel wrap(@Nullable String uniqId, @NonNull Function<String, UserModel> factory) {
        if (uniqId == null) return null;
        if (containsKey(uniqId)) return get(uniqId);
        UserModel result = factory.apply(uniqId);
        if (result != null) put(result);
        return result;
    }

    @Nullable
    public UserModel wrap(@Nullable Long userId, @NonNull LongFunction<UserModel> factory) {
        if (userId == null) return null;
        if (containsKey(userId)) return get(userId);
        UserModel result = factory.apply(userId);
        if (result != null) put(result);
        return result;
    }

    @NonNull
    public <T> Stream<UserModel> wrap(@Nullable Collection<T> params, @NonNull Function<Set<T>, Stream<UserModel>> factory) {
        if (params == null || params.isEmpty()) return Stream.empty();
        Map<Boolean, Set<T>> parts = params.stream().collect(Collectors.partitioningBy(this::containsKey, Collectors.toSet()));
        Stream<UserModel> cached = parts.get(Boolean.TRUE).stream().map(this::get);
        Stream<UserModel> loaded = factory.apply(parts.get(Boolean.FALSE));
        if (loaded == null) return cached;
        loaded = loaded.map(i -> {
            if (i == null) return null;
            put(i);
            return i;
        }).filter(Objects::nonNull);
        return Stream.concat(cached, loaded);
    }
}
