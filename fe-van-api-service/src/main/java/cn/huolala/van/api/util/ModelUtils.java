package cn.huolala.van.api.util;

import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.ApplicationContext;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public class ModelUtils {
    private static final Pattern bigUnicode = Pattern.compile("\\\\U([0-9a-fA-F]{8})");

    private ModelUtils() {
    }

    @NonNull
    public static String parseStringJson(@Nullable String raw) {
        if (raw != null && raw.startsWith("\"")) {
            try {
                return getMapper().readValue(preprocessBigUnicode(raw), String.class);
            } catch (Exception ignored) {
                // noop
            }
        }
        return "";
    }

    @NonNull
    private static ObjectMapper getMapper() {
        ApplicationContext context = ApplicationContextUtil.getApplicationContext();
        return context.getBean(ObjectMapper.class);
    }

    @NonNull
    private static String preprocessBigUnicode(String raw) {
        StringBuffer result = new StringBuffer();
        Matcher matcher = bigUnicode.matcher(raw);
        while (matcher.find()) {
            int codePoint = Integer.parseInt(matcher.group(1), 16);
            matcher.appendReplacement(result, String.valueOf(Character.toChars(codePoint)));
        }
        matcher.appendTail(result);
        return result.toString();
    }

    @NonNull
    public static <K, M> Function<Map<K, M>, List<Optional<M>>> alignToList(@NonNull List<K> ids) {
        return map -> ids.stream().map(map::get).map(Optional::ofNullable).collect(Collectors.toList());
    }

    @NonNull
    public static <M, K> Collector<Entry<K, M>, ?, List<Optional<M>>> toAlignedList(@NonNull List<K> keyList) {
        return Collectors.collectingAndThen(Collectors.toMap(Entry::getKey, Entry::getValue), alignToList(keyList));
    }

    @NonNull
    public static <M, K> Collector<M, ?, List<Optional<M>>> toAlignedList(@NonNull List<K> keyList,
                                                                          @NonNull Function<M, K> getKey) {
        return Collectors.collectingAndThen(Collectors.toMap(getKey, i -> i), alignToList(keyList));
    }

    public static <T> T requireNonNullInGetter(T obj) {
        if (obj != null) return obj;
        StackTraceElement[] trace = Thread.currentThread().getStackTrace();
        if (trace.length > 2) {
            StackTraceElement last = trace[2];
            String msg = String.format("The %s.%s() must not return null", last.getClassName(), last.getMethodName());
            throw new NullPointerException(msg);
        }
        throw new NullPointerException();
    }
}
