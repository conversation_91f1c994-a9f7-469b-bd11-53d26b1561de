package cn.huolala.van.api.util;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.Change;
import cn.huolala.van.api.model.LastModifiedData;
import cn.huolala.van.api.model.Processor;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.tasks.TaskDirName;
import cn.huolala.van.api.model.tasks.TaskFileName;
import cn.huolala.van.api.model.tasks.TaskFlagName;
import cn.lalaframework.lock.LockTemplate;
import cn.lalaframework.spring.ApplicationContextUtil;
import cn.lalaframework.storage.adapter.ObjectResult;
import cn.lalaframework.storage.adapter.Storage;
import cn.lalaframework.utils.IOUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.math.IntMath;
import org.redisson.api.RLock;
import org.springframework.lang.NonNull;

import java.math.RoundingMode;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.huolala.api.constants.enums.Env.prd;

public class StorageHelper {
    private StorageHelper() {
        throw new IllegalStateException("Cannot construct a helper class");
    }

    @NonNull
    public static String buildLastModifiedFlagKey(@NonNull Env env) {
        String lastModifiedFlagKey = ".van/lastModified";
        if (env != prd) lastModifiedFlagKey += "-" + env.name();
        return lastModifiedFlagKey;
    }

    public static boolean hasFlag(@NonNull Storage storage,
                                  @NonNull String projectName,
                                  @NonNull Long taskId,
                                  @NonNull TaskFlagName flag) {
        return storage.exist(flag.build(projectName, taskId));
    }

    /**
     * @see <a href="https://huolala.feishu.cn/wiki/HDyNwpFILi5qetkzJ3pclZVknzh" />
     */
    public static void touchLastModifiedFlag(@NonNull Storage storage, @NonNull Env env) {
        storage.putValue(buildLastModifiedFlagKey(env), System.currentTimeMillis());
    }

    /**
     * @see <a href="https://huolala.feishu.cn/wiki/HDyNwpFILi5qetkzJ3pclZVknzh" />
     */
    @NonNull
    public static Optional<LastModifiedData<String>> getLastModifiedFlag(@NonNull Storage storage, @NonNull Env env) {
        return storage.getOptional(buildLastModifiedFlagKey(env)).map(obj -> {
            long timestamp = obj.getHeaders().getLastModified();
            OffsetDateTime lm = null;
            if (timestamp >= 0) {
                lm = Instant.ofEpochSecond(timestamp).atZone(ZoneId.systemDefault()).toOffsetDateTime();
            }
            return new LastModifiedData<>(lm, IOUtils.toString(obj.getBody()));
        });
    }

    public static void setFlag(@NonNull Storage storage,
                               @NonNull String projectName,
                               @NonNull Long taskId,
                               @NonNull TaskFlagName flag) {
        storage.putValue(flag.build(projectName, taskId), new Object());
    }

    @NonNull
    public static List<String> purge(@NonNull Storage storage,
                                     @NonNull String projectName,
                                     @NonNull Long taskId,
                                     @NonNull TaskFileName file) {
        String key = file.build(projectName, taskId);
        if (!storage.exist(key)) return Collections.emptyList();
        storage.delete(key);
        return Collections.singletonList(key);
    }

    @NonNull
    public static List<String> purge(@NonNull Storage storage,
                                     @NonNull String projectName,
                                     @NonNull Long taskId,
                                     @NonNull TaskDirName dir) {
        String path = dir.build(projectName, taskId);
        List<String> keys = Region.defaultStorage().listAll(path, null).map(ObjectResult::getKey)
            .collect(Collectors.toList());

        final int total = keys.size();
        final int page = 1000;
        IntStream.range(0, IntMath.divide(total, page, RoundingMode.CEILING)).map(i -> i * page)
            .mapToObj(i -> keys.subList(i, Math.min(i + page, total))).parallel().forEach(storage::batchDelete);
        return keys;
    }

    @NonNull
    public static <T> Change<T> partialUpdate(@NonNull Storage storage,
                                              @NonNull String key,
                                              @NonNull Class<T> type,
                                              @NonNull Processor<T> processor) {
        LockTemplate lockTemplate = ApplicationContextUtil.getBean(LockTemplate.class);
        RLock lock = lockTemplate.getLock(String.format("%s.%s:%s", storage.getStorageName(), "partialUpdate", key));
        if (lock.isLocked()) {
            throw new VanBadRequestException("Conflict: This value is currently being edited from another request.");
        }
        lock.lock();
        try {
            T before = storage.getOptionalValue(key, type).orElse(null);
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            // Deep clone the object, because it may be modified in the processor.
            T after = mapper.convertValue(before, type);
            after = processor.apply(after);
            storage.putValue(key, after);
            return new Change<>(before, after);
        } finally {
            lock.unlock();
        }
    }
}
