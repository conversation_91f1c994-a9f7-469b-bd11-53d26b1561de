package cn.huolala.van.api.model;

import cn.huolala.van.api.dao.enums.MiniprogramDeployType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MiniprogramDeployParams {

    @NonNull
    private MiniprogramDeployType deployType;

    @NonNull
    private Long creatorId;

    @NonNull
    private Integer robot;

    @Nullable
    private String version;

    @Nullable
    private String description;

}
