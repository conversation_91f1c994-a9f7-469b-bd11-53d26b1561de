package cn.huolala.van.api.model.lone;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LoneAppWindow {
    @JsonProperty("is_rel")
    private int isRel;
    @JsonProperty("window_type")
    private WindowType windowType;
    @JsonProperty("close_network")
    private WindowStatus windowStatus;

    @Getter
    @Setter
    public static class WindowStatus {
        private String name;
        private String startTime;
        private String endTime;
    }

    public enum WindowType {
        Unknown,
        Normal,
        Temp,
        Gray,
        Global,
    }
}
