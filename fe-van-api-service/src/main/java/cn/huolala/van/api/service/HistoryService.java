package cn.huolala.van.api.service;

import cn.huolala.api.constants.model.ActiveTaskIds;
import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.meta.PreviousCanary;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;

public interface HistoryService {
    @NonNull
    List<ConfigEnvTask> getLatestTaskIdForEachEnv(long projectId);

    @NonNull
    Map<ProjectType, Integer> countCanaryEachTypes();

    @NonNull
    ActiveTaskIds findActiveTaskIds();

    @NonNull
    List<NpmPublishRecord> searchNpmPublishHistory(@NonNull SearchNpmPublishHistoryParams params);

    @NonNull
    Map<ProjectType, Integer> countCanaryEachTypesLikeCanary();

    int countNpmPublishesByUser(@NonNull UserModel user);

    int countCanariesByUser(@NonNull UserModel user);

    @NonNull
    Optional<CanaryRecord> getHead(@Nullable Long projectId, @Nullable Region region);

    @NonNull
    CanaryRecord getHeadNeverNull(@Nullable Long projectId, @Nullable Region region);

    long getHeadIdNeverNull(@Nullable Long projectId, @Nullable Region region);

    @Nullable
    CanaryRecord getLatestCanary(long projectId);

    @NonNull
    List<CanaryRecord> getLatestCanaryForEachRegion(long projectId);

    @NonNull
    Optional<Long> getPreviousId(long canaryId);

    @NonNull
    Optional<CanaryRecord> getPrevious(@Nullable Long canaryId);

    @NonNull
    CanaryRecord getPreviousNeverNull(@Nullable Long canaryId);

    @NonNull
    Map<Long, Long> getPreviousIdMap(long projectId, Collection<Long> canaryIds);

    @NonNull
    Optional<CanaryRecord> getCanaryRecord(@Nullable Long canaryId);

    @NonNull
    CanaryRecord getCanaryRecordNeverNull(@Nullable Long canaryId);

    void setPreviousCanary(long canaryId, @NonNull PreviousCanary value);

    @NonNull
    List<DeployRecord> searchDeployHistory(long projectId, @NonNull HistorySearchForDeploy params);

    @NonNull
    List<CanaryRecord> searchCanaryHistory(long projectId, @NonNull HistorySearchForCanary params);

    boolean wasReleased(long projectId, long taskId);

    @NonNull
    List<CanaryRecord> findCanaryHistory(long projectId, @NonNull Set<Long> taskIds);

    void insertOrUpdateDeployLog(@NonNull UserModel user,
                                 @NonNull BuildTaskModel task,
                                 @NonNull ConfigEnv env);

    @NonNull
    DeployRecord getDevHead(@NonNull BuildTaskModel task, @NonNull ConfigEnv env);

    void insertCanaryLog(@NonNull DeployParams params);

    boolean isBeingUsedInTestEnv(long projectId, long taskId);

    int countNpmPublishHistory(long projectId, long taskId);

    @Nullable
    DeployRecord getLatestDeploy(long projectId, Env env);
}
