package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@JsonClassDescription("Redundant storage some data in OSS makes VanAgent easy to use")
public class VanProjectConfig {
    @JsonProperty(value = "allowed_host", defaultValue = "[]")
    private List<String> allowedHosts;

    @JsonProperty(value = "dev_public", defaultValue = "false")
    private boolean devPublic;

    @JsonPropertyDescription("Indicates whether this project is an international project")
    @JsonProperty(defaultValue = "false")
    private boolean international;

    @JsonProperty(defaultValue = "false")
    private boolean office;

    @JsonPropertyDescription("LOne AppID")
    private String appid;

    @JsonProperty(value = "off_web", defaultValue = "false")
    @JsonPropertyDescription("Indicates whether this project supports the offline package solution")
    private boolean offWeb;

    @JsonProperty(defaultValue = "false")
    @JsonPropertyDescription("Indicates whether this project is Workers")
    private boolean workers;
}
