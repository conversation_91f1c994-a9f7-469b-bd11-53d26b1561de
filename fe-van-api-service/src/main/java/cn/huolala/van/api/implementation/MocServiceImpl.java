package cn.huolala.van.api.implementation;

import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.model.MocRecordModel;
import cn.huolala.van.api.service.MocService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.IOUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Service
public class MocServiceImpl implements MocService {
    @Autowired
    private CloseableHttpClient httpClient;
    @Value("${huolala.moc.token}")
    private String token;
    @Value("${huolala.moc.url}")
    private String mocUrl;
    @Value("${huolala.moc.appid:ops-van-api}")
    private String appid;

    @Autowired
    private ObjectMapper mapper;

    @Override
    public void createMocRecord(MocRecordModel mocRecordModel) {

        HttpPost httpPost = new HttpPost(mocUrl + "/openapi/v1/records");

        mocRecordModel.setSource(appid);

        try {
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", token);
            String jsonString = mapper.writeValueAsString(mocRecordModel);
            httpPost.setEntity(new StringEntity(jsonString, ContentType.APPLICATION_JSON));
        } catch (IOException e) {
            throw new InternalMappingException(e);
        }

        CloseableHttpResponse response;
        try {
            response = httpClient.execute(httpPost);
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }

        try {
            String res = IOUtils.toString(response.getEntity().getContent(), StandardCharsets.UTF_8);
            int status = response.getStatusLine().getStatusCode();
            if (status != 200) {
                throw new InternalException("createMocRecord failed, status code: " + status + ", respBody: " + res);
            }
        } catch (IOException e) {
            throw new InternalException(e);
        }
    }
}
