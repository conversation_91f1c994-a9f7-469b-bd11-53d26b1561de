package cn.huolala.van.api.model;

import cn.lalaframework.spring.ApplicationContextUtil;
import cn.lalaframework.storage.adapter.Storage;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.context.ApplicationContext;
import org.springframework.lang.NonNull;
import org.springframework.util.function.SingletonSupplier;

@JsonFormat(shape = JsonFormat.Shape.STRING)
public enum RegionStorage {
    main, oversea;

    @NonNull
    private final SingletonSupplier<Storage> service;

    RegionStorage() {
        final String beanName = name() + "Storage";
        service = SingletonSupplier.of(() -> {
            ApplicationContext context = ApplicationContextUtil.getApplicationContext();
            return context.getBean(beanName, Storage.class);
        });
    }

    @NonNull
    public Storage getService() {
        return service.obtain();
    }
}
