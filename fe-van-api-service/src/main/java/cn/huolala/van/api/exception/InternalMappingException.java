package cn.huolala.van.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
public class InternalMappingException extends RuntimeException {
    public InternalMappingException(Exception exception) {
        super(exception);
    }

    public InternalMappingException(String message) {
        super(message);
    }

    public InternalMappingException(String message, Exception reason) {
        super(message, reason);
    }
}
