package cn.huolala.van.api.model.events;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Comparator;
import java.util.HashMap;
import java.util.stream.Stream;

public class ProjectEventPolling {
    @NonNull
    private final Query query;
    @NonNull
    private final HashMap<ProjectId, ProjectEventQueue> storage;
    @Nullable
    private Long cursor;

    public ProjectEventPolling(@NonNull Query query) {
        this.query = query;
        this.storage = new HashMap<>();
    }

    public void poll() {
        // Initial the maxBuildTaskUpdatedAt if null.
        if (cursor == null) {
            query.apply(0L, 1).findFirst().ifPresent(pe -> cursor = pe.getKey());
        }
        if (cursor == null) return;

        // Find all events which after the maxId.
        query.apply(cursor, 100)
                .sorted(Comparator.comparing(ProjectEvent::getKey, Comparator.reverseOrder()))
                .forEach(this::collectProjectEvent);
    }

    private void collectProjectEvent(@NonNull ProjectEvent event) {
        // Update the maxId.
        cursor = event.getKey();
        if (event.getType() == ProjectEventType.UnknownEvent) return;
        ProjectId projectId = ProjectId.singleInstance(event.getProjectId());
        // Add the event object into the queue and remove overflowed items.
        storage.computeIfAbsent(projectId, k -> new ProjectEventQueue()).add(event);
        projectId.signalAll();
    }

    @Nullable
    public ProjectEventQueue getQueue(@NonNull ProjectId projectId) {
        return storage.get(projectId);
    }

    @FunctionalInterface
    public interface Query {
        @NonNull
        Stream<ProjectEvent> apply(long key, int limit);
    }
}
