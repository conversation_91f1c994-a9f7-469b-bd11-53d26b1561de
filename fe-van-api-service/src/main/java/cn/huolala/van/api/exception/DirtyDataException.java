package cn.huolala.van.api.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
public class DirtyDataException extends RuntimeException {
    public DirtyDataException(String message) {
        super(message);
    }

    public DirtyDataException(String message, Exception exception) {
        super(message, exception);
    }

    /**
     * Create a ResourceNotFoundException.
     *
     * @param resourceName Lower case is recommended.
     */
    public static DirtyDataException create(String resourceName, String keyName, Object key) {
        String msg = String.format("The %s has dirty where %s=%s", resourceName, keyName, key);
        return new DirtyDataException(msg);
    }

    public static DirtyDataException create(JsonProcessingException exception) {
        return new DirtyDataException(exception.getMessage(), exception);
    }
}
