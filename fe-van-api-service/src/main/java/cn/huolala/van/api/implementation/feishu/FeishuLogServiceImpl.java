package cn.huolala.van.api.implementation.feishu;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.service.feishu.RemoteLogService;
import cn.lalaframework.utils.ConfigUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lark.oapi.Client;
import com.lark.oapi.card.enums.MessageCardHeaderTemplateEnum;
import com.lark.oapi.card.model.*;
import com.lark.oapi.service.im.v1.enums.CreateMessageReceiveIdTypeEnum;
import com.lark.oapi.service.im.v1.enums.MsgTypeEnum;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import lombok.extern.log4j.Log4j2;
import org.apache.logging.log4j.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@Log4j2
public class FeishuLogServiceImpl implements RemoteLogService {
    @Resource(name = "van")
    private Client client;

    @Value("${feishu.chatId:oc_f830e571aab16bc5533796d9616326dc}")
    private String chatId;
    @Autowired
    private ObjectMapper mapper;

    private MessageCardField[] buildTagFields(Map<String, String> tags) {
        if (tags == null) {
            return null;
        }
        MessageCardField[] tagFields = new MessageCardField[tags.size() + 1];
        tagFields[0] = MessageCardField.newBuilder()
                .isShort(false)
                .text(MessageCardLarkMd.newBuilder()
                        .content("**日志详情**").build()).build();
        int i = 1;
        for (Map.Entry<String, String> entry : tags.entrySet()) {
            tagFields[i] = MessageCardField.newBuilder()
                    .isShort(false)
                    .text(MessageCardLarkMd.newBuilder()
                            .content(String.format("**%s:** %s", entry.getKey(), entry.getValue())).build())
                    .build();
            i++;
        }
        return tagFields;
    }

    private void sendCardMessage(Level level, String message, Map<String, String> tags, Throwable e) {

        List<MessageCardElement> elementList = new ArrayList<>();
        elementList.add(MessageCardDiv.newBuilder()
                .fields(new MessageCardField[]{
                        MessageCardField.newBuilder()
                                .isShort(false)
                                .text(MessageCardLarkMd.newBuilder()
                                        .content("**机器信息**").build()).build(),
                        MessageCardField.newBuilder()
                                .isShort(false)
                                .text(MessageCardLarkMd.newBuilder()
                                        .content(String.format("**IP:** %s", ConfigUtil.getIp())).build())
                                .build()
                })
                .build());
        MessageCardField[] tagFields = buildTagFields(tags);
        if (tagFields != null && tagFields.length > 0) {
            elementList.add(MessageCardHr.newBuilder().build());
            elementList.add(MessageCardDiv.newBuilder()
                    .fields(buildTagFields(tags))
                    .build());
        }
        elementList.add(MessageCardHr.newBuilder().build());
        elementList.add(
                MessageCardDiv.newBuilder()
                        .fields(new MessageCardField[]{
                                MessageCardField.newBuilder()
                                        .isShort(false)
                                        .text(MessageCardLarkMd.newBuilder()
                                                .content("**message:**").build()).build(),
                                MessageCardField.newBuilder()
                                        .isShort(false)
                                        .text(MessageCardPlainText.newBuilder()
                                                .content(message == null ? "" : message).build())
                                        .build()
                        })
                        .build()
        );
        if (e != null) {
            elementList.add(MessageCardHr.newBuilder().build());
            elementList.add(
                    MessageCardDiv.newBuilder()
                            .fields(new MessageCardField[]{
                                    MessageCardField.newBuilder()
                                            .isShort(false)
                                            .text(MessageCardLarkMd.newBuilder()
                                                    .content("**exception:**").build()).build(),
                                    MessageCardField.newBuilder()
                                            .isShort(false)
                                            .text(MessageCardPlainText.newBuilder()
                                                    .content(e.getMessage()).build())
                                            .build()
                            })
                            .build()
            );
        }

        MessageCard card = MessageCard.newBuilder()
                .config(MessageCardConfig.newBuilder()
                        .enableForward(true)
                        .updateMulti(false)
                        .wideScreenMode(true)
                        .build())
                .header(MessageCardHeader.newBuilder()
                        .title(MessageCardPlainText.newBuilder()
                                .content(String.format("【%s】日志告警", ConfigUtil.getAppId()))
                                .build())
                        .template(level == Level.INFO ? MessageCardHeaderTemplateEnum.BLUE : MessageCardHeaderTemplateEnum.RED)
                        .build())
                .elements(elementList.toArray(new MessageCardElement[0]))
                .build();

        CreateMessageReq createMessageReq = CreateMessageReq.newBuilder()
                .receiveIdType(CreateMessageReceiveIdTypeEnum.CHAT_ID)
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .msgType(MsgTypeEnum.MSG_TYPE_INTERACTIVE.getValue())
                        .receiveId(chatId)
                        .content(card.String())
                        .build())
                .build();
        try {
            CreateMessageResp createMessageResp = client.im().message().create(createMessageReq);
            if (!createMessageResp.success()) {
                log.error("send card message error, code: {}, message: {}, error: {}", createMessageResp.getCode(), createMessageResp.getMsg(), createMessageResp.getError());
            }
        } catch (Exception e1) {
            log.error("send card message error", e1);
        }
    }

    @Override
    public void log(Level level, String message) {
        log(level, message, null, null);
    }

    @Override
    public void log(Level level, String message, Map<String, String> tags) {
        log(level, message, tags, null);
    }

    @Override
    public void log(Level level, String message, Throwable e) {
        log(level, message, null, e);

    }

    @Override
    public void log(Level level, String message, Map<String, String> tags, Throwable e) {
        try {
            log.log(level, message + mapper.writeValueAsString(tags), e);
        } catch (JsonProcessingException ex) {
            throw new InternalMappingException(ex);
        }
        sendCardMessage(level, message, tags, e);
    }

    @Override
    public void info(String message) {
        info(message, null, null);
    }

    @Override
    public void info(String message, Map<String, String> tags) {
        info(message, tags, null);
    }

    private void info(String message, Map<String, String> tags, Throwable e) {
        log(Level.INFO, message, tags, e);
    }

    @Override
    public void error(String message) {
        error(message, null, null);
    }

    @Override
    public void error(String message, Map<String, String> tags) {
        error(message, tags, null);
    }

    @Override
    public void error(String message, Throwable e) {
        error(message, null, e);
    }

    @Override
    public void error(String message, Map<String, String> tags, Throwable e) {
        log(Level.ERROR, message, tags, e);
    }
}
