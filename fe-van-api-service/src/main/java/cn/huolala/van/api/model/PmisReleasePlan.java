package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PmisReleasePlan {
    @ApiModelProperty("This value is a long number which is float-64 unsafe, so it will be serialized with a string.")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private String releaseName;

    @ApiModelProperty("@example The time format is like \"2022-10-29 00:00:00\".\n" +
            "NOTE: As you see, the pmis does not provide the timezone information.")
    private String releaseTime;
    @ApiModelProperty("@example The time format is like \"2022-10-29 00:00:00\".\n" +
            "NOTE: As you see, the pmis does not provide the timezone information.")
    private String releaseTimeEnd;

    private Object grayTime;
}
