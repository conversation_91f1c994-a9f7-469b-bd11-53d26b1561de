package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.entity.UserMedalEntity;
import cn.huolala.van.api.dao.projection.MedalRankingProjection;
import cn.huolala.van.api.dao.repository.UserMedalRepository;
import cn.huolala.van.api.model.UserMedalModel;
import cn.huolala.van.api.service.UserMedalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserMedalServiceImpl implements UserMedalService {

    @Autowired
    private UserMedalRepository userMedalRepository;

    @Override
    @NonNull
    public List<UserMedalModel> list(@NonNull Long userId) {
        return userMedalRepository.findByUserIdOrderByCreatedAtDesc(userId)
                .stream().map(UserMedalModel::create).collect(Collectors.toList());
    }

    @Override
    public Long add(Long userId, MedalEnum name, String remark) {
        UserMedalEntity entity = userMedalRepository.save(UserMedalEntity
                .builder()
                .userId(userId)
                .uniqName(name)
                .remark(remark)
                .build()
        );
        return entity.getId();
    }

    @Override
    public long countMedalReceived(MedalEnum name) {
        return userMedalRepository.countByUniqName(name);
    }

    @Override
    @NonNull
    public Page<MedalRankingProjection> rank(@NonNull Collection<String> excludeUsers, int page, int size) {
        // NOTE: The first argument must not be an empty list.
        Collection<String> fixedSet = excludeUsers.isEmpty() ? Collections.singleton("") : excludeUsers;
        return userMedalRepository.rank(fixedSet, PageRequest.of(page, size));
    }
}
