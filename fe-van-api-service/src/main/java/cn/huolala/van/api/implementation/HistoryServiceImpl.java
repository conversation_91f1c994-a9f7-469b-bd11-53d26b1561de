package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.api.constants.model.ActiveTaskIds;
import cn.huolala.api.constants.model.LastModifiedModel;
import cn.huolala.van.api.dao.entity.*;
import cn.huolala.van.api.dao.repository.CanaryHistoryRepository;
import cn.huolala.van.api.dao.repository.ComponentDeployHistoryRepository;
import cn.huolala.van.api.dao.repository.DeployHistoryRepository;
import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.meta.PreviousCanary;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.HistoryService;
import cn.huolala.van.api.service.MetaService;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.EncodingUtils;
import cn.huolala.van.api.util.VanUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.van.api.exception.ResourceNotFoundException.curryResourceNotFoundException;
import static java.util.Optional.ofNullable;

@Service
public class HistoryServiceImpl implements HistoryService {
    private static final AtomicReference<ActiveTaskIds> activeTaskIdsCache = new AtomicReference<>();

    @Autowired
    private DeployHistoryRepository deployHistoryRepository;
    @Autowired
    private CanaryHistoryRepository canaryHistoryRepository;
    @Autowired
    private ComponentDeployHistoryRepository componentDeployHistoryRepository;
    @Autowired
    private MetaService metaService;
    @Autowired
    private UserService userService;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private ObjectMapper mapper;

    @NonNull
    private static Predicate buildProjectIdPredicate(long projectId,
                                                     @NonNull CriteriaBuilder cb,
                                                     @NonNull Root<?> root) {
        return cb.equal(root.get("projectId"), projectId);
    }

    @NonNull
    private static Order[] buildOrders(@NonNull CriteriaBuilder cb, @NonNull Root<? extends BaseEntity> root) {
        return new Order[]{cb.desc(root.get("updatedAt")), cb.desc(root.get("id"))};
    }

    @NonNull
    private static Supplier<ResourceNotFoundException> getExceptionSupplier(@Nullable Long projectId,
                                                                            @Nullable Region region) {
        return curryResourceNotFoundException(
                CanaryRecord.NAME, "[project, region]",
                String.format("[%d, %s]", projectId, region));
    }

    @NonNull
    private Optional<Predicate> buildTimeRangePredicate(@NonNull HistorySearchBase params,
                                                        @NonNull CriteriaBuilder cb,
                                                        @NonNull Root<? extends BaseEntity> root) {
        Path<LocalDateTime> path = root.get("updatedAt");

        Predicate left = ofNullable(params.getStartDate())
                .map(OffsetDateTime::toLocalDateTime)
                .map(i -> cb.greaterThanOrEqualTo(path, i))
                .orElse(null);

        Predicate right = ofNullable(params.getEndDate())
                .map(OffsetDateTime::toLocalDateTime)
                .map(i -> cb.lessThan(path, i))
                .orElse(null);

        if (left != null && right != null) {
            // noinspection SuspiciousNameCombination
            return Optional.of(cb.and(left, right));
        }

        if (left != null) return Optional.of(left);
        if (right != null) return Optional.of(right);

        return Optional.empty();
    }

    @NonNull
    private Optional<Predicate> buildCreatorIdIn(@Nullable Set<String> userUniqIds,
                                                 @NonNull Root<? extends BaseEntity> root) {
        return userService.getByUniqId(userUniqIds).map(UserModel::getId)
                .collect(Collectors.collectingAndThen(Collectors.toSet(), Optional::of))
                .filter(i -> !i.isEmpty())
                .map(root.get("creatorId")::in);
    }

    @NonNull
    private ActiveTaskIds getActiveTaskIdsWithoutCache(OffsetDateTime lastModified) {
        Set<Long> ids = canaryHistoryRepository
                .findActiveTaskIds()
                .stream().map(CsvRow::create).flatMap(CsvRow::stream).map(Long::parseLong)
                .collect(Collectors.toSet());
        return new ActiveTaskIds(lastModified, ids);
    }

    @Override
    @NonNull
    public List<ConfigEnvTask> getLatestTaskIdForEachEnv(long projectId) {
        return deployHistoryRepository.getLatestTaskIdForEachEnv(projectId).stream()
                .map(i -> {
                    ConfigEnv env = ConfigEnv.fromString(i.getContent());
                    if (env == null) return null;
                    return new ConfigEnvTask(env, i.getId());
                }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @NonNull
    @Override
    public Map<ProjectType, Integer> countCanaryEachTypes() {
        return canaryHistoryRepository.countCanaryEachTypes()
                .stream().collect(Collectors.toMap(
                        i -> ProjectType.createFromOrdinal(i[0]),
                        i -> i[1],
                        Integer::sum
                ));
    }

    @Override
    @NonNull
    public ActiveTaskIds findActiveTaskIds() {
        return activeTaskIdsCache.updateAndGet(value -> {
            OffsetDateTime lastModified = canaryHistoryRepository.getLastModifiedRecord()
                    // Merge the ID value as nanoseconds into the createdAt field.
                    .map(
                            i -> i.getCreatedAt().atZone(ZoneId.systemDefault())
                                    .toOffsetDateTime()
                                    .withNano((int) (i.getId() % 1E9))
                    ).orElse(null);

            switch (LastModifiedModel.compare(value, lastModified)) {
                case After:
                case Equal:
                    return value;
                default:
                    return getActiveTaskIdsWithoutCache(lastModified);
            }
        });
    }

    @Override
    @NonNull
    public List<NpmPublishRecord> searchNpmPublishHistory(@NonNull SearchNpmPublishHistoryParams params) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ComponentDeployHistoryEntity> query = cb.createQuery(ComponentDeployHistoryEntity.class);
        Root<ComponentDeployHistoryEntity> root = query.from(ComponentDeployHistoryEntity.class);

        Predicate[] predicates = Stream.of(
                buildProjectIdPredicate(params.getProjectId(), cb, root),
                ofNullable(params.getStatus()).map(v -> cb.equal(root.get("status"), v)).orElse(null),
                ofNullable(params.getTaskId()).map(v -> cb.equal(root.get("taskId"), v)).orElse(null),
                ofNullable(params.getVersion()).map(v -> cb.equal(root.get("version"), v)).orElse(null),
                ofNullable(params.getUserUniqId()).map(userService::getByUniqId)
                        .map(u -> cb.equal(root.get("creatorId"), u.getId()))
                        .orElse(null)
        ).filter(Objects::nonNull).toArray(Predicate[]::new);

        query
                .where(predicates)
                .orderBy(cb.desc(root.get("createdAt")), cb.desc(root.get("id")));

        TypedQuery<ComponentDeployHistoryEntity> eQuery = entityManager.createQuery(query);

        if (params.getLimit() != null) eQuery.setMaxResults(params.getLimit());

        return userService.buildListWithUserModel(
                eQuery.getResultList(),
                ComponentDeployHistoryEntity::getCreatorId,
                NpmPublishRecord::create);
    }

    @NonNull
    @Override
    public Map<ProjectType, Integer> countCanaryEachTypesLikeCanary() {
        return canaryHistoryRepository.countCanaryEachTypesLikeCanary()
                .stream().collect(Collectors.toMap(
                        i -> ProjectType.createFromOrdinal(i[0]),
                        i -> i[1],
                        Integer::sum
                ));
    }

    @Override
    public int countNpmPublishesByUser(@NonNull UserModel user) {
        return componentDeployHistoryRepository.countByCreatorId(user.getId());
    }

    @Override
    public int countCanariesByUser(@NonNull UserModel user) {
        return canaryHistoryRepository.countByCreatorId(user.getId());
    }

    @NonNull
    @Override
    public Optional<CanaryRecord> getHead(@Nullable Long projectId, @Nullable Region region) {
        if (projectId == null) return Optional.empty();
        // Considering downward compatibility, the both "" and "cn" mean Region.cn.
        Collection<String> regions = region == null || region == Region.cn
                ? Arrays.asList("", Region.cn.name())
                : Collections.singleton(region.name());
        return canaryHistoryRepository.getHead(projectId, regions).map(this::entityToCanaryRecord);
    }

    @NonNull
    @Override
    public CanaryRecord getHeadNeverNull(@Nullable Long projectId, @Nullable Region region) {
        return getHead(projectId, region).orElseThrow(getExceptionSupplier(projectId, region));
    }

    private Optional<Long> getHeadId(@Nullable Long projectId, @Nullable Region region) {
        if (projectId == null) return Optional.empty();
        // Considering downward compatibility, the both "" and "cn" mean Region.cn.
        Collection<String> regions = region == null || region == Region.cn
                ? Arrays.asList("", Region.cn.name())
                : Collections.singleton(region.name());
        return canaryHistoryRepository.getHeadId(projectId, regions);
    }

    @Override
    public long getHeadIdNeverNull(@Nullable Long projectId, @Nullable Region region) {
        return getHeadId(projectId, region).orElseThrow(getExceptionSupplier(projectId, region));
    }

    @Nullable
    private CanaryRecord entityToCanaryRecord(@Nullable CanaryHistoryEntity e) {
        if (e == null) return null;
        return CanaryRecord.create(e, userService.getById(e.getCreatorId()));
    }

    @Nullable
    @Override
    @Deprecated
    public CanaryRecord getLatestCanary(long projectId) {
        return canaryHistoryRepository.findFirstByProjectIdOrderByCreatedAtDescIdDesc(projectId)
                .map(this::entityToCanaryRecord).orElse(null);
    }

    @Override
    @NonNull
    public List<CanaryRecord> getLatestCanaryForEachRegion(long projectId) {
        List<CanaryHistoryEntity> list = canaryHistoryRepository.findLatestCanaryGroupByRegion(projectId);
        return userService.buildListWithUserModel(list, CanaryHistoryEntity::getCreatorId, CanaryRecord::create);
    }

    @NonNull
    @Override
    public Optional<Long> getPreviousId(long canaryId) {
        return metaService.getOptional(MetaType.VanCanaryChain, canaryId, PreviousCanary.class)
                .map(PreviousCanary::getPreCanaryId);
    }

    @NonNull
    @Override
    public Optional<CanaryRecord> getPrevious(@Nullable Long canaryId) {
        if (canaryId == null) return Optional.empty();
        return getPreviousId(canaryId).flatMap(this::getCanaryRecord);
    }

    @NonNull
    @Override
    public CanaryRecord getPreviousNeverNull(@Nullable Long canaryId) {
        return getPrevious(canaryId).orElseThrow(curryResourceNotFoundException("canary record", "id", canaryId));
    }

    @NonNull
    @Override
    public Map<Long, Long> getPreviousIdMap(long projectId, Collection<Long> canaryIds) {
        Map<Long, Long> map = metaService.getValue(MetaType.VanCanaryChain, canaryIds, PreviousCanary.class)
                .entrySet().stream().map(i -> Pair.of(i.getKey(), i.getValue().getPreCanaryId()))
                .filter(i -> i.getValue() != null)
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        if (!map.isEmpty()) assertOwnership(new HashSet<>(map.values()), projectId);
        return map;
    }

    private void assertOwnership(@Nullable Set<Long> canaryIds, long projectId) {
        VanUtils.assertOwnership(
                projectId, canaryIds, canaryHistoryRepository::findOutOfProjectId,
                "canary record", "canary records");
    }

    @NonNull
    @Override
    public Optional<CanaryRecord> getCanaryRecord(@Nullable Long canaryId) {
        return ofNullable(canaryId).flatMap(canaryHistoryRepository::get).map(this::entityToCanaryRecord);
    }

    @NonNull
    @Override
    public CanaryRecord getCanaryRecordNeverNull(@Nullable Long canaryId) {
        return getCanaryRecord(canaryId).orElseThrow(() -> ResourceNotFoundException.create(
                "canary record", "id", canaryId));
    }

    @Override
    public void setPreviousCanary(long canaryId, @NonNull PreviousCanary value) {
        metaService.setValue(MetaType.VanCanaryChain, canaryId, PreviousCanary.class, value);
    }

    @Override
    @NonNull
    public List<DeployRecord> searchDeployHistory(long projectId, @NonNull HistorySearchForDeploy params) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<DeployHistoryEntity> query = cb.createQuery(DeployHistoryEntity.class);
        Root<DeployHistoryEntity> root = query.from(DeployHistoryEntity.class);

        ArrayList<Predicate> conditions = new ArrayList<>();

        conditions.add(buildProjectIdPredicate(projectId, cb, root));
        buildTimeRangePredicate(params, cb, root).ifPresent(conditions::add);
        buildCreatorIdIn(params.getUserUniqIds(), root).ifPresent(conditions::add);

        Set<String> branchPrefix = params.getBranchPrefix();
        if (branchPrefix != null && !branchPrefix.isEmpty()) {
            Root<BuildTaskEntity> buildTaskRoot = query.from(BuildTaskEntity.class);
            conditions.add(cb.equal(root.get("taskId"), buildTaskRoot.get("id")));
            branchPrefix.stream()
                    .map(EncodingUtils::escapeLike)
                    .map(i -> cb.like(buildTaskRoot.get("branch"), i + "%"))
                    .collect(Collectors.collectingAndThen(Collectors.toList(), Optional::of))
                    .map(i -> cb.or(i.toArray(new Predicate[0])))
                    .ifPresent(conditions::add);
        }

        Env env = params.getEnvName();
        if (env != null) {
            Path<String> pEnv = root.get("env");
            Set<String> stableNames = params.getStableNames();
            if (stableNames != null && !stableNames.isEmpty()) {
                stableNames.stream()
                        .map(i -> ConfigEnv.from(env, i).toString())
                        .collect(Collectors.collectingAndThen(Collectors.toSet(), Optional::of))
                        .ifPresent(s -> conditions.add(pEnv.in(s)));
            } else {
                conditions.add(cb.or(
                        cb.like(pEnv, EncodingUtils.escapeLike(env.name()) + "-%"),
                        cb.equal(pEnv, env.name())
                ));
            }
        }

        query
                .select(root)
                .where(conditions.toArray(new Predicate[0]))
                .orderBy(buildOrders(cb, root));

        List<DeployHistoryEntity> list = entityManager.createQuery(query)
                .setFirstResult(params.getOffset())
                .setMaxResults(params.getLimit())
                .getResultList();

        return userService.buildListWithUserModel(list, DeployHistoryEntity::getCreatorId, DeployRecord::create);
    }

    @Override
    @NonNull
    public List<CanaryRecord> searchCanaryHistory(long projectId, @NonNull HistorySearchForCanary params) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<CanaryHistoryEntity> query = cb.createQuery(CanaryHistoryEntity.class);
        Root<CanaryHistoryEntity> root = query.from(CanaryHistoryEntity.class);

        List<Predicate> conditions = new ArrayList<>();

        conditions.add(buildProjectIdPredicate(projectId, cb, root));
        buildTimeRangePredicate(params, cb, root).ifPresent(conditions::add);
        buildCreatorIdIn(params.getUserUniqIds(), root).ifPresent(conditions::add);

        Set<String> regions = params.getRegions();
        if (regions != null && !regions.isEmpty()) {
            conditions.add(root.get("region").in(regions));
        }

        query
                .select(root)
                .where(conditions.toArray(new Predicate[0]))
                .orderBy(buildOrders(cb, root));

        List<CanaryHistoryEntity> list = entityManager.createQuery(query)
                .setFirstResult(params.getOffset())
                .setMaxResults(params.getLimit())
                .getResultList();

        return userService.buildListWithUserModel(list, CanaryHistoryEntity::getCreatorId, CanaryRecord::create);
    }

    @Override
    public boolean wasReleased(long projectId, long taskId) {
        return canaryHistoryRepository.wasReleased(projectId, taskId) > 0;
    }

    @NonNull
    @Override
    public List<CanaryRecord> findCanaryHistory(long projectId, @NonNull Set<Long> taskIds) {
        List<CanaryHistoryEntity> list = canaryHistoryRepository.findByProjectIdAndTaskId(projectId, taskIds);
        return userService.buildListWithUserModel(list, CanaryHistoryEntity::getCreatorId, CanaryRecord::create);
    }

    @Override
    public void insertOrUpdateDeployLog(@NonNull UserModel user,
                                        @NonNull BuildTaskModel task,
                                        @NonNull ConfigEnv env) {
        deployHistoryRepository.insertOrUpdate(task.getProjectId(), task.getId(), env.toString(), user.getId());
    }

    @NonNull
    @Override
    public DeployRecord getDevHead(@NonNull BuildTaskModel task, @NonNull ConfigEnv env) {
        DeployHistoryEntity entity = deployHistoryRepository.get(task.getProjectId(), task.getId(), env.toString());
        Objects.requireNonNull(entity, "The entity must not null here");
        UserModel user = userService.getById(entity.getCreatorId());
        DeployRecord deployRecord = DeployRecord.create(entity, user);
        if (deployRecord == null) throw new InternalException("The entity must not null here");
        return deployRecord;
    }

    @Override
    public void insertCanaryLog(@NonNull DeployParams params) {
        Long projectId = params.getProject().getId();
        Long userId = params.getUser().getId();
        Region region = params.getRegion();
        String regionName = region == Region.cn ? "" : region.name();
        LegacyDeployConfig config = params.getConfig();
        String canary;
        try {
            canary = mapper.writeValueAsString(config);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
        canaryHistoryRepository.insert(
                projectId, userId, regionName, canary,
                StringUtils.join(config.collectTaskIds(), ","),
                StringUtils.defaultString(params.getMessage()));
    }

    @Override
    public boolean isBeingUsedInTestEnv(long projectId, long taskId) {
        return deployHistoryRepository.isBeingUsedInTestEnv(projectId, taskId) > 0;
    }

    @Override
    public int countNpmPublishHistory(long projectId, long taskId) {
        return componentDeployHistoryRepository.countByProjectIdAndTaskId(projectId, taskId);
    }

    @Override
    public DeployRecord getLatestDeploy(long projectId, Env env) {
        DeployHistoryEntity entity = deployHistoryRepository.getLatestDeploy(projectId, env.getLongName());
        if (entity == null) {
            return null;
        }
        UserModel user = userService.getById(entity.getCreatorId());
        return DeployRecord.create(entity, user);
    }

}
