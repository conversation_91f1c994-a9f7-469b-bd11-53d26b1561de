package cn.huolala.van.api.model.lone;

import cn.huolala.van.api.model.CsvRow;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.util.List;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LoneAppDetail extends LoneAppSummary {
    @Nullable
    private LoneLangType langType;
    @Nullable
    private LoneAppType appType;
    @Nullable
    private LoneAccessType accessType;

    @Nullable
    private String langVersion;
    @Nullable
    private String level;
    @Nullable
    private String gitLink;
    @Nullable
    private String des;
    @Nullable
    private String productId;
    @Nullable
    private String regionIds;
    @Nullable
    private String frameworkDocLink;
    @Nullable
    private String forbidden;
    @Nullable
    private String updatedAt;
    @Nullable
    private String createdAt;

    @Nullable
    private BusUser busUser;

    @Nullable
    private List<Object> product;
    @Nullable
    private List<BusDep> busdepList;

    @Nullable
    private List<Integer> domainAccessType;
    @Nullable
    private String domain;
    @Nullable
    private List<DomainInfo> domainList;

    @Nullable
    private String gitUrl;
    @Nullable
    private String langTypeName;
    @Nullable
    private List<String> labelList;
    @Nullable
    private LoneBoolean isCore;
    @Nullable
    private LoneBoolean isCrux;
    @Nullable
    private LoneBoolean isAuth;
    @Nullable
    private LoneBoolean isK8s;

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class BusDep {
        @Nullable
        private String name;
        @Nullable
        private String orgId;
        @Nullable
        private String parentOrgId;
        @Nullable
        private String duty;
    }

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class DomainInfo {
        @Nullable
        private String id;
        @Nullable
        private String domainId;
        @Nullable
        private String region;
        @Nullable
        private String domain;
    }

    @Setter
    @Getter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class BusUser {
        @Nullable
        private CsvRow leader;
        @Nullable
        private CsvRow develop;
        @Nullable
        private CsvRow tester;
        @Nullable
        private CsvRow devops;
        @Nullable
        private LoneRegion region;
        @Nullable
        private String updatedAt;
        @Nullable
        private String createdAt;

        @Nullable
        private LoneBoolean isLeader;
        @Nullable
        private LoneBoolean isDevelop;
        @Nullable
        private LoneBoolean isTester;
        @Nullable
        private LoneBoolean isDevops;

        @Nullable
        private List<String> leaderListCn;
        @Nullable
        private List<String> leaderList;
        @Nullable
        private List<String> leaderListUid;
        @Nullable
        private List<String> developListCn;
        @Nullable
        private List<String> developList;
        @Nullable
        private List<String> developListUid;
        @Nullable
        private List<String> testerListCn;
        @Nullable
        private List<String> testerList;
        @Nullable
        private List<String> testerListUid;
        @Nullable
        private List<String> devopsListCn;
        @Nullable
        private List<String> devopsList;
        @Nullable
        private List<String> devopsListUid;
    }
}
