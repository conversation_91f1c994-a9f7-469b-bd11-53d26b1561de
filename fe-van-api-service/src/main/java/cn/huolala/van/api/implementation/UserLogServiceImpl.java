package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.dao.entity.UserLogEntity;
import cn.huolala.van.api.dao.repository.UserLogRepository;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.model.AuditLog;
import cn.huolala.van.api.model.UserLogModel;
import cn.huolala.van.api.model.UserLogSearchParams;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.service.UserLogService;
import cn.huolala.van.api.service.UserService;
import cn.huolala.van.api.util.BatchInsertion;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.criteria.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.van.api.util.EncodingUtils.escapeLike;

@Service
public class UserLogServiceImpl implements UserLogService {
    @NonNull
    private final BatchInsertion<AuditLog> batchInsertion = new BatchInsertion<>(
            "user_logs",
            Pair.of("user_id", AuditLog::getUserId),
            Pair.of("project_id", AuditLog::getProjectId),
            Pair.of("type", AuditLog::getType),
            Pair.of("meta", AuditLog::getMeta),
            Pair.of("description", AuditLog::getDescription)
    );

    @Autowired
    private UserLogRepository userLogRepository;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private UserService userService;

    @Override
    @Transactional
    public long addLogs(@NonNull Collection<AuditLog> requests) {
        if (requests.isEmpty()) return 0;
        return batchInsertion.buildQuery(requests).executeUpdate();
    }

    @Nullable
    private String buildMetaJson(@Nullable Object meta) {
        if (meta == null) return null;
        try {
            return mapper.writeValueAsString(meta);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
    }

    private int countByUserAndTypeIn(@NonNull UserModel user, @NonNull UserLogType... types) {
        return userLogRepository.countByUserIdAndTypeIn(user.getId(), types);
    }

    @Override
    public int countMiniprogramByUser(@NonNull UserModel user) {
        return countByUserAndTypeIn(user, UserLogType.MINIPROGRAM_PREVIEW, UserLogType.MINIPROGRAM_UPLOAD);
    }

    @Override
    public int countCompositeByUser(@NonNull UserModel user) {
        return countByUserAndTypeIn(user, UserLogType.COMPOSITE_BUILD);
    }

    @Override
    public int countWorkflowByUser(@NonNull UserModel user) {
        return countByUserAndTypeIn(
                user,
                UserLogType.WORKFLOW_COMPARE,
                UserLogType.WORKFLOW_MERGE,
                UserLogType.WORKFLOW_REBASE
        );
    }

    @Override
    @NonNull
    public List<UserLogModel> search(@NonNull UserLogSearchParams searchParams) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<UserLogEntity> query = cb.createQuery(UserLogEntity.class);
        Root<UserLogEntity> root = query.from(UserLogEntity.class);
        query.select(root);

        Path<LocalDateTime> createdAt = root.get("createdAt");

        Predicate[] predicates = Stream.of(
                        Optional.ofNullable(searchParams.getUserUniqIds())
                                .filter(i -> !i.isEmpty())
                                .map(userService::getByUniqId)
                                .orElseGet(Stream::empty)
                                .map(UserModel::getId)
                                .collect(Collectors.collectingAndThen(Collectors.toSet(), Optional::of))
                                .filter(s -> !s.isEmpty())
                                .map(ids -> root.get("userId").in(ids)),

                        Optional.ofNullable(searchParams.getProjectIds())
                                .filter(i -> !i.isEmpty())
                                .map(ids -> root.get("projectId").in(ids)),

                        Optional.ofNullable(searchParams.getTypes())
                                .map(Set::stream).orElseGet(Stream::empty).map(Enum::ordinal)
                                .collect(Collectors.collectingAndThen(Collectors.toSet(), Optional::of))
                                .filter(i -> !i.isEmpty())
                                .map(types -> root.get("type").in(types)),

                        Optional.ofNullable(searchParams.getStartTime())
                                .map(v -> cb.greaterThanOrEqualTo(createdAt, v.toLocalDateTime())),

                        Optional.ofNullable(searchParams.getEndTime())
                                .map(v -> cb.lessThan(createdAt, v.toLocalDateTime())),

                        Optional.ofNullable(searchParams.getKeyword())
                                .map(v -> cb.like(root.get("meta"), escapeLike(v)))
                )
                .map(o -> o.orElse(null))
                .filter(Objects::nonNull)
                .toArray(Predicate[]::new);

        query.where(predicates).orderBy(cb.desc(createdAt));

        List<UserLogEntity> result = entityManager.createQuery(query)
                .setFirstResult(searchParams.getSize() * searchParams.getPage())
                .setMaxResults(searchParams.getSize())
                .getResultList();

        return userService.buildListWithUserModel(result, UserLogEntity::getUserId, UserLogModel::create);
    }

    @Override
    @Transactional
    public long addLog(@NonNull Long userId, @NonNull Long projectId, @NonNull UserLogType type,
            @Nullable String description, @Nullable Object meta) {
        AuditLog log = new AuditLog(userId, projectId, type, description, meta);
        return addLogs(Collections.singleton(log));
    }

}
