package cn.huolala.van.api.model.events;

import cn.huolala.van.api.exception.InternalException;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

public class ProjectEventListener {
    private static final long TIMEOUT = 2000L;
    private static final String SEP = ":";
    @NonNull
    private final ProjectId projectId;
    @NonNull
    private final ProjectEventPolling[] polls;
    @NonNull
    private final State[] states;

    public ProjectEventListener(long projectId,
                                @Nullable String lastEventId,
                                @NonNull ProjectEventPolling[] polls) {
        this.projectId = ProjectId.singleInstance(projectId);
        this.polls = polls;
        this.states = new State[polls.length];
        Arrays.setAll(states, i -> new State());

        String[] slices = StringUtils.split(lastEventId, SEP);

        if (slices != null) { // This branch means reconnecting.
            for (int i = 0; i < polls.length && i < slices.length; i++) {
                states[i].setKey(Long.parseLong(slices[i]));
            }
        } else { // This branch means new connection.
            for (int i = 0; i < polls.length; i++) {
                ProjectEventQueue q = polls[i].getQueue(this.projectId);
                if (q != null) q.getLastStream().forEach(states[i]::touch);
            }
        }
    }

    @Nullable
    public ProjectEvent getAndWait() {
        for (; ; ) {
            for (int i = 0; i < polls.length; i++) {
                ProjectEventQueue q = polls[i].getQueue(projectId);
                if (q != null) {
                    State state = states[i];
                    ProjectEvent pe = q.getNext(state.getKey(), state::contains);
                    if (pe != null) {
                        state.touch(pe);
                        return pe;
                    }
                }
            }
            // Wait next.
            try {
                if (!projectId.await(TIMEOUT)) return null;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new InternalException(e);
            }
        }
    }

    @NonNull
    public String getLastEventId() {
        return Arrays.stream(states).map(State::getKey)
                .map(String::valueOf).collect(Collectors.joining(SEP));
    }

    private static class State {
        @Getter
        @NonNull
        private final Set<String> sent;

        @Getter
        @Setter
        private long key;

        public State() {
            key = 0L;
            sent = new HashSet<>();
        }

        public boolean contains(@NonNull ProjectEvent pe) {
            return sent.contains(pe.getHash());
        }

        public void touch(@NonNull ProjectEvent pe) {
            if (key != pe.getKey()) {
                sent.clear();
                key = pe.getKey();
            }
            sent.add(pe.getHash());
        }
    }
}
