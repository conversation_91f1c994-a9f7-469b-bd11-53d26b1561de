package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.facade.model.BuildTaskDTO;
import cn.huolala.van.api.model.ProjectAndTaskModel;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.meta.TaskSyncInfo;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.tasks.BuildTaskSearchField;
import groovy.lang.Tuple2;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Stream;

public interface BuildTaskService {
    @NonNull
    Optional<BuildTaskModel> get(@Nullable Long projectId, @Nullable Long taskId);

    @NonNull
    BuildTaskModel getNeverNull(long projectId, long taskId);

    @NonNull
    Stream<BuildTaskModel> batchGet(@Nullable Long projectId, @Nullable Set<Long> ids);

    void assertOwnership(@Nullable Set<Long> taskIds, long projectId);

    @NonNull
    Map<Long, TaskSyncInfo> findLlmSyncInfo(Set<Long> taskIds);

    @Nullable
    TaskSyncInfo findLlmSyncInfo(long taskId);

    @NonNull
    Map<String, BuildTaskModel> findLatestByBranches(long projectId, @Nullable Collection<String> branches);

    @NonNull
    List<BuildTaskModel> search(long projectId, int page, int size,
                                @NonNull Map<BuildTaskSearchField, Set<String>> conditions);

    @NonNull
    Map<BuildTaskSearchField, List<String>> findSuggestion(long projectId, @Nullable String keywords);

    @Nullable
    Long getLatestSuccessTaskIdsByProjectId(long projectId);

    @NonNull
    UserBase getFixedUser(BuildTaskModel task);

    @NonNull
    Map<Long, Long> findProjectIdsByTaskIds(@Nullable Collection<Long> taskIds);

    @NonNull
    Map<ProjectType, Integer> countEachTypes();

    int countByUser(@NonNull UserModel user);

    @NonNull
    ProjectAndTaskModel getBoth(long projectId, long taskId);

    @NonNull
    List<BuildTaskModel> listByType(long projectId, BuildTaskType type, int page, int size);

    void assertTaskMetas(@NonNull BuildTaskModel task) throws VanBadRequestException;

    @NonNull
    Stream<ProjectEvent> findBuildTaskEvents(long maxKey, int limit);

    @NonNull
    Map<Long, String> findTag(long projectId, @NonNull Set<Long> buildTaskIds);

    @Nullable
    String findTag(long projectId, @Nullable Long taskId);

    @NonNull
    List<Tuple2<Long, Long>> listTaskIds(OffsetDateTime startTime, OffsetDateTime endTime, int limit);

    long createBuildTask(@NonNull BuildTaskDTO task);

    void updateBuildTaskStatus(long projectId,
                               long taskId,
                               @NonNull BuildTaskStatus status,
                               @Nullable BuildTaskStatus prevStatus);

    void updateBuildTaskBuildId(long projectId, long taskId, @Nullable String buildId);

    void updateBuildTaskBranch(long projectId, long taskId, String branch);

    @NonNull
    Stream<BuildTaskModel> findUserRecentBuildTasks(@NonNull UserModel user, int limit);
}
