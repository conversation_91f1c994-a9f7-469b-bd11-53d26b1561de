package cn.huolala.van.api.model.storage;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Getter
public class ObjectListResult {
    @NonNull
    private final List<String> prefixes;

    @NonNull
    private final List<ObjectSummary> summaries;

    @Nullable
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String nextContinuationToken;

    public ObjectListResult() {
        prefixes = new ArrayList<>();
        summaries = new ArrayList<>();
    }

    public void merge(ObjectListResult o) {
        this.prefixes.addAll(o.prefixes);
        this.summaries.addAll(o.summaries);
        this.nextContinuationToken = o.nextContinuationToken;
    }
}
