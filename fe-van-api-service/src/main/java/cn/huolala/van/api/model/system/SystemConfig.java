package cn.huolala.van.api.model.system;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.van.api.dao.enums.Role;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class SystemConfig {

    @JsonProperty("repository")
    private String repository;

    @JsonProperty("gitUrl")
    private String gitUrl;

    @JsonProperty("pre")
    private boolean pre;

    @JsonProperty("triggerWebhook")
    private String triggerWebhook;

    @JsonProperty("buildStatusMapping")
    private Map<BuildTaskStatus, String> buildStatusMapping;

    @JsonProperty("allowedDevDomain")
    private List<String> allowedDevDomain;

    @JsonProperty("roles")
    private List<Role> roles;

    @JsonProperty("env")
    private List<SystemStableEnv> env;

    @JsonProperty("currentUser")
    private SystemUser currentUser;

}
