package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.ProjectType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class ProjectSearchParams {
    @Nullable
    private ProjectType type;
    @Nullable
    private String keyword;
    @Nullable
    private Integer page;
    @Nullable
    private Integer size;

    public int getPage(int defaultValue) {
        return page == null ? defaultValue : page;
    }

    public int getSize(int defaultValue) {
        return size == null ? defaultValue : size;
    }
}
