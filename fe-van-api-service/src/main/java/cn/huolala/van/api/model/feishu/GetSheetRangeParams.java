package cn.huolala.van.api.model.feishu;

import cn.lalaframework.soa.exception.BusinessException;
import com.google.gson.annotations.SerializedName;
import com.lark.oapi.core.annotation.Path;
import com.lark.oapi.core.annotation.Query;

public class GetSheetRangeParams {
    @Path
    @SerializedName("spreadsheetToken")
    public final String spreadsheetToken;

    @Path
    @SerializedName("range")
    public final String range;

    @Query
    @SerializedName("valueRenderOption")
    public final String valueRenderOption;

    public GetSheetRangeParams(String spreadsheetToken, String range, String valueRenderOption) {
        if (spreadsheetToken == null) throw new BusinessException("The parameter 'spreadsheetToken' is required");
        if (range == null) throw new BusinessException("The parameter 'range' is required");
        this.spreadsheetToken = spreadsheetToken;
        this.range = range;
        this.valueRenderOption = valueRenderOption;
    }

    @Override
    public String toString() {
        return String.format("%s:%s:%s", spreadsheetToken, range, valueRenderOption);
    }
}
