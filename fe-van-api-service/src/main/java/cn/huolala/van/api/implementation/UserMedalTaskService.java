package cn.huolala.van.api.implementation;

import cn.huolala.van.api.service.UserMedalCalculateService;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UserMedalTaskService {
    @Autowired
    private UserMedalCalculateService userMedalCalculateService;

    @HllXxlJob(value = "calculateUserMedal")
    public void calculateUserMedal() {
        userMedalCalculateService.calculateInBackGround();
    }

    @HllXxlJob(value = "notifyUserYesterdayMedal")
    public void notifyUserYesterdayMedal() {
        userMedalCalculateService.notifyUserYesterdayMedal();
    }
}
