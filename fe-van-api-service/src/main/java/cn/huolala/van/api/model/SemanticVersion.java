
package cn.huolala.van.api.model;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.lang.NonNull;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SemanticVersion {

    // NOTE: 基于 semver.org 官方规范的正则 [copied from GPT]
    private static final Pattern SEMVER_PATTERN = Pattern.compile(
            "^(0|[1-9]\\d*)\\." + // major
                    "(0|[1-9]\\d*)\\." + // minor
                    "(0|[1-9]\\d*)" + // patch
                    "(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?"
                    + // pre-release
                    "(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$" // build metadata
    );

    private int major;

    private int minor;

    private int patch;

    private String pre;

    private String metadata;

    private String original;

    public static boolean isValidSemver(String version) {
        return version != null && SEMVER_PATTERN.matcher(version).matches();
    }

    public static SemanticVersion create(@NonNull String version) {
        // 1.2.3-rc.1+build.123
        // major.minor.patch-pre-release+build
        if (!SemanticVersion.isValidSemver(version)) {
            throw new IllegalArgumentException("Invalid semantic version: " + version);
        }
        SemanticVersion sv = new SemanticVersion();
        Matcher matcher = SEMVER_PATTERN.matcher(version);
        if (!matcher.matches() || matcher.groupCount() < 3) {
            throw new IllegalArgumentException("Invalid semantic version: " + version);
        }
        int major = Integer.parseInt(matcher.group(1));
        int minor = Integer.parseInt(matcher.group(2));
        int patch = Integer.parseInt(matcher.group(3));
        String pre = "";
        String metadata = "";

        if (matcher.group(4) != null && !matcher.group(4).isEmpty()) {
            pre = matcher.group(4);
        }
        if (matcher.group(5) != null && !matcher.group(5).isEmpty()) {
            metadata = matcher.group(5);
        }
        sv.setMajor(major);
        sv.setMinor(minor);
        sv.setPatch(patch);
        sv.setPre(pre);
        sv.setMetadata(metadata);
        sv.setOriginal(version);
        return sv;
    }

    @Override
    public String toString() {
        String core = major + "." + minor + "." + patch;
        String prr = pre.isEmpty() ? "" : "-" + pre;
        String bm = metadata.isEmpty() ? "" : "+" + metadata;
        return core + prr + bm;
    }

    public boolean isPreRelease() {
        return !pre.isEmpty();
    }

    public SemanticVersion incMajor() {
        SemanticVersion newSv = new SemanticVersion();
        newSv.setMajor(major + 1);
        newSv.setMinor(0);
        newSv.setPatch(0);
        newSv.setPre("");
        newSv.setMetadata("");
        newSv.setOriginal(original);
        return newSv;
    }

    public SemanticVersion incMinor() {
        SemanticVersion newSv = new SemanticVersion();
        newSv.setMajor(major);
        newSv.setMinor(minor + 1);
        newSv.setPatch(0);
        newSv.setPre("");
        newSv.setMetadata("");
        newSv.setOriginal(original);
        return newSv;
    }

    public SemanticVersion incPatch() {
        SemanticVersion newSv = new SemanticVersion();
        newSv.setMajor(major);
        newSv.setMinor(minor);
        if (pre.isEmpty()) {
            newSv.setPatch(patch + 1);
        } else {
            newSv.setPatch(patch);
        }
        newSv.setPre("");
        newSv.setMetadata("");
        newSv.setOriginal(original);
        return newSv;
    }

    public SemanticVersion nextPreRelease() {
        SemanticVersion newSv = new SemanticVersion();
        newSv.setMajor(major);
        newSv.setMinor(minor);
        newSv.setPatch(patch);

        if (pre.isEmpty()) {
            // start from next patch version
            newSv.setPatch(patch + 1);
            newSv.setPre("rc.1"); // start from 1
        } else {
            String[] parts = pre.split("\\.");
            int preNum = 1; // start from 1
            String prefix = parts[0];
            if (parts.length >= 2) {
                String numStr = parts[parts.length - 1];
                try {
                    int num = Integer.parseInt(numStr);
                    preNum = num + 1;
                } catch (NumberFormatException e) {
                    // ignored, force set rc.1
                    prefix = "rc";
                }
            }
            newSv.setPre(prefix + "." + preNum);
        }
        newSv.setMetadata("");
        newSv.setOriginal(original);
        return newSv;
    }
}
