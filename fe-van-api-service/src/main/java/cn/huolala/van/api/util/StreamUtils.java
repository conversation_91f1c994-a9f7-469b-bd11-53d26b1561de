package cn.huolala.van.api.util;

import org.springframework.lang.NonNull;

import java.util.Map;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public class StreamUtils {
    private StreamUtils() {
    }

    @NonNull
    public static <T extends Map.Entry<K, U>, K, U> Collector<T, ?, Map<K, U>> toMap() {
        return Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue);
    }
}
