package cn.huolala.van.api.implementation;

import cn.huolala.van.api.component.VanUserCache;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.dao.repository.ProjectStarsRepository;
import cn.huolala.van.api.dao.repository.ProjectUserRepository;
import cn.huolala.van.api.dao.repository.UserRepository;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.Change;
import cn.huolala.van.api.model.SsoUserForSearch;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.roles.ProjectUserMeta;
import cn.huolala.van.api.model.roles.ProjectUserModel;
import cn.huolala.van.api.model.roles.UserRoleModel;
import cn.huolala.van.api.service.SsoService;
import cn.huolala.van.api.service.UserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.ToLongFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;

@Service
public class UserServiceImpl implements UserService {
    private Set<String> vanAdmin = new HashSet<>();
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private SsoService ssoService;
    @Autowired
    private ProjectUserRepository projectUserRepository;
    @Autowired
    private ProjectStarsRepository projectStarsRepository;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private VanUserCache userCache;

    @NonNull
    private static List<Object> appendProjectAndUserIds(@NonNull Long userId,
                                                        @NonNull List<Long> projectIds,
                                                        @NonNull StringBuilder sb) {
        List<Object> args = new ArrayList<>();
        for (int i = 0; i < projectIds.size(); i++) {
            sb.append(i == 0 ? "  " : "  ,");
            sb.append("(?, ?)\n");
            args.add(projectIds.get(i));
            args.add(userId);
        }
        return args;
    }

    @Nullable
    private static UserBase.Simple findOwnerFromAdmins(@NonNull List<ProjectUserModel> admins) {
        if (admins.isEmpty()) return null;
        return admins.stream()
                .filter(i -> i.checkRole(Role.AdminRole))
                .findFirst().orElseGet(() -> admins.get(0))
                .extractSimple();
    }

    @Nullable
    private UserModel getByUniqIdWithoutCache(@Nullable String uniqId) {
        return ofNullable(uniqId).map(userRepository::getByUniqId).map(UserModel::new)
                .orElse(null);
    }

    @NonNull
    private Stream<UserModel> getByUniqIdWithoutCache(@Nullable Set<String> uniqIds) {
        if (uniqIds == null || uniqIds.isEmpty()) return Stream.empty();
        return userRepository.findByUniqIds(uniqIds).stream().map(UserModel::wrap);
    }

    @Override
    @Nullable
    public UserModel getByUniqId(@Nullable String uniqId) {
        return userCache.wrap(uniqId, this::getByUniqIdWithoutCache);
    }

    @Override
    @NonNull
    public Stream<UserModel> getByUniqId(@Nullable Collection<String> uniqIds) {
        return userCache.wrap(uniqIds, this::getByUniqIdWithoutCache);
    }

    @Nullable
    private UserModel getByIdWithoutCache(@Nullable Long id) {
        return ofNullable(id).map(userRepository::getById).map(UserModel::new).orElse(null);
    }

    @Override
    @Nullable
    public UserModel getById(@Nullable Long id) {
        return userCache.wrap(id, this::getByIdWithoutCache);
    }

    @NonNull
    private Stream<UserModel> getByIdWithoutCache(@Nullable Set<Long> ids) {
        if (ids == null || ids.isEmpty()) return Stream.empty();
        return userRepository.findByIds(ids).stream().map(UserModel::wrap);
    }

    @Override
    @NonNull
    public Map<Long, UserModel> getById(@NonNull Collection<Long> ids) {
        return userCache.wrap(ids, this::getByIdWithoutCache).collect(Collectors.toMap(UserModel::getId, i -> i));
    }

    @Override
    public long getTotalUserCount() {
        return userRepository.count();
    }

    @NonNull
    @Override
    public Stream<UserBase.Simple> searchUser(@NonNull String keyword,
                                              @NonNull Long searcherId,
                                              @Nullable Long projectId,
                                              int limit) {
        return userRepository.searchUser(keyword, searcherId, projectId, limit)
                .stream().map(i -> UserBase.create(i[0], i[1]))
                .filter(Objects::nonNull);
    }

    @Override
    @Transactional
    @CanIgnoreReturnValue
    public int deleteStars(@NonNull Long userId, @NonNull List<Long> projectIds) {
        if (projectIds.isEmpty()) return 0;
        StringBuilder sb = new StringBuilder("DELETE FROM project_stars\n");
        sb.append("WHERE (project_id, user_id) IN (\n");
        List<Object> args = appendProjectAndUserIds(userId, projectIds, sb);
        sb.append(")\n");

        Query query = entityManager.createNativeQuery(sb.toString());
        for (int i = 0; i < args.size(); i++) query.setParameter(i + 1, args.get(i));

        return query.executeUpdate();
    }

    /* The UserService should end here. */
    /* The methods below should be migrated to ProjectUserService */

    @Override
    @Transactional
    @CanIgnoreReturnValue
    public int insertStars(@NonNull Long userId, @NonNull List<Long> projectIds) {
        if (projectIds.isEmpty()) return 0;
        StringBuilder sb = new StringBuilder("INSERT IGNORE INTO project_stars (project_id, user_id) VALUES\n");
        List<Object> args = appendProjectAndUserIds(userId, projectIds, sb);

        Query query = entityManager.createNativeQuery(sb.toString());
        for (int i = 0; i < args.size(); i++) query.setParameter(i + 1, args.get(i));

        return query.executeUpdate();
    }

    @Override
    @NonNull
    public List<ProjectUserModel> findByProjectIdsAndUserId(Set<Long> ids, long userId) {
        return projectUserRepository.findByProjectsAndUser(ids, userId)
                .stream().map(ProjectUserModel::fromEntity).collect(Collectors.toList());
    }

    @Override
    @NonNull
    public Set<Long> filterUserAccessibleProjectIds(@NonNull Collection<Long> ids, @NonNull UserModel user) {
        return filterUserAccessibleProjectIds(ids, user, false);
    }

    @Override
    @NonNull
    public Set<Long> filterUserAccessibleProjectIds(
            @NonNull Collection<Long> ids,
            @NonNull UserModel user,
            boolean doNotCareSuperAdmin) {
        if (ids.isEmpty()) return Collections.emptySet();
        if (!doNotCareSuperAdmin && isSuperAdmin(user.getUniqId())) return new HashSet<>(ids);
        return projectUserRepository.filterUserAccessibleProjectIds(ids, user.getId());
    }

    @NonNull
    @Override
    public Map<Long, UserBase.Simple> findProjectOwner(@Nullable Set<Long> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) return Collections.emptyMap();
        return findProjectUsers(projectIds, Role.AdminRole).collect(Collectors.groupingBy(
                ProjectUserModel::getProjectId,
                Collectors.collectingAndThen(Collectors.toList(), UserServiceImpl::findOwnerFromAdmins)
        ));
    }

    @Override
    @NonNull
    public Stream<ProjectUserModel> findProjectUsers(@NonNull Set<Long> projectIds, @NonNull Role role) {
        if (projectIds.isEmpty()) return Stream.empty();
        return projectUserRepository.findByProjectIdInAndRole(projectIds, role)
                .stream().map(ProjectUserModel::fromEntity)
                .filter(Objects::nonNull);
    }

    @NonNull
    @Override
    public Stream<ProjectUserModel> findProjectUsers(@Nullable Long projectId) {
        if (projectId == null) return Stream.empty();
        return projectUserRepository.findByProjectIdOrderByRoleDesc(projectId)
                .stream().map(ProjectUserModel::fromEntity)
                .filter(Objects::nonNull);
    }

    @Override
    @NonNull
    public Stream<ProjectUserModel> findProjectUsers(@NonNull Set<Long> projectIds) {
        if (projectIds.isEmpty()) return Stream.empty();
        return projectUserRepository.findByProjectIdInOrderByRoleDesc(projectIds)
                .stream().map(ProjectUserModel::fromEntity)
                .filter(Objects::nonNull);
    }

    @Override
    @NonNull
    public List<UserRoleModel> findRoles(@Nullable Long projectId) {
        return findProjectUsers(projectId).collect(UserRoleModel.collectToList());
    }

    @Override
    @NonNull
    public Map<Long, List<UserRoleModel>> findRoles(Set<Long> ids) {
        return findProjectUsers(ids).collect(Collectors.groupingBy(
                ProjectUserModel::getProjectId,
                UserRoleModel.collectToList()
        ));
    }

    @Nullable
    private UserModel pullUserFromSso(@Nullable String uniqId) {
        if (uniqId == null) return null;
        SsoUserForSearch user = ssoService.getUserByAccount(uniqId);
        if (user == null || user.getAccount() == null) return null;
        userRepository.insertOrUpdate(
                user.getAccount(),
                user.getUserName(),
                user.getAccount() + "@huolala.cn"
        );
        return getByUniqId(uniqId);
    }

    @Override
    @NonNull
    @Transactional
    public Map<String, UserModel> pullUserFromSsoIfNeeded(Collection<String> uniqIds) {
        Map<String, UserModel> map = getByUniqId(uniqIds).collect(Collectors.toMap(UserModel::getUniqId, i -> i));
        uniqIds.parallelStream().filter(i -> !map.containsKey(i)).map(this::pullUserFromSso)
                // Convert it to a sequential stream to avoid the hash map being written on different threads.
                .collect(Collectors.toList()).stream()
                .filter(Objects::nonNull).forEach(i -> map.put(i.getUniqId(), i));
        return map;
    }

    @Override
    @Nullable
    @Transactional
    public UserModel pullUserFromSsoIfNeeded(@Nullable String uniqId) {
        UserModel user = getByUniqId(uniqId);
        if (user != null) return user;
        return pullUserFromSso(uniqId);
    }


    @Override
    @Transactional
    public int batchUpdateProjectUsers(@NonNull Long projectId, @NonNull Map<Long, ProjectUserMeta> data) {
        if (data.isEmpty()) return 0;
        Object[] parameters = data.entrySet().stream().flatMap(i -> {
            try {
                long userId = i.getKey();
                ProjectUserMeta pum = i.getValue();
                String meta = mapper.writeValueAsString(pum);
                int role = pum.evaluateMaxRole().ordinal();
                String uniqStr = String.format("%d:%d", userId, projectId);
                return Stream.of(uniqStr, projectId, userId, role, meta);
            } catch (JsonProcessingException e) {
                throw new InternalMappingException(e);
            }
        }).toArray();

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO project_users (uniq_str, project_id, user_id, role, meta) VALUES ");
        for (int i = 0; i < parameters.length; i += 5) {
            if (i > 0) sql.append(", ");
            sql.append("(?, ?, ?, ?, ?)");
        }
        sql.append(" ON DUPLICATE KEY UPDATE meta = VALUES(meta), role = GREATEST(role, VALUES(role))");

        Query query = entityManager.createNativeQuery(sql.toString());
        for (int i = 0; i < parameters.length; i++) query.setParameter(i + 1, parameters[i]);
        return query.executeUpdate();
    }

    @Override
    @Transactional
    @NonNull
    @CanIgnoreReturnValue
    public Change<Role> updateProjectUser(@NonNull Long projectId, @NonNull Long userId, @NonNull Role role) {
        Optional<ProjectUserModel> current = projectUserRepository
                .getByProjectAndUserForUpdate(projectId, userId)
                .map(ProjectUserModel::fromEntity);

        Role before = current.map(ProjectUserModel::getRole).orElse(null);

        if (before == role) throw new VanBadRequestException("No change");

        Role loneRole = current.map(i -> i.getMeta().evaluateMaxRole()).orElse(Role.NoRole);

        if (role.ordinal() < loneRole.ordinal()) role = loneRole;

        projectUserRepository.insertOrUpdate(
                projectId,
                userId,
                role.ordinal()
        );

        return new Change<>(before, role);
    }

    @Override
    @NonNull
    public List<Long> findStaredProjectIdListByUserId(@NonNull Long userId) {
        return projectStarsRepository.listStaredProjectIdListByUserId(userId);
    }

    @Override
    public long removeProjectUser(Long projectId, Long userId) {
        return projectUserRepository.deleteByProjectIdAndUserId(projectId, userId);
    }

    @Value("${van.admin}")
    public void setVanAdmin(String emailList) {
        if (StringUtils.isBlank(emailList)) return;
        this.vanAdmin = Arrays.stream(emailList.split("\n")).map(String::trim).map(i -> {
            int at = i.indexOf('@');
            return at == -1 ? i : i.substring(0, at);
        }).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
    }

    @Override
    public boolean isSuperAdmin(String userUniqId) {
//        return false;
        return this.vanAdmin.contains(userUniqId);
    }

    @Override
    public boolean isParticipant(@Nullable UserModel user, @Nullable Long projectId) {
        if (user == null || projectId == null) return false;
        if (isSuperAdmin(user.getUniqId())) return true;
        Integer maxRole = projectUserRepository.getRole(user.getId(), projectId);
        return maxRole != null && maxRole > 0;
    }

    @Override
    public boolean isAdministrator(@Nullable UserModel user, @Nullable Long projectId) {
        if (user == null || projectId == null) return false;
        if (isSuperAdmin(user.getUniqId())) return true;
        Integer maxRole = projectUserRepository.getRole(user.getId(), projectId);
        return maxRole != null && maxRole >= Role.AdminRole.ordinal();
    }

    @NonNull
    @Override
    public <E, M> List<M> buildListWithUserModel(@NonNull Collection<E> entities,
                                                 @NonNull ToLongFunction<E> getUserId,
                                                 @NonNull BiFunction<E, UserModel, M> toModel) {
        Map<Long, UserModel> userMap = entities.stream().map(getUserId::applyAsLong)
                .collect(Collectors.collectingAndThen(Collectors.toSet(), this::getById));
        return entities.stream()
                .map(entity -> toModel.apply(entity, userMap.get(getUserId.applyAsLong(entity))))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    @NonNull
    @Override
    public <E, R> List<R> buildListWithUserModel(@NonNull Collection<E> entities,
                                                 @NonNull Function<E, String> getUserUniqId,
                                                 @NonNull BiFunction<E, UserModel, R> toModel) {
        return buildStreamWithUserModel(entities, getUserUniqId, toModel).collect(Collectors.toList());
    }

    @NonNull
    @Override
    public <E, R> Stream<R> buildStreamWithUserModel(@NonNull Collection<E> entities,
                                                     @NonNull Function<E, String> getUserUniqId,
                                                     @NonNull BiFunction<E, UserModel, R> toModel) {
        Map<String, UserModel> userMap = entities.stream().map(getUserUniqId)
                .collect(Collectors.collectingAndThen(Collectors.toSet(), this::getByUniqId))
                .collect(Collectors.toMap(UserModel::getUniqId, i -> i));
        return entities.stream()
                .map(entity -> toModel.apply(entity, userMap.get(getUserUniqId.apply(entity))))
                .filter(Objects::nonNull);
    }
}
