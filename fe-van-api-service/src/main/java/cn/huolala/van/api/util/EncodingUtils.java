package cn.huolala.van.api.util;

import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.jetbrains.annotations.Contract;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import cn.huolala.van.api.exception.InternalException;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class EncodingUtils {

    private EncodingUtils() {
    }

    public static boolean containsUtf8mb4(@NonNull String kw) {
        return kw.matches("[^\\u0000-\\uFFFF]");
    }

    @Nullable
    @Contract(value = "!null->!null", pure = true)
    public static String escapeLike(String raw) {
        if (raw == null) return null;
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < raw.length(); i++) {
            char c = raw.charAt(i);
            if (c == '\\' || c == '_' || c == '%') sb.append('\\');
            sb.append(c);
        }
        return sb.toString();
    }

    @NonNull
    @SuppressWarnings("java:S127")
    public static String encodeGolangJsonWithoutQuotes(@NonNull String raw) {
        StringBuilder sb = new StringBuilder();
        int length = raw.length();
        for (int i = 0; i < length; i++) {
            int p = raw.codePointAt(i);
            if (Character.isSupplementaryCodePoint(p)) {
                sb.append(String.format("\\U%08X", p));
                i++;
            } else if (p > 255) {
                sb.append(String.format("\\u%04x", p));
            } else {
                switch (p) {
                    case '\b':
                        sb.append("\\b");
                        break;
                    case '\f':
                        sb.append("\\f");
                        break;
                    case '\n':
                        sb.append("\\n");
                        break;
                    case '\r':
                        sb.append("\\r");
                        break;
                    case '\t':
                        sb.append("\\t");
                        break;
                    case '"':
                    case '\\':
                    case '/':
                        sb.append("\\");
                        sb.append((char) p);
                        break;
                    default:
                        sb.append((char) p);
                }
            }
        }
        return sb.toString();
    }

    @NonNull
    public static String encodeGolangJson(@NonNull String raw) {
        return String.format("\"%s\"", encodeGolangJsonWithoutQuotes(raw));
    }

    @Nullable
    public static String takeoffParentheses(@NonNull String str) {
        int l = str.indexOf('(');
        if (l < 0) return null;
        int r = str.lastIndexOf(')');
        if (r < 0 || l + 1 > r) return null;
        return str.substring(l + 1, r);
    }

    @Nullable
    public static String trimLeftAndRight(@NonNull String str, @NonNull String left, @NonNull String right) {
        if (!str.startsWith(left) || !str.endsWith(right)) return null;
        return str.substring(left.length(), str.length() - right.length());
    }

    /**
     * Convert parameters to a standard query string.
     * NOTE: If parameters are null or empty,
     *       return an empty string without leading '?' which is also a valid query string.
     *       Otherwise, return a query string which is leading with '?' character.
     */
    @NonNull
    public static String buildQueryString(@Nullable Iterable<? extends NameValuePair> parameters) {
        if (parameters == null) return "";
        String qs = URLEncodedUtils.format(parameters, StandardCharsets.UTF_8);
        if (qs.isEmpty()) return qs;
        return "?" + qs;
    }

    @NonNull
    public static byte[] shaBytes(@NonNull byte[] data, @NonNull String algorithm) {
        try {
            return MessageDigest.getInstance(algorithm).digest(data);
        } catch (NoSuchAlgorithmException e) {
            throw new InternalException(e);
        }
    }

    @NonNull
    public static String shaHexString(@NonNull byte[] data, @NonNull String algorithm) {
        byte[] result = shaBytes(data, algorithm);
        StringBuilder sb = new StringBuilder();
        for (byte b : result) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

}
