package cn.huolala.van.api.implementation;

import java.io.IOException;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.util.function.Function;
import cn.huolala.van.api.model.MiniProgramDetail.FieldUpperDetail;
import cn.huolala.van.api.model.elatic.ApiData;
import cn.huolala.van.api.model.elatic.PageViewInfo;
import cn.huolala.van.api.model.elatic.FieldData;
import cn.huolala.van.api.model.elatic.FieldUpperData;
import cn.huolala.van.api.model.elatic.HostUpperData;
import cn.huolala.van.api.model.elatic.MetricData;
import cn.huolala.van.api.model.elatic.RouteUpperData;
import cn.huolala.van.api.model.elatic.TimeRange;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.ElasticsearchException;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation;
import co.elastic.clients.elasticsearch._types.aggregations.CompositeAggregation;
import co.elastic.clients.elasticsearch._types.aggregations.CompositeAggregationSource;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.jackson.JacksonJsonpGenerator;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.util.ObjectBuilder;
import groovy.util.logging.Log4j2;

@Service
@Log4j2
public class ElaticSearchServiceImpl {
	private static final int PAGE_SIZE = 1000;
	private static final int MAX_REQUEST = 10;

	@Autowired
	private ElasticsearchClient client;

	private String getIndex(String projecName) {
		if (projecName.equals("light-app")) {
			return "prod-watch-dog-light-app-*";
		}

		return "prod-watch-dog-others-*";
	}

	public MetricData getProjectMetricData(String projectName, TimeRange timeRange)
			throws ElasticsearchException, IOException {
		SearchRequest request = SearchRequest.of(r -> r
				.index(getIndex(projectName))
				.query(q -> q
						.bool(b -> b
								.filter(f -> f
										.range(rg -> rg
												.field("date")
												.gte(JsonData.of(timeRange.getGte()))
												.lte(JsonData.of(timeRange.getLte()))
												.format("strict_date_optional_time")))
								.must(m -> m
										.term(t -> t
												.field("type")
												.value(FieldValue.of("metrics"))))
								.must(m -> m
										.term(t -> t
												.field("project")
												.value(FieldValue.of(projectName))))))
				.aggregations("ttfb_percentile", a -> a
						.percentiles(p -> p
								.field("data.ttfb")
								.percents(90.0)))
				.aggregations("fcp_percentile", a -> a
						.percentiles(p -> p
								.field("data.fcp")
								.percents(90.0)))
				.aggregations("lcp_percentile", a -> a
						.percentiles(p -> p
								.field("data.lcp")
								.percents(90.0)))
				.aggregations("first_interaction_time_percentile", a -> a
						.percentiles(p -> p
								.field("data.firstInteractionTime")
								.percents(90.0)))
				.aggregations("page_first_render_time_percentile", a -> a
						.percentiles(p -> p
								.field("data.pageFirstRenderTime")
								.percents(90.0)))
				.aggregations("first_paint_percentile", a -> a
						.percentiles(p -> p
								.field("data.firstPaint")
								.percents(90.0)))
				.size(0));
		SearchResponse<Void> response = client.search(request, Void.class);
		MetricData data = new MetricData();
		String ttfbString = response.aggregations()
				.get("ttfb_percentile").tdigestPercentiles().values().keyed().get("90.0");

		String lcpString = response.aggregations()
				.get("lcp_percentile").tdigestPercentiles().values().keyed().get("90.0");

		String fcpString = response.aggregations()
				.get("fcp_percentile").tdigestPercentiles().values().keyed().get("90.0");

		String firstInteractionTimeString = response.aggregations()
				.get("first_interaction_time_percentile").tdigestPercentiles().values().keyed().get("90.0");

		String pageFirstRenderTimeString = response.aggregations()
				.get("page_first_render_time_percentile").tdigestPercentiles().values().keyed().get("90.0");

		String firstPaintString = response.aggregations()
				.get("first_paint_percentile").tdigestPercentiles().values().keyed().get("90.0");

		if (lcpString != null && Double.parseDouble(lcpString) > 0) {
			data.setLcpUpperTime(subK(Double.parseDouble(lcpString)));
		}

		if (ttfbString != null && Double.parseDouble(ttfbString) > 0) {
			data.setTtfbUpperTime(subK(Double.parseDouble(ttfbString)));
		}
		if (fcpString != null && Double.parseDouble(fcpString) > 0) {
			data.setFcpUpperTime(subK(Double.parseDouble(fcpString)));
		}
		if (firstInteractionTimeString != null && Double.parseDouble(firstInteractionTimeString) > 0) {
			data.setFirstInteractionTimeUpperTime(subK(Double.parseDouble(firstInteractionTimeString)));
		}
		if (pageFirstRenderTimeString != null && Double.parseDouble(pageFirstRenderTimeString) > 0) {
			data.setPageFirstRenderTimeUpperTime(subK(Double.parseDouble(pageFirstRenderTimeString)));
		}
		if (firstPaintString != null && Double.parseDouble(firstPaintString) > 0) {
			data.setFirstPaintUpperTime(subK(Double.parseDouble(firstPaintString)));
		}

		return data;

	}

	public void printEsBySearchRequest(SearchRequest searchRequest) throws IOException {
		JacksonJsonpMapper jsonpMapper = new JacksonJsonpMapper();
		jsonpMapper.objectMapper().registerModule(new JavaTimeModule());
		jsonpMapper.objectMapper().setDateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"));

		StringWriter writer = new StringWriter();
		JacksonJsonpGenerator generator = new JacksonJsonpGenerator(new JsonFactory().createGenerator(writer));
		searchRequest.serialize(generator, jsonpMapper);
		generator.flush();
		String text = writer.toString();
		System.out.println(text);
	}

	public FieldUpperDetail getFieldDurationPercentileByType(String projectName, String type, String field,
			TimeRange timeRange)
			throws ElasticsearchException, IOException {
		return getFieldPercentileByType(projectName, type, field, "data.duration", timeRange);
	}

	public FieldUpperDetail getFieldPercentileByType(String projectName, String type, String field,
			String upperField,
			TimeRange timeRange)
			throws ElasticsearchException, IOException {
		Map<String, SortOrder> countOrder = new HashMap<>();
		countOrder.put("_count", SortOrder.Desc);

		@SuppressWarnings("unchecked")
		SearchRequest searchRequest = SearchRequest.of(s -> s
				.index(getIndex(projectName))
				.size(0)
				.query(q -> q
						.bool(b -> b
								.filter(f -> f
										.range(r -> r
												.field("date")
												.gte(JsonData.of(timeRange.getGte()))
												.lte(JsonData.of(timeRange.getLte()))
												.format("strict_date_optional_time")))
								.must(m -> m
										.match(t -> t
												.field("project")
												.query(FieldValue.of(projectName))))
								.must(m -> m
										.match(t -> t
												.field("type")
												.query(FieldValue.of(type))))
								.must(m -> m
										.match(t -> t
												.field("level")
												.query(FieldValue.of("info"))))
								.must(m -> m
										.match(t -> t
												.field("env")
												.query(FieldValue.of("prod"))))))
				.aggregations("percentile", b2 -> b2
						.percentiles(p -> p
								.field(upperField)
								.percents(90.0)))
				.aggregations("field_agg", a -> a
						.terms(t -> t.field(field).size(5).order(countOrder))
						.aggregations("field_percentile", a2 -> a2
								.percentiles(p -> p
										.field(upperField)
										.percents(90.0)))));

		SearchResponse<Object> response = client.search(searchRequest, Object.class);

		FieldUpperDetail fieldInfo = new FieldUpperDetail();

		List<FieldUpperData> upperTimeDetail = new ArrayList<>();
		String totalUpperString = response.aggregations().get("percentile").tdigestPercentiles().values()
				.keyed().get("90.0");
		if (totalUpperString == null) {
			return null;
		}

		StringTermsAggregate fieldAgg = response.aggregations().get("field_agg").sterms();
		for (StringTermsBucket fieldBucket : fieldAgg.buckets().array()) {
			String host = fieldBucket.key().toString();
			Long count = fieldBucket.docCount();
			String upperString = fieldBucket.aggregations().get("field_percentile").tdigestPercentiles()
					.values()
					.keyed().get("90.0");
			if (upperString != null) {
				upperTimeDetail.add(new FieldUpperData(host, count, upperString));
			}
		}

		fieldInfo.setUpperValue(subK(Double.parseDouble(totalUpperString)));
		fieldInfo.setUpperDetail(upperTimeDetail);
		return fieldInfo;
	}

	private Double subK(Double ms) {
		return ms / 1000.0;
	}

	public ApiData getProjectNetworkData(String projectName, String type, TimeRange timeRange)
			throws ElasticsearchException, IOException {
		return this.getProjectNetworkDataByQuery(projectName, q -> q
				.bool(b -> b
						.filter(f -> f
								.range(r -> r
										.field("date")
										.gte(JsonData.of(timeRange.getGte()))
										.lte(JsonData.of(timeRange.getLte()))
										.format("strict_date_optional_time")))
						.must(m -> m
								.match(t -> t
										.field("project")
										.query(FieldValue.of(projectName))))
						.must(m -> m
								.match(t -> t
										.field("type")
										.query(FieldValue.of(type))))
						.must(m -> m
								.match(t -> t
										.field("level")
										.query(FieldValue.of("info"))))
						.mustNot(m -> m
								.match(t -> t
										.field("data.resourceType")
										.query(FieldValue.of("unknown"))))
						.mustNot(m -> m
								.match(t -> t
										.field("data.resourceType")
										.query(FieldValue.of("video"))))
						.must(m -> m
								.match(t -> t
										.field("env")
										.query(FieldValue.of("prod"))))));
	}

	public ApiData getFailProjectNetworkData(String projectName, String type, TimeRange timeRange)
			throws ElasticsearchException, IOException {
		return this.getProjectNetworkDataByQuery(projectName, q -> q
				.bool(b -> b
						.filter(f -> f
								.range(r -> r
										.field("date")
										.gte(JsonData.of(timeRange.getGte()))
										.lte(JsonData.of(timeRange.getLte()))
										.format("strict_date_optional_time")))
						.must(m -> m
								.match(t -> t
										.field("project")
										.query(FieldValue.of(projectName))))
						.must(m -> m
								.match(t -> t
										.field("type")
										.query(FieldValue.of(type))))
						.must(m -> m
								.match(t -> t
										.field("level")
										.query(FieldValue.of("info"))))
						.mustNot(m -> m
								.match(t -> t
										.field("data.resourceType")
										.query(FieldValue.of("unknown"))))
						.mustNot(m -> m
								.match(t -> t
										.field("data.resourceType")
										.query(FieldValue.of("video"))))
						.mustNot(m -> m
								.term(t -> t
										.field("data.httpCode")
										.value(FieldValue.of(200))))
						.must(m -> m
								.match(t -> t
										.field("env")
										.query(FieldValue.of("prod"))))));
	}

	public ApiData getProjectNetworkDataByQuery(String projectName,
			Function<Query.Builder, ObjectBuilder<Query>> queryFn) throws ElasticsearchException, IOException {
		Map<String, SortOrder> countOrder = new HashMap<>();
		countOrder.put("_count", SortOrder.Desc);

		@SuppressWarnings("unchecked")
		SearchRequest searchRequest = SearchRequest.of(s -> s
				.index(getIndex(projectName))
				.size(0)
				.query(queryFn)
				.aggregations("total", agg -> agg
						.valueCount(t -> t.field("clientId")))
				.aggregations("duration_percentile", b2 -> b2
						.percentiles(p -> p
								.field("data.duration")
								.percents(90.0)))
				.aggregations("host_agg", a -> a
						.terms(t -> t.field("data.url.host").size(5).order(countOrder))
						.aggregations("host_duration_percentile", a2 -> a2
								.percentiles(p -> p
										.field("data.duration")
										.percents(90.0)))
						.aggregations("route_agg", a2 -> a2
								.terms(t -> t.field("data.url.route").size(30).order(countOrder))
								.aggregations("route_duration_percentile",
										a3 -> a3.percentiles(p -> p.field("data.duration").percents(90.0))))));

		SearchResponse<Object> response = client.search(searchRequest, Object.class);

		ApiData apiData = new ApiData();
		List<HostUpperData> hostInfo = new ArrayList<>();
		List<RouteUpperData> routeInfo = new ArrayList<>();
		String durationUpperTimeString = response.aggregations().get("duration_percentile").tdigestPercentiles()
				.values()
				.keyed().get("90.0");
		if (durationUpperTimeString == null) {
			return null;
		}
		double total = response.aggregations().get("total").valueCount().value();
		apiData.setTotal((long) total);
		apiData.setDurationUpperTime(subK(Double.parseDouble(durationUpperTimeString)));
		StringTermsAggregate hostAgg = response.aggregations().get("host_agg").sterms();
		for (StringTermsBucket hostBucket : hostAgg.buckets().array()) {
			String host = hostBucket.key().toString();
			Long count = hostBucket.docCount();
			String upperString = hostBucket.aggregations().get("host_duration_percentile").tdigestPercentiles().values()
					.keyed().get("90.0");
			hostInfo.add(new HostUpperData(host, count, upperString));
			StringTermsAggregate routeAgg = hostBucket.aggregations().get("route_agg").sterms();
			for (StringTermsBucket routeBucket : routeAgg.buckets().array()) {
				String route = routeBucket.key().toString();
				Long routeCount = routeBucket.docCount();
				String routeUpperString = routeBucket.aggregations().get("route_duration_percentile")
						.tdigestPercentiles().values().keyed().get("90.0");
				routeInfo.add(new RouteUpperData(host, route, routeCount, routeUpperString));
			}
		}
		apiData.setHostUpperDetail(hostInfo);
		apiData.setRouteUpperTimeDetail(routeInfo);
		return apiData;
	}

	public PageViewInfo getProjectViewInfo(String projectName, TimeRange timeRange)
			throws ElasticsearchException, IOException {
		SearchRequest searchRequest = new SearchRequest.Builder()
				.index(getIndex(projectName))
				.size(0)
				.query(q -> q
						.bool(b -> b
								.filter(f -> f
										.range(r -> r
												.field("date")
												.gte(JsonData.of(timeRange.getGte()))
												.lte(JsonData.of(timeRange.getLte()))
												.format("strict_date_optional_time")))
								.must(m -> m
										.term(t -> t.field("type").value(FieldValue.of("pageview"))))
								.must(m -> m
										.term(t -> t.field("project").value(FieldValue.of(projectName))))
								.must(m -> m
										.term(t -> t.field("env").value(FieldValue.of("prod"))))))
				.aggregations("uv", agg -> agg
						.cardinality(t -> t.field("clientId")))
				.aggregations("pv", agg -> agg
						.valueCount(t -> t.field("clientId")))
				.aggregations("system_agg", agg -> agg
						.terms(t -> t.field("appInfo.system.keyword").size(10)))
				.aggregations("app_agg", agg -> agg
						.terms(t -> t.field("appInfo.app.keyword").size(10)))
				.build();
		SearchResponse<JsonData> response = client.search(searchRequest, JsonData.class);

		List<FieldData> systemInfo = new ArrayList<>();
		response.aggregations().get("system_agg").sterms().buckets().array().forEach(bucket -> {
			systemInfo.add(new FieldData(bucket.key(), bucket.docCount()));
		});

		List<FieldData> appInfo = new ArrayList<>();
		response.aggregations().get("app_agg").sterms().buckets().array().forEach(bucket -> {
			appInfo.add(new FieldData(bucket.key(), bucket.docCount()));
		});

		double pv = response.aggregations().get("pv").valueCount().value();
		double uv = response.aggregations().get("uv").cardinality().value();
		PageViewInfo info = new PageViewInfo();
		info.setPv((long) pv);
		info.setUv((long) uv);
		info.setAppInfo(appInfo);
		info.setSystemInfo(systemInfo);
		return info;
	}

	public Map<String, Long> getAllProject(TimeRange timeRange) throws IOException {
		Map<String, Long> result = new HashMap<>();
		Map<String, String> after = null;

		for (int i = 0; i < MAX_REQUEST; i++) {
			Map<String, CompositeAggregationSource> sources = new HashMap<>();
			sources.put("project", CompositeAggregationSource.of(source -> source
					.terms(t -> t.field("project"))));

			@SuppressWarnings("unchecked")
			CompositeAggregation.Builder compositeBuilder = new CompositeAggregation.Builder()
					.sources(sources)
					.size(PAGE_SIZE);

			if (after != null) {
				compositeBuilder.after(after);
			}

			Aggregation compositeAggregation = new Aggregation.Builder().composite(compositeBuilder.build()).build();

			SearchRequest searchRequest = SearchRequest.of(s -> s
					.index("prod-watch-dog-*")
					.size(0)
					.query(q -> q
							.bool(b -> b
									.filter(f -> f
											.range(r -> r
													.field("date")
													.gte(JsonData.of(timeRange.getGte()))
													.lte(JsonData.of(timeRange.getLte()))
													.format("strict_date_optional_time")))
									.must(m -> m
											.match(t -> t
													.field("env")
													.query(FieldValue.of("prod"))))))

					.aggregations("composite_agg", compositeAggregation));

			SearchResponse<Object> response = client.search(searchRequest, Object.class);
			response.aggregations()
					.get("composite_agg")
					.composite()
					.buckets().array().forEach(bucket -> {
						String value = bucket.key().get("project").to(String.class);
						long docCount = bucket.docCount();
						result.put(value, docCount);
					});

			Map<String, JsonData> afterKey = response.aggregations()
					.get("composite_agg")
					.composite()
					.afterKey();
			if (afterKey != null) {
				Map<String, String> tempAfter = new HashMap<>();
				afterKey.forEach((key, value) -> tempAfter.put(key, value.toString()));
				after = tempAfter;
			}
		}

		return result;
	}

}
