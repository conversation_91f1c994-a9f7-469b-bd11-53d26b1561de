package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.dao.entity.BuildTaskEntity;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.dao.repository.BuildTaskRepository;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.facade.model.BuildTaskDTO;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.feishu.FeishuActionValue.ButtonType;
import cn.huolala.van.api.model.feishu.FeishuActionValue.PerformDeploy;
import cn.huolala.van.api.model.feishu.FeishuActionValue.PerformMiniprogramPreview;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.model.feishu.FeishuCard.Confirm;
import cn.huolala.van.api.model.feishu.FeishuCard.Element;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.*;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.OverflowAction.OverflowOption;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.SelectStaticElement.SelectStaticOption;
import cn.huolala.van.api.model.feishu.FeishuCard.TagContent;
import cn.huolala.van.api.model.meta.TaskSyncInfo;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.tasks.BuildTaskSearchField;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.EncodingUtils;
import cn.huolala.van.api.util.EnumUtils;
import cn.huolala.van.api.util.FeishuUtils;
import cn.huolala.van.api.util.VanUtils;
import cn.lalaframework.logging.LoggerFactory;
import cn.lalaframework.spring.ApplicationContextUtil;
import cn.lalaframework.storage.adapter.Storage;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import groovy.lang.Tuple2;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static cn.huolala.api.constants.enums.BuildTaskType.NormalBuild;
import static cn.huolala.api.constants.enums.Env.pre;
import static cn.huolala.api.constants.enums.Env.stg;
import static cn.huolala.api.constants.enums.MetaType.*;
import static cn.huolala.van.api.util.EncodingUtils.*;
import static cn.huolala.van.api.util.FeishuUtils.*;
import static java.lang.String.format;
import static java.time.Instant.ofEpochSecond;

@Service
@Log4j2
public class BuildTaskServiceImpl implements BuildTaskService {

    private static final Logger LOGGER = LoggerFactory.getLogger();

    @Autowired
    private BuildTaskRepository buildTaskRepository;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private UserService userService;
    @Autowired
    private TaskResourceService taskResourceService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private MetaService metaService;
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private MiniprogramService miniprogramService;

    private static String statusToMd(BuildTaskModel task) {
        BuildTaskStatus status = task.getStatus();
        switch (status) {
            case Ready:
                return "⏳ 等待中";
            case Running:
                return "🚚 构建中";
            case Failed:
                return "❌ 失败";
            case Timeout:
                return "❌ 超时";
            case Cancel:
                return "⛔ 取消";
            case Done:
                long duration = task.getUpdatedAt().toEpochSecond() - task.getCreatedAt().toEpochSecond();
                StringBuilder sb = new StringBuilder("✅ 完成");
                sb.append("（耗时 ");
                sb.append(duration);
                sb.append("s");
                if (duration > 180) sb.append("，\uD83D\uDC4E 贼慢");
                sb.append("）");
                return sb.toString();
            default:
                return "❓ " + status;
        }
    }

    private static boolean supportsFastDeploy(@NonNull ProjectModel project) {
        ProjectType type = project.getType();
        // Only the Web and Workers projects support the fast deploy.
        return type == ProjectType.Web || type == ProjectType.Workers || type == ProjectType.Miniprogram;
    }

    private void appendNormalFastDeployButtons(@NonNull ProjectModel project,
            @NonNull BuildTaskModel task,
            @NonNull UserModel user,
            @NonNull FeishuCard card) {
        int roleBitmap = userService.computeRoleBitmap(user, project.getId());
        if (roleBitmap > 0) {
            new HrElement().appendTo(card);

            new DivElement().addField(new Field("**快捷发布**")).appendTo(card);

            ActionElement btnGroup = new ActionElement().appendTo(card);

            new PerformDeploy(project.getId(), task.getId(), new ConfigEnv(stg, 0)).sign().wrapAsButton(stg.name())
                    .confirm(new Confirm("操作确认", format("将 #%d 发布到 %s 吗？", task.getId(), stg))).appendTo(btnGroup);

            new PerformDeploy(project.getId(), task.getId(), new ConfigEnv(pre, 0)).sign().wrapAsButton(pre.name())
                    .confirm(new Confirm("操作确认", format("将 #%d 发布到 %s 吗？", task.getId(), pre)))
                    .disable((roleBitmap & Role.TestRole.bitValue) == 0 ? "仅测试角色可发 pre" : null).appendTo(btnGroup);

            OverflowAction oa = new PerformDeploy(project.getId(), task.getId()).sign().wrapAsOverflowAction()
                    .appendTo(btnGroup);
            IntStream.rangeClosed(1, 4).mapToObj(i -> new ConfigEnv(stg, i).toString())
                    .map(v -> new OverflowOption(FeishuCard.TagContent.txt(v), v)).forEach(oa::addOption);
            IntStream.rangeClosed(1, 4).mapToObj(i -> new ConfigEnv(pre, i).toString())
                    .map(v -> new OverflowOption(FeishuCard.TagContent.txt(v), v)).forEach(oa::addOption);
        }
    }

    private void appendMiniprogramFastDeployButtons(@NonNull ProjectModel project,
            @NonNull BuildTaskModel task,
            @NonNull UserModel user,
            @NonNull FeishuCard card) {
        // preview
        new DivElement().addField(new Field("**快捷预览**")).appendTo(card);
        ActionElement btnGroup = new ActionElement().appendTo(card);
        String version = String.format("0.0.%s", task.getId());
        String description = String.format("#%s", task.getId());
        String deployType = "preview";
        IntStream.rangeClosed(1, 5).forEach(r -> {
            String n = String.valueOf(r);
            new PerformMiniprogramPreview(project.getId(), task.getId(), n, deployType, version,
                    description).wrapAsButton(String.format("bot-%d", r)).appendTo(btnGroup);
        });
        // add hr
        new HrElement().appendTo(card);
        // upload
        new DivElement().addField(new Field("**快捷上传**")).appendTo(card);

        // NOTE: here we use feishu Form component to submit, so we don't use ActionElement and Action
        // TODO: add version tip depends on latest upload verison
        List<Element<?>> elements = new ArrayList<>();
        InputElement versionInput = new InputElement();
        versionInput.setInputType("text");
        versionInput.setName("version_field");
        versionInput.setRequired(true);
        versionInput.setPlaceholder(TagContent.txt("请输入版本号"));
        elements.add(versionInput);

        InputElement descriptionInput = new InputElement();
        descriptionInput.setInputType("multiline_text");
        descriptionInput.setName("description_field");
        descriptionInput.setRequired(false);
        descriptionInput.setRows(2);
        descriptionInput.setPlaceholder(TagContent.txt("请输入描述"));
        elements.add(descriptionInput);

        SelectStaticElement botSelect = new SelectStaticElement();
        botSelect.setName("bot_field");
        botSelect.setRequired(true);
        botSelect.setPlaceholder(TagContent.txt("请选择bot"));
        IntStream.rangeClosed(1, 5).forEach(r -> {
            String n = String.valueOf(r);
            botSelect.addOption(new SelectStaticOption("bot-" + n, n));
        });
        elements.add(botSelect);
        // button
        ButtonElement submit = new ButtonElement();
        submit.setName("feishu_upload_miniprogram_button");
        submit.setActionType("form_submit");
        submit.setText(TagContent.txt("提交"));
        submit.setType(ButtonType.PRIMARY);
        Map<String, String> valMap = new HashMap<>();
        valMap.put("project_id", String.valueOf(project.getId()));
        valMap.put("task_id", String.valueOf(task.getId()));
        submit.setValue(valMap);
        elements.add(submit);

        FormElement form = new FormElement();
        form.setName("miniprogram_upload_form");
        form.setElements(elements);
        form.appendTo(card);
    }

    @NonNull
    private FeishuCard buildFeishuCard(@NonNull BuildTaskModel task,
                                       @NonNull ProjectModel project,
                                       @NonNull UserModel user) {
        BuildTaskStatus status = task.getStatus();

        FeishuCard card = FeishuUtils.buildNotificationCard("构建", project.getName());

        new DivElement().addFields(
            Arrays.asList(Field.label("构建版本", buildTaskMdLink(task.getProjectId(), task.getId())).shorten(),
                Field.label("创建时间", task.getCreatedAt().format(CARD_DATE_TIME)).shorten(),
                Field.label("构建状态", statusToMd(task)).shorten(),
                Field.label("更新时间", task.getUpdatedAt().format(CARD_DATE_TIME)).shorten(),
                buildCommitField(project, task))).appendTo(card);

        // Fast deploy buttons
        if (status == BuildTaskStatus.Done && supportsFastDeploy(project)) {
            if (project.getType() == ProjectType.Miniprogram) {
                appendMiniprogramFastDeployButtons(project, task, user, card);
            } else {
                appendNormalFastDeployButtons(project, task, user, card);
            }
        }
        new HrElement().appendTo(card);
        NoteElement note = new NoteElement().appendTo(card);
        Element.Note.txt(String.format("回复『unsub build_tasks %s』退订该项目构建推送", project.getName()))
            .appendTo(note);

        return card;
    }

    private String buildConfigFilePath(long projectId, long taskId, @NonNull String file) {
        String projectName = projectService.getNameById(projectId);
        // The file is .van or .purged
        return format("%s/tasks_config/%d/%s", projectName, taskId, file);
    }

    private void putTaskConfig(long projectId,
                               long taskId,
                               @NonNull Storage storage,
                               @NonNull String file,
                               @NonNull Object content) {
        String path = buildConfigFilePath(projectId, taskId, file);
        storage.putValue(path, content);
    }

    private boolean hasTaskConfig(long projectId, long taskId, @NonNull Storage storage, @NonNull String name) {
        String path = buildConfigFilePath(projectId, taskId, name);
        return storage.exist(path);
    }

    @Override
    @NonNull
    public Optional<BuildTaskModel> get(@Nullable Long projectId, @Nullable Long taskId) {
        if (projectId == null || taskId == null) return Optional.empty();
        Optional<BuildTaskEntity> oe = buildTaskRepository.findByProjectIdAndId(projectId, taskId);
        return oe.map(this::entityToModel);
    }

    @NonNull
    private BuildTaskModel entityToModel(@NonNull BuildTaskEntity entity) {
        UserModel user = userService.getByUniqId(entity.fetchUserUniq());
        if (user == null) user = UserModel.unknown;
        return new BuildTaskModel(entity, user);
    }

    @NonNull
    private Stream<BuildTaskModel> entityToModel(@NonNull Collection<BuildTaskEntity> list) {
        return userService.buildStreamWithUserModel(list, BuildTaskEntity::fetchUserUniq, BuildTaskModel::create);
    }

    @Override
    @NonNull
    public Stream<BuildTaskModel> batchGet(@Nullable Long projectId, @Nullable Set<Long> ids) {
        if (projectId == null || ids == null || ids.isEmpty()) return Stream.empty();
        return entityToModel(buildTaskRepository.findByProjectIdAndIdIn(projectId, ids));
    }

    @Override
    public void assertOwnership(@Nullable Set<Long> taskIds, long projectId) {
        VanUtils.assertOwnership(projectId, taskIds, buildTaskRepository::findOutOfProjectId, "task", "tasks");
    }

    @Override
    @NonNull
    public Map<Long, TaskSyncInfo> findLlmSyncInfo(Set<Long> ids) {
        return metaService.getValue(LLMSyncTaskStatus, ids, TaskSyncInfo.class);
    }

    @Nullable
    @Override
    public TaskSyncInfo findLlmSyncInfo(long taskId) {
        return metaService.getValue(LLMSyncTaskStatus, taskId, TaskSyncInfo.class);
    }

    @NonNull
    @Override
    public Map<String, BuildTaskModel> findLatestByBranches(long projectId, @Nullable Collection<String> branches) {
        if (branches == null || branches.isEmpty()) return Collections.emptyMap();
        return entityToModel(buildTaskRepository.findLatestTaskByBranches(projectId, branches)).collect(
            Collectors.toMap(BuildTaskModel::getBranch, i -> i, (a, b) -> b.getId() > a.getId() ? b : a));
    }

    @NonNull
    @Override
    public List<BuildTaskModel> search(long projectId,
                                       int page,
                                       int size,
                                       @NonNull Map<BuildTaskSearchField, Set<String>> conditions) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        CriteriaQuery<BuildTaskEntity> query = cb.createQuery(BuildTaskEntity.class);
        Root<BuildTaskEntity> root = query.from(BuildTaskEntity.class);

        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.equal(root.get("projectId"), projectId));
        predicates.add(cb.notEqual(root.get("type"), BuildTaskType.MiniprogramDeploy));
        predicates.add(cb.notEqual(root.get("type"), BuildTaskType.MiniprogramUpload));
        conditions.entrySet().stream().flatMap(entity -> {
            BuildTaskSearchField field = entity.getKey();
            Set<String> value = entity.getValue().stream().map(String::trim).filter(v -> !v.isEmpty())
                .collect(Collectors.toSet());
            if (value.isEmpty()) return Stream.empty();
            if (field == BuildTaskSearchField.status) {
                return Stream.of(root.get("status")
                    .in(value.stream().map(s -> EnumUtils.fromString(BuildTaskStatus.class, s)).map(Enum::ordinal)
                        .toArray()));
            }
            if (field == BuildTaskSearchField.keyword) {
                // Every keyword is using 'AND' logic.
                return value.stream().map(v -> {
                    List<Predicate> ors = new ArrayList<>();
                    ors.add(
                        cb.like(root.get("commitMessage"), "%" + escapeLike(encodeGolangJsonWithoutQuotes(v)) + "%"));
                    if (!containsUtf8mb4(v)) {
                        ors.add(cb.like(root.get("username"), "%" + escapeLike(v) + "%"));
                        ors.add(cb.like(root.get("email"), escapeLike(v) + "@%"));
                        ors.add(cb.equal(root.get("hash"), v));
                        ors.add(cb.equal(root.get("branch"), v));
                        if (NumberUtils.isDigits(v)) ors.add(cb.equal(root.get("id"), v));
                    }
                    return cb.or(ors.toArray(new Predicate[0]));
                });
            }
            if (field == BuildTaskSearchField.username) {
                // Every keyword is using 'OR' logic.
                Predicate[] a = value.stream().map(v -> {
                    UserBase.Simple ub = UserBase.parse(v);
                    if (ub == null) return null;
                    String uniqId = ub.getUserUniqId();
                    if (uniqId.isEmpty()) return null;
                    return cb.like(root.get("email"), escapeLike(uniqId) + "@%");
                }).filter(Objects::nonNull).toArray(Predicate[]::new);
                return Stream.of(cb.or(a));
            }
            return Stream.of(root.get(field.name()).in(value));
        }).forEach(predicates::add);

        query.select(root).where(predicates.toArray(new Predicate[0]))
            .orderBy(cb.desc(root.get("createdAt")), cb.desc(root.get("id")));

        List<BuildTaskEntity> list = entityManager.createQuery(query).setFirstResult(page * size).setMaxResults(size)
            .getResultList();

        return entityToModel(list).collect(Collectors.toList());
    }

    @NonNull
    @Override
    public Map<BuildTaskSearchField, List<String>> findSuggestion(long projectId, @Nullable String kw) {
        if (kw == null || Strings.isBlank(kw)) return Collections.emptyMap();
        if (containsUtf8mb4(kw)) return Collections.emptyMap();
        return Stream.of(BuildTaskSearchField.username, BuildTaskSearchField.branch, BuildTaskSearchField.hash)
            .parallel().map(field -> {
                List<String> list;
                if (field == BuildTaskSearchField.username) {
                    list = groupFieldLike(projectId, "email", kw).stream().map(i -> i.substring(0, i.indexOf("@")))
                        .collect(Collectors.collectingAndThen(Collectors.toList(), userService::getByUniqId))
                        .map(UserModel::toString).collect(Collectors.toList());
                } else {
                    list = groupFieldLike(projectId, field.name(), kw);
                }
                if (list.isEmpty()) return null;
                return Pair.of(field, list);
            }).filter(Objects::nonNull).collect(Collectors.toConcurrentMap(Pair::getKey, Pair::getValue));
    }

    @NonNull
    private List<String> groupFieldLike(long projectId, String fieldName, @NonNull String likeValue) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<String> query = cb.createQuery(String.class);
        Root<BuildTaskEntity> root = query.from(BuildTaskEntity.class);
        Path<String> path = root.get(fieldName);
        query.select(path).where(cb.equal(root.get("projectId"), projectId),
            cb.notEqual(root.get("type"), BuildTaskType.MiniprogramDeploy),
            cb.notEqual(root.get("type"), BuildTaskType.MiniprogramUpload),
            cb.like(path, "%" + escapeLike(likeValue) + "%")).groupBy(path).orderBy(cb.desc(root.get("createdAt")));
        return entityManager.createQuery(query).setMaxResults(3).getResultList();
    }

    @Override
    @Nullable
    public Long getLatestSuccessTaskIdsByProjectId(long projectId) {
        // TODO: Refactor this batch finding method.
        return buildTaskRepository.findLatestSuccessTaskByProjectIds(Collections.singleton(projectId)).stream()
            .collect(Collectors.toMap(i -> i[0], i -> i[1], Math::max)).get(projectId);
    }

    @NonNull
    @Override
    public UserBase getFixedUser(BuildTaskModel task) {
        String branch = task.getBranch();

        // Nothing to do if the branch is not starts "van/temporary-".
        final String prefix = "van/temporary-";
        if (!branch.startsWith(prefix)) return task;

        // Nothing to do if the branch is not expected format.
        String[] slices = branch.substring(prefix.length()).split("-");
        if (slices.length != 2) return task;

        // Attempt to extract and parse the associated taskId from the branch name.
        long associatedTaskId;
        try {
            associatedTaskId = Long.parseLong(slices[1]);
        } catch (NumberFormatException e) {
            // Nothing to do if the associated taskId cannot take and parsed from the branch name.
            return task;
        }

        // Nothing to do if the associated taskId is the same.
        if (associatedTaskId == task.getId()) return task;

        // Nothing to do if the task is not found by the associated taskId.
        // Finally, this is the real username (:joy:
        return get(task.getProjectId(), associatedTaskId).orElse(task);
    }

    @NonNull
    @Override
    public Map<Long, Long> findProjectIdsByTaskIds(@Nullable Collection<Long> taskIds) {
        if (taskIds == null) return Collections.emptyMap();
        return buildTaskRepository.findProjectIdsByTaskIds(taskIds).stream()
            .collect(Collectors.toMap(i -> i[0], i -> i[1]));
    }

    @NonNull
    @Override
    public Map<ProjectType, Integer> countEachTypes() {
        return buildTaskRepository.countEachTypes().stream()
            .collect(Collectors.toMap(i -> ProjectType.createFromOrdinal(i[0]), i -> i[1], Integer::sum));
    }

    @Override
    public int countByUser(@NonNull UserModel user) {
        return buildTaskRepository.countByEmailLike(user.getUniqId() + "@%");
    }

    @NonNull
    @Override
    public BuildTaskModel getNeverNull(long projectId, long taskId) {
        return get(projectId, taskId).orElseThrow(() -> {
            String msg = format("The build task #%d on the project #%d is not found", taskId, projectId);
            return new VanBadRequestException(msg);
        });
    }

    @NonNull
    @Override
    public ProjectAndTaskModel getBoth(long projectId, long taskId) {
        CompletableFuture<BuildTaskModel> cm = CompletableFuture.supplyAsync(() -> getNeverNull(projectId, taskId));
        ProjectModel projectModel = projectService.getNeverNull(projectId);
        return new ProjectAndTaskModel(projectModel, cm.join());
    }

    @NonNull
    @Override
    public List<BuildTaskModel> listByType(long projectId, @NonNull BuildTaskType type, int page, int size) {
        return entityToModel(buildTaskRepository.listByType(projectId, type.ordinal(), size, page * size)).collect(
            Collectors.toList());
    }

    @Override
    public void assertTaskMetas(@NonNull BuildTaskModel task) throws VanBadRequestException {
        final String controllerResultSuffix = "-controller_result.json";
        taskResourceService.listTaskMeta(task, null).parallel().map(VanResourceSummary::getPath)
            .filter(i -> i.endsWith(controllerResultSuffix)).map(name -> checkTaskMeta(task, name))
            .filter(Objects::nonNull).filter(Pair::getValue)
            // Join back to the current worker thread.
            .findAny().map(Pair::getKey).map(i -> StringUtils.removeEnd(i, controllerResultSuffix)).ifPresent(name -> {
                String msg = format("The task %s is not allowed to be deployed by hook:%s", task, name);
                throw new VanBadRequestException(msg);
            });
    }

    @Nullable
    private Pair<String, Boolean> checkTaskMeta(@NonNull BuildTaskModel task, @NonNull String name) {
        HttpEntity<InputStream> obj = taskResourceService.getTaskMeta(task, name);
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        if (obj == null) return null;
        InputStream body = obj.getBody();
        if (body == null) return null;
        JsonNode node = mapper.convertValue(body, JsonNode.class);
        if (node == null || !node.isObject()) return null;
        node = node.get("disableTestDeploy");
        if (node == null || !node.isBoolean()) return null;
        return Pair.of(name, node.asBoolean());
    }

    @NonNull
    @Override
    public Stream<ProjectEvent> findBuildTaskEvents(long maxKey, int limit) {
        LocalDateTime lm = ofEpochSecond(maxKey).atZone(ZoneId.systemDefault()).toLocalDateTime();
        return buildTaskRepository.findAfter(lm, limit).stream().map(ProjectEvent::create);
    }

    @NonNull
    @Override
    public Map<Long, String> findTag(long projectId, @NonNull Set<Long> buildTaskIds) {
        return metaService.getValue(TaskMultiBuildTaskInfo, buildTaskIds);
    }

    @Nullable
    @Override
    public String findTag(long projectId, @Nullable Long taskId) {
        if (taskId == null) return null;
        return findTag(projectId, Collections.singleton(taskId)).get(taskId);
    }

    @NonNull
    @Override
    public List<Tuple2<Long, Long>> listTaskIds(OffsetDateTime startTime, OffsetDateTime endTime, int limit) {
        return buildTaskRepository.listTaskIds(startTime, endTime, limit).stream().map(s -> new Tuple2<>(s[0], s[1]))
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public long createBuildTask(@NonNull BuildTaskDTO btd) {
        BuildTaskEntity entity = new BuildTaskEntity();

        entity.setEmail(btd.getEmail());
        entity.setUsername(btd.getUsername());

        entity.setBranch(btd.getBranch());
        entity.setCommitMessage(EncodingUtils.encodeGolangJson(btd.getCommitMessage()));
        entity.setHash(btd.getHash());

        entity.setType(btd.getType());
        entity.setStatus(btd.getStatus());

        entity.setBuildId(btd.getBuildId());
        entity.setProjectId(btd.getProjectId());

        ZoneId z = ZoneId.systemDefault();
        if (btd.getUpdatedAt() != null) {
            entity.setUpdatedAt(btd.getUpdatedAt().atZoneSameInstant(z).toLocalDateTime());
        }
        if (btd.getCreatedAt() != null) {
            entity.setCreatedAt(btd.getCreatedAt().atZoneSameInstant(z).toLocalDateTime());
        }

        buildTaskRepository.saveAndFlush(entity);

        // NOTE: The `saveAndFlush` does not update the auto generated fields such as `createdAt` and `updatedAt`.
        // And the `getById` method will reuse the same Entity cache as Hibernate Persistence Context,
        // so the auto generated fields are also null in the entity.
        // Therefore, the `refresh` is required here.
        entityManager.refresh(entity);

        BuildTaskModel task = entityToModel(entity);

        // This action should be executed synchronously.
        // Because a consumer may update the task status immediately after it is created.
        // If creating the Feishu card is delayed, the Feishu card may not be found while the task updating.
        // However, this action is just a notification logic,
        // which should not block main logic, so the Exception will just be printed as a log.
        // TODO: The better solution is to use a MQ to ensure the actions order.
        try {
            createFeishuCard(task);
        } catch (Exception e) {
            log.error(e);
        }

        return task.getId();
    }

    @Override
    @Transactional
    public void updateBuildTaskStatus(long projectId,
                                      long taskId,
                                      @NonNull BuildTaskStatus status,
                                      @Nullable BuildTaskStatus prevStatus) {
        BuildTaskModel task = getNeverNull(projectId, taskId);
        BuildTaskStatus currentStatus = task.getStatus();
        if (prevStatus != null) {
            if (prevStatus != currentStatus) {
                throw new VanBadRequestException(
                    String.format("The current status %s does not match the %s", currentStatus.name(),
                        prevStatus.name()));
            }
            // The status is already set to expected value, nothing should be done here.
            if (prevStatus == status) return;
        }
        int count = buildTaskRepository.updateStatus(projectId, taskId, status.ordinal(), currentStatus.ordinal());
        if (count == 0) {
            throw new VanBadRequestException("Conflict: The status is currently being edited from another request");
        }

        // The Hibernate Persistence Context caches the Entity object.
        // To ensure the result is not staled, the `clear` method must be called here.
        entityManager.clear();

        task = getNeverNull(projectId, taskId);

        // This action should be executed synchronously.
        // Because a consumer may update the task status more than once in a moment.
        // If updating the Feishu card is delayed, the eventual consistency of the Feishu card data can't be ensured.
        // However, this action is just a notification logic,
        // which should not block main logic, so the Exception will just be printed as a log.
        // TODO: The better solution is to use a MQ to ensure the actions order.
        try {
            updateFeishuCard(task);
        } catch (Exception e) {
            log.error(e);
        }
    }

    private void createFeishuCard(@NonNull BuildTaskModel task) {
        // Nothing to do, if the task type is not NormalBuild.
        if (!NormalBuild.equals(task.getType())) return;

        UserModel user = userService.getByUniqId(task.getUserUniqId());
        ProjectModel project = projectService.getNeverNull(task.getProjectId());

        // Nothing to do, if the task is not owned by any existing user.
        if (user == null) {
            throw new VanBadRequestException("Why is the user for the build task #" + task.getId() + " null?");
        }

        // Nothing to do, if the user has unsubscribed to the notification in this project.
        if (metaService.getOptional(BuildTaskNotificationFeishuSubscribe, user.getId(), Long[].class)
            .map(Arrays::stream).orElseGet(Stream::empty).anyMatch(project.getId()::equals)) {
            log.info("The build notification of task #" + task.getId() + " is ignored by user " + user);
            return;
        }

        String messageId = metaService.getValue(BuildTaskFeishuCardId, task.getId(), String.class);

        // Nothing to do, if the feishu card has been created.
        if (StringUtils.isNotEmpty(messageId)) {
            throw new VanBadRequestException("The Feishu card for build task #" + task.getId() + " was created");
        }

        FeishuCard card = buildFeishuCard(task, project, user);

        messageId = feishuService.sendCard(card, task.extractSimple());

        metaService.setValue(BuildTaskFeishuCardId, task.getId(), String.class, messageId);
    }

    private void updateFeishuCard(@NonNull BuildTaskModel task) {
        String messageId = metaService.getValue(BuildTaskFeishuCardId, task.getId(), String.class);

        // Nothing to do, if the feishu card has not been created.
        if (StringUtils.isEmpty(messageId)) return;

        ProjectModel project = projectService.getNeverNull(task.getProjectId());

        UserModel user = userService.getByUniqId(task.getUserUniqId());

        // Nothing to do, if the task is not owned by any existing user.
        if (user == null) return;

        FeishuCard card = buildFeishuCard(task, project, user);

        feishuService.updateCard(card, messageId);
    }

    @Override
    @Transactional
    public void updateBuildTaskBuildId(long projectId, long taskId, @Nullable String buildId) {
        buildTaskRepository.updateBuildId(projectId, taskId, buildId);
    }

    @Override
    @Transactional
    public void updateBuildTaskBranch(long projectId, long taskId, String branch) {
        buildTaskRepository.updateBranch(projectId, taskId, branch);
    }

    @NonNull
    @Override
    public Stream<BuildTaskModel> findUserRecentBuildTasks(@NonNull UserModel user, int limit) {
        String pattern = user.getUniqId() + "@%";
        if (limit <= 0) throw new VanBadRequestException("The `limit` must be non-negative");
        if (limit > 3000) throw new VanBadRequestException("The `limit` cannot exceed 3000");
        return entityToModel(buildTaskRepository.findUserRecentBuildTasks(pattern, limit ));
    }
}
