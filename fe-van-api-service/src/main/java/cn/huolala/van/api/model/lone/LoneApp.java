package cn.huolala.van.api.model.lone;

import cn.huolala.van.api.model.CsvRow;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Setter
@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class LoneApp {
    @ApiModelProperty("The Lone native ID")
    @Nullable
    private Long busId;

    @ApiModelProperty("The Lone AppID")
    @Nullable
    private String service;

    @ApiModelProperty("Description")
    @Nullable
    private String name;

    @Nullable
    private String gitLink;

    @Nullable
    private LoneLangType langType;
    @Nullable
    private LoneAppType appType;
    @Nullable
    private LoneAccessType accessType;

    @Nullable
    private CsvRow domainIds;
    @Nullable
    private CsvRow domainNames;
    @Nullable
    private CsvRow leaderUids;
    @Nullable
    private CsvRow leaderNames;
    @Nullable
    private CsvRow developUids;
    @Nullable
    private CsvRow developNames;
    @Nullable
    private CsvRow orgIds;
    @Nullable
    private CsvRow departments;

    @Override
    public int hashCode() {
        return service == null ? 0 : service.hashCode();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o instanceof LoneApp) {
            return service != null && service.equals(((LoneApp) o).getService());
        }
        return false;
    }
}
