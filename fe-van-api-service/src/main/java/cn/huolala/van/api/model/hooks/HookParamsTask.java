package cn.huolala.van.api.model.hooks;

import cn.huolala.van.api.model.tasks.BuildTaskModel;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import org.springframework.lang.NonNull;

@Getter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class HookParamsTask {
    private final long id;
    @NonNull
    private final String hash;
    @NonNull
    private final String branch;
    @NonNull
    private final String commitMessage;
    @NonNull
    private final String username;

    public HookParamsTask(@NonNull BuildTaskModel task) {
        id = task.getId();
        hash = task.getHash();
        branch = task.getBranch();
        commitMessage = task.getCommitMessage();
        username = task.toUserString();
    }
}
