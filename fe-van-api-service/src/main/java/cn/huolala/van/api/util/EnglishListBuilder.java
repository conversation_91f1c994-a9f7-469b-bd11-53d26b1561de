package cn.huolala.van.api.util;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collector;

public class EnglishListBuilder {
    @NonNull
    private final List<String> buffer;
    @Nullable
    private final String before;
    @Nullable
    private final String after;

    public EnglishListBuilder() {
        this(null, null);
    }

    public EnglishListBuilder(@Nullable String before, @Nullable String after) {
        this.buffer = new ArrayList<>();
        this.before = before;
        this.after = after;
    }

    public static Collector<String, EnglishListBuilder, EnglishListBuilder> collect() {
        return Collector.of(EnglishListBuilder::new, EnglishListBuilder::append, EnglishListBuilder::merge);
    }

    public static Collector<String, EnglishListBuilder, EnglishListBuilder> collect(@Nullable String before,
                                                                                    @Nullable String after) {
        return Collector.of(() -> new EnglishListBuilder(before, after),
                EnglishListBuilder::append, EnglishListBuilder::merge);
    }

    public void append(@NonNull String part) {
        buffer.add(part);
    }

    public EnglishListBuilder merge(@NonNull EnglishListBuilder another) {
        buffer.addAll(another.buffer);
        return this;
    }

    @NonNull
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        if (before != null) sb.append(before);
        int size = buffer.size();
        for (int i = 0; i < size; i++) {
            if (i > 0) {
                if (size > 2) sb.append(",");
                sb.append(" ");
                if (i + 1 == size) sb.append("and ");
            }
            sb.append(buffer.get(i));
        }
        if (after != null) sb.append(after);
        return sb.toString();
    }
}
