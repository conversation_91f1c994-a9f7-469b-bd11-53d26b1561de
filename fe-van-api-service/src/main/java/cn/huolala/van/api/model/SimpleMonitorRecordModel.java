package cn.huolala.van.api.model;

import cn.huolala.van.api.dao.projection.SimpleMonitorRecordProjection;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

@Data
@AllArgsConstructor
public class SimpleMonitorRecordModel {
    @NonNull
    @JsonIgnore
    private final Long projectId;

    @NonNull
    private final Double pageViewTotal;
    @NonNull
    private final Double score;
    @NonNull
    private final OffsetDateTime recordAt;

    @Nullable
    public static SimpleMonitorRecordModel from(@Nullable SimpleMonitorRecordProjection entity) {
        if (entity == null) return null;
        return new SimpleMonitorRecordModel(
                entity.getProjectId(),
                entity.getPageViewTotal(),
                entity.getScore(),
                entity.getRecordAt().atZone(ZoneId.systemDefault()).toOffsetDateTime()
        );
    }
}
