package cn.huolala.van.api.model.project;

import cn.huolala.van.api.dao.entity.ProjectEntity;
import cn.huolala.api.constants.enums.ProjectType;
import lombok.AccessLevel;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

import static cn.huolala.van.api.util.ModelUtils.parseStringJson;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.defaultString;

@Getter
public class ProjectSummary {
    @Getter(AccessLevel.NONE)
    @NonNull
    protected final ProjectEntity entity;
    @NonNull
    private final Long id;
    @NonNull
    private final ProjectType type;
    @NonNull
    private final String name;
    @NonNull
    private final String description;
    @NonNull
    private final OffsetDateTime activatedAt;

    public ProjectSummary(@NonNull ProjectEntity e) {
        entity = e;
        id = ofNullable(e.getId()).orElse(0L);
        type = ofNullable(e.getType()).orElse(ProjectType.Unknown);
        name = defaultString(e.getName());
        description = parseStringJson(e.getDescription());
        ZoneId zone = ZoneId.systemDefault();
        activatedAt = ofNullable(e.getActivatedAt()).orElseGet(e::getUpdatedAt).atZone(zone).toOffsetDateTime();
    }

    @Nullable
    public static ProjectSummary from(@Nullable ProjectEntity pe) {
        if (pe == null) return null;
        return new ProjectSummary(pe);
    }

    public String toString() {
        return "id=" + id + ", name=" + name;
    }

    @Nullable
    public String fetchRawDescription() {
        return entity.getDescription();
    }
}
