
package cn.huolala.van.api.implementation;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.dao.entity.ComponentDeployHistoryEntity;
import cn.huolala.van.api.dao.enums.ComponentPublishStatus;
import cn.huolala.van.api.dao.repository.ComponentDeployHistoryRepository;
import cn.huolala.van.api.exception.ForbiddenException;
import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.facade.model.gitlab.GitlabBranch;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.SemanticVersion;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.npm.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.GitlabService;
import cn.huolala.van.api.service.HistoryService;
import cn.huolala.van.api.service.UserLogService;
import cn.huolala.van.api.service.NpmService;
import cn.huolala.van.api.util.EncodingUtils;
import cn.lalaframework.logging.LoggerFactory;

@Service
public class NpmServiceImpl implements NpmService {

    private final String email = "<EMAIL>";

    private static final Logger LOGGER = LoggerFactory.getLogger();

    @Value("${van.npm.scope:}")
    private String npmScope;

    @Value("${van.npm.registry:}")
    private String npmRegistry;

    @Value("${van.npm.username:}")
    private String npmUsername;

    @Value("${van.npm.password:}")
    private String npmPassword;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private CloseableHttpClient httpClient;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private GitlabService gitlabService;

    @Autowired
    private UserLogService userLogService;

    @Autowired
    private ComponentDeployHistoryRepository componentDeployHistoryRepository;

    private String login() {
        String url = npmRegistry + "/-/user/org.couchdb.user:" + npmUsername;
        HttpPut httpPut = new HttpPut(url);
        try {
            String encodeAuth = Base64.getEncoder()
                    .encodeToString((npmUsername + ":" + npmPassword).getBytes(StandardCharsets.UTF_8));
            httpPut.setHeader("Authorization", "Basic " + encodeAuth);
            httpPut.setHeader("Content-Type", "application/json");
            NpmLoginParams loginParams = new NpmLoginParams("org.couchdb.user:" + npmUsername, npmUsername, npmPassword,
                    email, "user");
            String params = mapper.writeValueAsString(loginParams);
            httpPut.setEntity(new StringEntity(params, ContentType.APPLICATION_JSON));
        } catch (IOException e) {
            throw new InternalException(e);
        }
        try (CloseableHttpResponse response = httpClient.execute(httpPut)) {
            int status = response.getStatusLine().getStatusCode();
            HttpEntity entity = response.getEntity();
            if (status >= 400) {
                throw new InternalRequestException("failed to login npm, status code: " + status);
            }
            if (entity != null) {
                LOGGER.info("npm login ok");
                // 200 201 etc...
                String body = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                NpmLogin login = mapper.readValue(body, NpmLogin.class);
                return login.getToken();
            }
            throw new InternalRequestException("npm login failed, status code: " + status + ", body null");
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
    }

    private void validatePackageFields(String name, String version) {
        if (!StringUtils.hasText(name)) {
            throw new VanBadRequestException("package name cannot be empty");
        }
        if (!name.startsWith("@" + npmScope + "/")) {
            throw new VanBadRequestException(String.format("package must be in @%s scope", npmScope));
        }
        if (!StringUtils.hasText(version)) {
            throw new VanBadRequestException("package version cannot be empty");
        }
        if (version.length() > 30) {
            throw new VanBadRequestException("version is too long, more than 30");
        }
        if (!SemanticVersion.isValidSemver(version)) {
            throw new VanBadRequestException("version is not followed Semantic Versioning");
        }
    }

    // if remote npm package exists, check if the version already exists or not
    // if it already exists in remote, then should throw an exception or
    // we can publish the version
    private void validateRemoteVersion(String name, String version) {
        try {
            NpmPackageRemoteInfo remoteInfo = getPackageRemoteInfo(name);
            if (remoteInfo != null && remoteInfo.getVersions() != null
                    && remoteInfo.getVersions().containsKey(version)) {
                throw new VanBadRequestException(String.format("%s already exists", version));
            }
        } catch (ResourceNotFoundException e) {
            LOGGER.error("package remote info not found", e);
        }
    }

    private byte[] readTarball(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        String tarballKey = String.format("%s/%s/%s-%s.tar.gz", project.getName(), String.valueOf(task.getId()),
                project.getName(), String.valueOf(task.getId()));
        return Region.defaultStorage().getOptional(tarballKey).map(tbObj -> {
            try {
                if (tbObj.getBody() == null) {
                    throw new InternalException("package no data");
                }
                byte[] tarballBytes = IOUtils.toByteArray(tbObj.getBody());
                if (tarballBytes.length == 0) {
                    throw new InternalException("package tarball is empty");
                }
                return tarballBytes;
            } catch (IOException e) {
                throw new InternalException(e);
            }
        }).orElse(null);
    }

    private NpmVersion buildNpmVersion(
            @NonNull ProjectModel project,
            @NonNull BuildTaskModel task,
            @NonNull NpmPackage npmPackage,
            @NonNull String readme,
            @NonNull byte[] tarballBytes) {
        // npm dist
        String integrity = "sha512-"
                + Base64.getEncoder().encodeToString(EncodingUtils.shaBytes(tarballBytes, "SHA-512"));
        String tarName = npmPackage.getName() + "-" + npmPackage.getVersion() + ".tgz";
        String tarball = npmRegistry + "/" + npmPackage.getName() + "/-/" + tarName;
        NpmDist npmDist = new NpmDist(integrity, EncodingUtils.shaHexString(tarballBytes, "SHA-1"), tarball);
        // pkg
        Map<String, Object> pkg = new HashMap<>();
        pkg.put("author", "van-bot");

        // maintainers
        List<NpmMaintainer> maintainers = new ArrayList<>();
        maintainers.add(new NpmMaintainer(npmUsername, email));

        return new NpmVersion(
                npmPackage.getName() + npmPackage.getVersion(),
                npmPackage.getName(),
                npmPackage.getVersion(),
                npmPackage.getDescription(),
                readme,
                "README.md",
                task.getHash(),
                "",
                "",
                pkg,
                maintainers,
                npmDist);
    }

    @Override
    @NonNull
    public NpmPackage getPackageJsonInfo(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        String fileKey = String.format("%s/%s/package.json", project.getName(), String.valueOf(task.getId()));
        return Region.defaultStorage().getValueNeverNull(fileKey, NpmPackage.class);
    }

    @Override
    @NonNull
    public String getReadMe(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        String readmeKey = String.format("%s/%s/README.md", project.getName(), String.valueOf(task.getId()));
        return Region.defaultStorage().getOptional(readmeKey).map(obj -> {
            try {
                if (obj.getBody() == null) {
                   throw new InternalException("README.md no data");
                }
                byte[] data = IOUtils.toByteArray(obj.getBody());
                return new String(data, StandardCharsets.UTF_8);
            } catch (ResourceNotFoundException e) {
                LOGGER.error("{} not found", readmeKey, e);
                return "";
            } catch (IOException e) {
                throw new InternalException(e);
            }
        }).orElse("");
    }

    @Override
    public NpmInfo getInfo(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        // NOTE: assume here must have a package.json file
        // also should have name version description fields
        NpmPackage npmPackage = getPackageJsonInfo(project, task);
        String fileKey = String.format("%s/%s/package.json", project.getName(), String.valueOf(task.getId()));
        Object pkg = Region.defaultStorage().getOptionalValue(fileKey, Object.class).orElse(null);

        // check version if already exists in npm remote info
        // set npmPublished
        NpmPackageRemoteInfo remoteInfo = null;
        boolean npmPublished = false;
        boolean vanPublished = false;
        Map<String, String> distTags = new HashMap<>();
        try {
            String packageName = String.format("@%s/%s", npmScope, project.getName());
            remoteInfo = getPackageRemoteInfo(packageName);
        } catch (ResourceNotFoundException e) {
            LOGGER.error("resource not found", e);
        }
        if (remoteInfo != null) {
            distTags = remoteInfo.getDistTags();
            if (!npmPackage.getVersion().isEmpty()) {
                npmPublished = remoteInfo.getVersions().containsKey(npmPackage.getVersion());
            }
        }
        int count = historyService.countNpmPublishHistory(project.getId(), task.getId());
        if (count > 0) {
            vanPublished = true;
        }
        return new NpmInfo(pkg, npmPublished, vanPublished, distTags);
    }

    @Override
    public NpmPackageRemoteInfo getPackageRemoteInfo(@NonNull String packageName) {
        String url = npmRegistry + "/" + packageName;
        LOGGER.info("get package remote info from {}", url);
        HttpGet httpGet = new HttpGet(url);
        httpGet.setHeader("Content-Type", "application/json");
        try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
            int status = response.getStatusLine().getStatusCode();
            HttpEntity entity = response.getEntity();
            if (status == 403) {
                throw new ForbiddenException("forbidden");
            }
            if (status == 404) {
                throw new ResourceNotFoundException("npm package not found");
            }
            if (status >= 400) {
                throw new InternalRequestException("failed to fetch package info, status code: " + status);
            }
            if (entity != null) {
                String body = EntityUtils.toString(entity, StandardCharsets.UTF_8);
                return mapper.readValue(body, NpmPackageRemoteInfo.class);
            }
            throw new InternalRequestException("failed to fetch package info, status code: " + status + ", body null");
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
    }

    @Override
    public NpmVersion publishToRegistry(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        NpmPackage npmPackage = getPackageJsonInfo(project, task);
        // validate fields
        validatePackageFields(npmPackage.getName(), npmPackage.getVersion());
        validateRemoteVersion(npmPackage.getName(), npmPackage.getVersion());
        // login first, failed early
        String token = login();
        // get readme
        String readme = getReadMe(project, task);
        byte[] tarballBytes = readTarball(project, task);

        NpmVersion npmVersion = buildNpmVersion(project, task, npmPackage, readme, tarballBytes);
        Map<String, NpmVersion> versions = new HashMap<>();
        versions.put(npmPackage.getVersion(), npmVersion);

        // distTags
        Map<String, String> distTags = new HashMap<>();
        if (SemanticVersion.create(npmPackage.getVersion()).isPreRelease()) {
            distTags.put("dev", npmPackage.getVersion());
        } else {
            distTags.put("latest", npmPackage.getVersion());
        }
        // attachments
        Map<String, NpmAttachment> attachments = new HashMap<>();
        NpmAttachment attachment = new NpmAttachment("application/octet-stream", tarballBytes.length, tarballBytes);
        String tarName = npmPackage.getName() + "-" + npmPackage.getVersion() + ".tgz";
        attachments.put(tarName, attachment);

        NpmPackageRemoteInfo npmPackageRemoteInfo = new NpmPackageRemoteInfo(
                npmVersion.getId(),
                npmVersion.getName(),
                npmVersion.getDescription(),
                distTags,
                versions,
                readme,
                npmVersion.getMaintainers(),
                attachments);

        String url = npmRegistry + "/" + npmPackageRemoteInfo.getName();
        HttpPut httpPut = new HttpPut(url);
        httpPut.setHeader("Content-Type", "application/json");
        if (token != null && !token.isEmpty()) {
            httpPut.setHeader("Authorization", "Bearer " + token);
        } else {
            String encodeAuth = Base64.getEncoder().encodeToString((npmUsername + ":" + npmPassword).getBytes());
            httpPut.setHeader("Authorization", "Basic " + encodeAuth);
        }
        try {
            String params = mapper.writeValueAsString(npmPackageRemoteInfo);
            httpPut.setEntity(new StringEntity(params, ContentType.APPLICATION_JSON));
        } catch (JsonProcessingException e) {
            throw new InternalRequestException(e);
        }
        try (CloseableHttpResponse response = httpClient.execute(httpPut)) {
            int status = response.getStatusLine().getStatusCode();
            if (status >= 400) {
                throw new InternalRequestException("failed to publish package to npm, status code: " + status);
            }
            return npmVersion;
        } catch (IOException e) {
            throw new InternalException(e);
        }
    }

    @Override
    public void createPublishCommit(
            @NonNull ProjectModel project,
            @NonNull BuildTaskModel task,
            @NonNull String name,
            @NonNull String version) {
        // add git repository comment
        String host = "https://van.huolala.work";
        if (!Objects.equals(npmScope, "hll")) {
            host = "https://van.xlcx.work";
        }
        String link = String.format("%s/inspect/%s/%s", host, name, version);
        String comment = String.join(
                "\n",
                String.format(":tada: `%s` of `%s` has been released.", version, name),
                String.format(":point_right: Visit [van](%s) for more details.", link));
        gitlabService.createCommitDiscussion(project, task, comment);
    }

    @Override
    public void createPublishHistoryRecord(
            @NonNull String version,
            @NonNull long creatorId,
            @NonNull long projectId,
            @NonNull long taskId,
            @NonNull ComponentPublishStatus status,
            @NonNull String message) {
        int count = componentDeployHistoryRepository.countByProjectIdAndTaskIdAndVersion(projectId, taskId, version);
        if (count > 0) {
            throw new InternalRequestException("version already exists");
        }
        ComponentDeployHistoryEntity entity = new ComponentDeployHistoryEntity(version, creatorId, projectId, taskId,
                status, message);
        componentDeployHistoryRepository.save(entity);
    }

    @Override
    public NpmVersions listNpmVersions(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        GitlabBranch gitlabBranch = gitlabService.getBranchInfo(project.getRepository(), task.getBranch());
        if (gitlabBranch == null) {
            throw new InternalRequestException("git branch not found");
        }
        if (!gitlabBranch.getCommit().getId().equals(task.getHash())) {
            throw new InternalRequestException(
                    String.format("commit %s of branch %s is outdated", task.getHash(), task.getBranch()));
        }
        byte[] pkgBytes = gitlabService.getRepositoryRawFile(project.getRepository(), "package.json", task.getHash());
        if (pkgBytes == null || pkgBytes.length == 0) {
            throw new InternalRequestException("invalid package.json");
        }
        NpmPackage npmPackage;
        try {
            npmPackage = mapper.readValue(pkgBytes, NpmPackage.class);
        } catch (IOException e) {
            throw new InternalRequestException("deserialize package.json error, " + e.getMessage());
        }
        if (npmPackage == null
                || !StringUtils.hasText(npmPackage.getName())
                || !StringUtils.hasText(npmPackage.getVersion())) {
            throw new InternalRequestException("name and version are required in package.json file");
        }
        SemanticVersion semanticVersion = SemanticVersion.create(npmPackage.getVersion());
        return new NpmVersions(
                semanticVersion.incMajor().toString(),
                semanticVersion.incMinor().toString(),
                semanticVersion.incPatch().toString(),
                semanticVersion.nextPreRelease().toString());
    }

    @Override
    public void publish(@NonNull ProjectModel project, @NonNull BuildTaskModel task, @NonNull UserModel user, String message) {
        if (task.getStatus() != BuildTaskStatus.Done && task.getStatus() != BuildTaskStatus.ReleaseFailed) {
            throw new VanBadRequestException("task status is invalid");
        }
        NpmVersion npmVersion = null;
        ComponentPublishStatus status = ComponentPublishStatus.Unknown;
        try {
            npmVersion = publishToRegistry(project, task);
            status = ComponentPublishStatus.Success;
        } catch (Exception e) {
            LOGGER.error("failed to publish to registry", e);
            status = ComponentPublishStatus.Failed;
        }
        if (npmVersion != null) {
            String version = npmVersion.getVersion();
            // do it async
            CompletableFuture.runAsync(() -> {
                // add user log
                Map<String, String> meta = new HashMap<>();
                meta.put("task_id", String.valueOf(task.getId()));
                meta.put("version", version);
                userLogService.addLog(user.getId(), project.getId(), UserLogType.NPM_RELEASE, "", meta);
            });
            // create gitlab commit
            createPublishCommit(project, task, npmVersion.getName(), npmVersion.getVersion());
        }
        // create history record
        String version = (npmVersion != null && !npmVersion.getVersion().isEmpty()) ? npmVersion.getVersion()
                : "unknown";
        createPublishHistoryRecord(version, user.getId(), project.getId(), task.getId(), status, message);
    }
}
