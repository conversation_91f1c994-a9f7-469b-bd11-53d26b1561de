package cn.huolala.van.api.model.roles;

import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;

import java.util.Optional;

@Setter
@Getter
public class ProjectUserMeta {
    @JsonProperty("region_info")
    private RegionalInfoMap regionInfo;

    @NonNull
    public static ProjectUserMeta createFromJson(String json) {
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        ProjectUserMeta result;
        try {
            if (StringUtils.isNotBlank(json)) {
                result = mapper.readValue(json, ProjectUserMeta.class);
            } else {
                result = null;
            }
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
        if (result == null) return new ProjectUserMeta();
        return result;
    }

    /**
     * Get the "Max" role of a user.
     * <p>
     * NOTE: This usage is outdated.
     * In fact, the "role" is an enum rather than number, DevRole may not be higher than TestRole.
     */
    public Role evaluateMaxRole() {
        return Optional.ofNullable(regionInfo)
                .map(RegionalInfoMap::getMaxRoleValue)
                .orElse(Role.NoRole);
    }
}
