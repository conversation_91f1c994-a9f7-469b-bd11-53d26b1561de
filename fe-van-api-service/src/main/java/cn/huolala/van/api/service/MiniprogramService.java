package cn.huolala.van.api.service;

import cn.huolala.van.api.dao.enums.MiniprogramDeployType;
import cn.huolala.van.api.model.deploy.MiniprogramDeployHistoryRecord;

import java.util.List;
import java.util.Optional;

import org.springframework.lang.NonNull;

public interface MiniprogramService {

    Optional<MiniprogramDeployHistoryRecord> getLatestUploadDeploy(@NonNull long projectId, @NonNull long taskId);

    List<MiniprogramDeployHistoryRecord> getLatestDeployByTypeOfEachRobot(@NonNull long projectId, @NonNull long taskId, @NonNull MiniprogramDeployType type);

    Optional<MiniprogramDeployHistoryRecord> getByProjectIdAndDeployTaskId(@NonNull long projectId, @NonNull long deployTaskId);

    long deployMiniprogram(@NonNull long projectId, @NonNull long taskId, @NonNull long creatorId, @NonNull MiniprogramDeployType type, @NonNull Integer robot, String version, String description);

    long createMiniprogramDeployHistory(Long projectId, Long taskId, Long deployTaskId, Long creatorId, int type, int robot, String version, String description);
}
