package cn.huolala.van.api.model.meta;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.LinkedHashMap;

@Getter
@Setter
@NoArgsConstructor
public class BuildCacheInfo {
    private Status status;

    private Long taskId;

    @ApiModelProperty("NOTE: This is a Unix timestamp in seconds")
    private Integer createdAt;

    @ApiModelProperty("NOTE: This is a Unix timestamp in seconds")
    private Integer updatedAt;

    /**
     * To ensure compatible with golang field name "setTaskID".
     */
    @ApiModelProperty(hidden = true)
    public void setTaskID(Long value) {
        this.taskId = value;
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum Status {
        RUNNING,
        SUCCESS,
        FAILED
    }

    public static class Map extends LinkedHashMap<String, BuildCacheInfo> {
    }
}
