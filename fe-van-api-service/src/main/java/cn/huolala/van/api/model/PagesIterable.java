package cn.huolala.van.api.model;

import cn.huolala.van.api.exception.InternalRequestException;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

public class PagesIterable<T> implements Iterable<T[]> {
    private final int size;
    @NonNull
    private final Load<T> load;

    public PagesIterable(@NonNull Load<T> load, int size) {
        this.size = size;
        this.load = load;
    }

    public static <T> Stream<T> createStream(@NonNull Load<T> load, int size) {
        return StreamSupport.stream(new PagesIterable<>(load, size).spliterator(), false)
                .filter(Objects::nonNull)
                .flatMap(Arrays::stream);
    }

    @NonNull
    @Override
    public Iterator<T[]> iterator() {
        return new PagesIterator<>(load, size, 10);
    }

    @FunctionalInterface
    public interface Load<T> {
        @Nullable
        T[] apply(int currentPage, int pageSize);
    }

    private static class PagesIterator<T> implements Iterator<T[]> {
        private final int size;
        @NonNull
        private final Load<T> load;
        private final int maxPage;
        @Nullable
        private T[] current;
        private int page;

        public PagesIterator(@NonNull Load<T> load, int size, int maxPage) {
            this.size = size;
            this.load = load;
            this.maxPage = maxPage;
            this.page = 1;
        }

        @Override
        public boolean hasNext() {
            return current == null || current.length == size;
        }

        @Override
        public T[] next() throws NoSuchElementException {
            if (page > maxPage) throw new InternalRequestException("Too many pages");
            if (!hasNext()) throw new NoSuchElementException();
            current = load.apply(page++, size);
            return current;
        }
    }
}
