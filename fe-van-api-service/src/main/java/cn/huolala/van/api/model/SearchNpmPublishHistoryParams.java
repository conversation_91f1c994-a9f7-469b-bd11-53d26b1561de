package cn.huolala.van.api.model;

import cn.huolala.van.api.dao.enums.ComponentPublishStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SearchNpmPublishHistoryParams {
    private long projectId;

    @Nullable
    private Long taskId;

    @Nullable
    private String version;

    @Nullable
    private String userUniqId;

    @Nullable
    private ComponentPublishStatus status;

    @Nullable
    private Integer limit;

    public SearchNpmPublishHistoryParams(long projectId, long taskId) {
        this.projectId = projectId;
        this.taskId = taskId;
    }

    public SearchNpmPublishHistoryParams limit(int value) {
        limit = value;
        return this;
    }
}
