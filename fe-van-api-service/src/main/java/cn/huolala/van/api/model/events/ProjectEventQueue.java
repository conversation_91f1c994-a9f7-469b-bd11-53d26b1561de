package cn.huolala.van.api.model.events;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Stream;

public class ProjectEventQueue {
    private static final int DQ_SIZE_PER_PROJECT = 10;

    @NonNull
    private final TreeMap<Long, Set<ProjectEvent>> tree;

    public ProjectEventQueue() {
        tree = new TreeMap<>();
    }

    @Nullable
    private Set<ProjectEvent> ceilingValue(long key) {
        Map.Entry<Long, Set<ProjectEvent>> v = tree.ceilingEntry(key);
        return v == null ? null : v.getValue();
    }

    public synchronized void add(@NonNull ProjectEvent event) {
        while (tree.size() >= DQ_SIZE_PER_PROJECT) tree.remove(tree.firstKey());
        tree.computeIfAbsent(event.getKey(), k -> new HashSet<>()).add(event);
    }

    @Nullable
    public synchronized ProjectEvent getNext(long key, @NonNull Predicate<ProjectEvent> hasSent) {

        Set<ProjectEvent> s;

        // Attempt to send the event of the current key while that event has not been sent.
        s = ceilingValue(key);
        if (s == null) return null;

        for (ProjectEvent pe : s) {
            if (!hasSent.test(pe)) return pe;
            key = pe.getKey();
        }

        // Now, all events of the current key have been sent.
        // Attempt to find the next key.

        s = ceilingValue(key + 1);
        if (s == null) return null;

        Iterator<ProjectEvent> it = s.iterator();
        if (!it.hasNext()) return null;

        return it.next();
    }


    @NonNull
    public Stream<ProjectEvent> getLastStream() {
        Map.Entry<Long, Set<ProjectEvent>> e = tree.lastEntry();
        if (e != null) {
            Set<ProjectEvent> s = e.getValue();
            if (s != null) {
                return s.stream();
            }
        }
        return Stream.empty();
    }
}
