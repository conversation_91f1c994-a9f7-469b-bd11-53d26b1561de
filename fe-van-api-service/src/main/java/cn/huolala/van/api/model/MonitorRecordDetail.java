package cn.huolala.van.api.model;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MonitorRecordDetail {
    private long buildId;

    private List<View> pageViewCount;
    private List<View> pageViewTotal;
    private List<View> apiPerPageCount;
    private List<View> resourcePerPageCount;

    private List<View> apiTime;
    private List<View> resourceTime;

    private List<View> ispTotal;
    private List<View> appTotal;
    private List<View> systemTotal;
    private List<View> dnsTime;
    private List<View> loadTime;
    private List<View> errorPerPageCount;
    private List<View> notResolveErrorTotal;

    private List<View> apiHostCount;
    private List<View> resourceHostCount;
    private List<View> apiTimeUpper;
    private List<View> resourceTimeUpper;
    private List<View> loadTimeUpper;

    @JsonProperty("LCP_time_upper")
    private List<View> lcpTimeUpper;
    @JsonProperty("FCP_time_upper")
    private List<View> fcpTimeUpper;
    @JsonProperty("TTFB_time_upper")
    private List<View> ttfbTimeUpper;

    private Object resourceSize;
    private Object useCache;
    private Object useGzip;

    private Map<String, ScoreInfo> scoreInfo;

    private MiniProgramDetail miniProgramDetail;
    private MiniProgramScoreSummary miniProgramScoreSummary;

    @Nullable
    public static MonitorRecordDetail fromRaw(@Nullable Object raw) {
        if (raw instanceof String) {
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            try {
                return mapper.readValue((String) raw, MonitorRecordDetail.class);
            } catch (JsonProcessingException e) {
                throw new InternalMappingException(e);
            }
        }
        return null;
    }

    @NonNull
    public Map<String, ScoreInfo> getScoreInfo() {
        return scoreInfo == null ? Collections.emptyMap() : scoreInfo;
    }

    @Data
    public static class View {
        private Map<String, String> tags;
        private Metric metric;
        private List<View> subGroupMetric;

        @NonNull
        public Map<String, String> getTags() {
            return tags == null ? Collections.emptyMap() : tags;
        }

        @NonNull
        public Metric getMetric() {
            return metric == null ? new Metric() : metric;
        }
    }

    @Data
    public static class Metric {
        private double max;
        private double min;
        private double variance;
        private double arg;
        private double total;
        private double upper;
    }

    @Data
    public static class ScoreInfo {
        private double total;
        private double score;
        private double value;
    }
}
