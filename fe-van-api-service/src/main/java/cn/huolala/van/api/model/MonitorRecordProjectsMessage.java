package cn.huolala.van.api.model;

import cn.huolala.van.api.dao.entity.MonitorRecordProjectEntity;
import cn.huolala.van.api.util.FeishuUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

public class MonitorRecordProjectsMessage {
    public Message getMessage(List<MonitorRecordProjectEntity> projects,
                              List<MonitorRecordProjectEntity> lastWeekProjects, String receiverUserUniqId, String secret) {
        Message message = new Message("前端项目评分周报");
        List<Object> header = new ArrayList<>(
                Arrays.asList(new MarkDown("**项目**"), new MarkDown("**分数**"), new MarkDown("**环比上周**")));
        ColumnSet tableHeader = new ColumnSet(header);
        tableHeader.background_style = "grey";
        message.elements.add(tableHeader);
        Map<Long, MonitorRecordProjectEntity> projectsMap = lastWeekProjects.stream()
                .collect(Collectors.toMap(p -> p.getId(), p -> p));
        Overflow overflow = new Overflow();
        Map<String, String> values = new HashMap<>();
        values.put("type", "send_project_record_score_detail");
        values.put("receiverUserUniqId", receiverUserUniqId);
        values.put("time", LocalDateTime.now().toString());
        String sign = FeishuUtils.sign(values, secret);
        values.put("sign", sign);
        overflow.value = values;
        for (MonitorRecordProjectEntity project : projects) {
            MonitorRecordProjectEntity lastWeekProject = projectsMap.get(project.getId());
            String compareString = "-";
            if (lastWeekProject == null || project.getScore() <= 0) {
                compareString = "-";
            } else {
                Double tmp = ((project.getScore() - lastWeekProject.getScore()) / lastWeekProject.getScore()) * 100;
                Integer percent = tmp.intValue();
                compareString = getColorString(getPercentColor(percent),
                        String.format("%s %d", getPercentPrefix(percent), Math.abs(percent)) + "%");
            }

            Text t = new Text(String.format("查看%s详情", project.getName()));
            Option option = new Option(t, project.getId().toString());
            overflow.options.add(option);
            List<Object> lineString = new ArrayList<>(
                    Arrays.asList(new MarkDown(project.getName()),
                            new MarkDown(getColorString(getScoreColor(project.getScore()),
                                    String.format("%.0f分", project.getScore()))),
                            new MarkDown(compareString)));
            ColumnSet line = new ColumnSet(lineString);
            message.elements.add(line);
        }
        message.elements.add(new Action(overflow));
        return message;
    }

    private String getColorString(String color, String str) {
        return String.format("<font color='%s'>%s</font>", color, str);
    }

    private String getPercentPrefix(Integer percent) {
        if (percent < 0) {
            return "↓";
        }

        if (percent > 0) {
            return "↑";
        }

        return "";
    }

    private String getPercentColor(Integer percent) {
        if (percent < 0) {
            return "red";
        }

        if (percent > 0) {
            return "green";
        }

        return "black";
    }

    private String getScoreColor(Double score) {
        if (score > 80) {
            return "green";
        }

        if (score < 60) {
            return "red";
        }

        return "black";
    }

    public class Action {
        private List<Object> actions;
        private String tag;

        public Action(Object action) {
            this.actions = new ArrayList<>();
            this.tag = "action";
            this.actions.add(action);
        }

        public List<Object> getActions() {
            return actions;
        }

        public void setActions(List<Object> actions) {
            this.actions = actions;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }
    }

    public class Overflow {
        ArrayList<Option> options = new ArrayList<Option>();
        Map<String, String> value;
        private String tag;

        public Overflow() {
            this.tag = "overflow";
            this.options = new ArrayList<>();
            this.value = new HashMap<>();
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public Map<String, String> getValue() {
            return value;
        }

        public void setValue(Map<String, String> value) {
            this.value = value;
        }

        public ArrayList<Option> getOptions() {
            return options;
        }

        public void setOptions(ArrayList<Option> options) {
            this.options = options;
        }
    }

    public class Option {
        private Text text;
        private String value;

        public Option(Text text, String value) {
            this.text = text;
            this.value = value;
        }

        public Text getText() {
            return text;
        }

        public void setText(Text text) {
            this.text = text;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

    }

    public class Div {
        private String tag;
        private Text text;
        private Object extra;

        public Div(Text text, Object extra) {
            this.text = text;
            this.tag = "div";
            this.extra = extra;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public Text getText() {
            return text;
        }

        public void setText(Text text) {
            this.text = text;
        }

        public Object getExtra() {
            return extra;
        }

        public void setExtra(Object extra) {
            this.extra = extra;
        }

    }

    public class Button {
        LarkMarkDown text;
        Map<String, String> value;
        private String tag;
        private String type;

        public Button(LarkMarkDown text) {
            this.text = text;
            this.tag = "button";
            this.type = "default";
        }

        public Button(LarkMarkDown text, Map<String, String> value) {
            this.text = text;
            this.tag = "button";
            this.type = "default";
            this.value = value;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public LarkMarkDown getText() {
            return text;
        }

        public void setText(LarkMarkDown textObject) {
            this.text = textObject;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Map<String, String> getValue() {
            return value;
        }

        public void setValue(Map<String, String> value) {
            this.value = value;
        }
    }

    public class Column {
        public String tag;
        public String width;
        public int weight;
        public String vertical_align;
        public ArrayList<Object> elements;

        public Column() {
            this.tag = "column";
            this.width = "weighted";
            this.weight = 1;
            this.vertical_align = "top";
            this.elements = new ArrayList<>();
        }

        public Column(Object content) {
            this.tag = "column";
            this.width = "weighted";
            this.weight = 1;
            this.vertical_align = "top";
            this.elements = new ArrayList<>();
            ColumnSet element = new ColumnSet();
            Column column = new Column();
            column.elements.add(content);
            element.columns.add(column);
            this.elements.add(element);
        }
    }

    public class Config {
        public boolean wide_screen_mode;

        public Config() {
            this.wide_screen_mode = true;
        }
    }

    public class ColumnSet {
        public String tag;
        public String flex_mode;
        public String background_style;
        public ArrayList<Column> columns;

        public ColumnSet() {
            this.tag = "column_set";
            this.flex_mode = "none";
            this.background_style = "default";
            this.columns = new ArrayList<>();
        }

        public ColumnSet(List<Object> objects) {
            this.tag = "column_set";
            this.flex_mode = "none";
            this.background_style = "default";
            this.columns = new ArrayList<>();
            for (Object object : objects) {
                this.columns.add(new Column(object));
            }
        }
    }

    public class Message {
        public Config config;
        public ArrayList<Object> elements;
        public Header header;

        public Message(String title) {
            this.config = new Config();
            this.elements = new ArrayList<>();
            this.header = new Header(title);
        }
    }

    public class MarkDown extends Text {
        public MarkDown(String content) {
            super(content);
            this.tag = "markdown";
        }
    }

    public class Text {
        public String content;
        public String tag;
        public String text_align;

        public Text(String content) {
            this.tag = "plain_text";
            this.text_align = "center";
            this.content = content;
        }
    }

    public class LarkMarkDown extends Text {
        public LarkMarkDown(String content) {
            super(content);
            this.tag = "lark_md";
        }
    }

    public class Header {
        public String template;
        public Text title;

        public Header(String headerTitle) {
            this.template = "green";
            this.title = new Text(headerTitle);
        }
    }

}
