package cn.huolala.van.api.model.hooks;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.project.ProjectModel;
import lombok.Getter;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.stream.Collectors;

@Getter
public class HookParamsForPrd extends HookParamsBase {
    @NonNull
    private final List<HookParamsTask> task;
    @NonNull
    private final Region idc;

    public HookParamsForPrd(@NonNull ProjectModel project,
                            @NonNull UserModel user,
                            @NonNull Region region,
                            @NonNull List<BuildTaskModel> tasks) {
        super(project, user, Env.prd);
        this.idc = region;
        this.task = tasks.stream().map(HookParamsTask::new).collect(Collectors.toList());
    }
}
