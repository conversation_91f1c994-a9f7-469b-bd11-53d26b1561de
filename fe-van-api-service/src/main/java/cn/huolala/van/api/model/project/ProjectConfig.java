package cn.huolala.van.api.model.project;

import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.Region;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Data
public class ProjectConfig {
    private String appId;

    /**
     * @deprecated Use ProjectModel.getRepository method instead.
     */
    @SuppressWarnings("java:S1133")
    @Deprecated
    private String repository;

    /**
     * @deprecated Dynamic lookup from Lone.
     */
    @SuppressWarnings("java:S1133")
    @Deprecated
    private Integer appIdId;

    private String devDomain;

    @JsonProperty("auto_create")
    private AutoCreate autoCreate;

    @JsonProperty("message_config")
    private MessageConfig messageConfig;

    @JsonProperty("build_script")
    private String buildScript;

    @JsonProperty("multi_build_script")
    private List<MultiBuildScript> multiBuildScript;

    @JsonProperty("build_image")
    private String buildImage;

    private Cache cache;
    private Npm npm;

    @JsonProperty("skip_checks")
    private Boolean skipChecks;

    private Build build;

    private List<Region> regions;

    @Data
    public static class AutoCreate {
        private String type;
        private String platform;
    }

    @Data
    public static class MessageConfig {
        private Event launchPrdEvent;
        private Event alertEvent;
        private Event launchTestEvent;

        public void validate() {
            if (launchPrdEvent == null) throw new VanBadRequestException("The launchPrdEvent is required");
            if (alertEvent == null) throw new VanBadRequestException("The alertEvent is required");
            if (launchTestEvent == null) throw new VanBadRequestException("The launchTestEvent is required");
        }

        @Data
        public static class Event {
            private String name;
            private String description;
            private Boolean enable;
        }
    }

    @Data
    public static class MultiBuildScript {
        private String script;
        private Map<String, String> env;
        private String tag;
        private Boolean disable;
        @JsonProperty("branch_filter")
        private Webhook branchFilter;
    }

    @Data
    public static class TriggeredTask {
        @JsonProperty("task_id")
        private Long taskId;
        private Status status;

        public enum Status {
            unknown,
            running,
            failed,
            success;

            @JsonCreator
            public static Status fromString(String str) {
                return Arrays.stream(Status.values())
                        .filter(i -> i.name().equals(str)).findFirst().orElse(unknown);
            }
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Cache extends TriggeredTask {
        private Boolean enable;
        @JsonProperty("build_id")
        private String buildId;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Npm extends TriggeredTask {
        private String version;
    }

    @Data
    public static class Build {
        private Long memory;
        private Long cpu;
        private Long timeout;
    }

    @Data
    public static class Webhook {
        private List<String> branch;
        private Boolean skip;
    }
}
