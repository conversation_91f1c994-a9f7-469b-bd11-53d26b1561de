package cn.huolala.van.api.util;

import cn.lalaframework.soa.exception.BusinessException;
import cn.lalaframework.tools.util.HexUtil;
import org.apache.http.NameValuePair;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class PhpUtils {
    private PhpUtils() {
    }

    public static String sign(String secret, List<NameValuePair> list) {
        try {
            MessageDigest md = MessageDigest.getInstance("md5");
            md.update(secret.getBytes(StandardCharsets.UTF_8));
            list.stream().sorted(Comparator.comparing(NameValuePair::getName)).forEach(i -> {
                md.update(i.getName().getBytes(StandardCharsets.UTF_8));
                if (i.getValue() == null) return;
                md.update(i.getValue().getBytes(StandardCharsets.UTF_8));
            });
            md.update(secret.getBytes(StandardCharsets.UTF_8));
            return HexUtil.encodeHexStr(md.digest()).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            throw new BusinessException(e);
        }
    }

    public static String sign(String secret, Map<String, ?> map) {
        try {
            MessageDigest md = MessageDigest.getInstance("md5");
            md.update(secret.getBytes(StandardCharsets.UTF_8));
            map.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(e -> {
                md.update(e.getKey().getBytes(StandardCharsets.UTF_8));
                if (e.getValue() != null) {
                    md.update(e.getValue().toString().getBytes(StandardCharsets.UTF_8));
                }
            });
            md.update(secret.getBytes(StandardCharsets.UTF_8));
            return HexUtil.encodeHexStr(md.digest()).toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            throw new BusinessException(e);
        }
    }
}
