package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.van.api.dao.entity.MetaEntity;
import cn.huolala.van.api.exception.DirtyDataException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class MetaModel {
    @Getter(AccessLevel.NONE)
    @NonNull
    private final Map<JavaType, Object> parsedCache;

    private long id;

    @NonNull
    private final MetaType type;
    @NonNull
    private final Long metaId;

    @Nullable
    private String meta;
    @Nullable
    private OffsetDateTime createdAt;
    @Nullable
    private OffsetDateTime updatedAt;

    public MetaModel(@NonNull MetaType type, @NonNull Long metaId) {
        this.type = type;
        this.metaId = metaId;
        parsedCache = new HashMap<>();
    }

    @Nullable
    public static <T> T parseMeta(@Nullable String json, @NonNull Class<T> valueType) {
        try {
            if (json == null) return null;
            // If the content is not an array but the caller expects it to be an array, wrap it as an array.
            boolean expectsAnArray = valueType.isArray() || List.class.isAssignableFrom(valueType);
            if (expectsAnArray && !json.startsWith("[")) {
                json = String.format("[%s]", json);
            }
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            return mapper.readValue(json, valueType);
        } catch (JsonProcessingException e) {
            throw DirtyDataException.create(e);
        }
    }

    @Nullable
    public static <T> String serializeMeta(@Nullable T meta) {
        try {
            if (meta == null) return null;
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            return mapper.writeValueAsString(meta);
        } catch (JsonProcessingException e) {
            throw DirtyDataException.create(e);
        }
    }

    @NonNull
    public static MetaModel from(MetaEntity entity) {
        ZoneId zi = ZoneId.systemDefault();
        MetaModel m = new MetaModel(entity.getType(), entity.getMetaId());
        m.setId(entity.getId());
        m.setMeta(entity.getMeta());
        m.setCreatedAt(entity.getCreatedAt().atZone(zi).toOffsetDateTime());
        m.setUpdatedAt(entity.getUpdatedAt().atZone(zi).toOffsetDateTime());
        return m;
    }

    @Nullable
    public <T> T parseMeta(@NonNull Class<T> valueType) {
        return parseMeta(meta, valueType);
    }

    @Nullable
    public <T> T parseMetaWithoutThrow(@NonNull Class<T> valueType) {
        try {
            return parseMeta(meta, valueType);
        } catch (DirtyDataException ignored) {
            return null;
        }
    }
}
