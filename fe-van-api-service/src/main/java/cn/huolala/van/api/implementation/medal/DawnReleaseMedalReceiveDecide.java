package cn.huolala.van.api.implementation.medal;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.entity.CanaryHistoryEntity;
import cn.huolala.van.api.dao.entity.UserEntity;
import cn.huolala.van.api.dao.repository.CanaryHistoryRepository;
import cn.huolala.van.api.service.MedalReceiveDecideService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

@Component
public class DawnReleaseMedalReceiveDecide implements MedalReceiveDecideService {

    @Autowired
    private CanaryHistoryRepository canaryHistoryRepository;

    @Override
    public boolean receive(UserEntity user, LocalDateTime end) {
        // 外层会检查是否获得这个勋章，这里就不需要检查了

        // 历史的发布时间不看了，只看过去 7 填的

        LocalDateTime date = end.withHour(0).withMinute(0).withSecond(0).withNano(0).minusDays(7);

        List<CanaryHistoryEntity> canaryHistoryEntities = canaryHistoryRepository.findByCreatorIdAndCreatedAtAfter(user.getId(), date);

        return canaryHistoryEntities.stream()
                .anyMatch(item -> (item.getCreatedAt().getHour() >= 2 && item.getCreatedAt().getHour() < 6));
    }

    @Override
    public MedalEnum medal() {
        return MedalEnum.RELEASE_IN_DAWN;
    }
}
