package cn.huolala.van.api.model;

import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.NonNull;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.joining;

@Getter
@Setter
public class PmisReleaseRecord {
    private String branch;
    private String commitId;
    private String packageName;

    private String deployUser;
    private String env;

    private Integer isGray;


    private String reason;
    private String region;

    private Long releaseId;

    private String releaseName;
    private String releaseTime;


    private String serviceName;
    private String status;

    private ReleaseType releaseType;
    private WindowType windowType;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime createdAt;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime updatedAt;

    public PmisReleaseRecord(@NonNull UserModel user,
                             @NonNull ProjectModel project,
                             @NonNull List<BuildTaskModel> tasks,
                             long launchId) {
        branch = (tasks.stream().map(BuildTaskModel::getBranch).collect(joining(",")));
        commitId = (tasks.stream().map(BuildTaskModel::getHash).collect(joining(",")));
        packageName = (tasks.stream().map(BuildTaskModel::getId).map(Object::toString).collect(joining(",")));
        releaseType = (ReleaseType.Normal);
        windowType = (WindowType.Normal);
        isGray = 2;
        region = "1";
        serviceName = project.fetchAndAssertAppId();
        deployUser = user.getUniqId();
        releaseId = launchId;
        env = "prd";
        status = "success";
        OffsetDateTime now = OffsetDateTime.now();
        updatedAt = now;
        createdAt = now;
        releaseTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum ReleaseType {
        @JsonEnumDefaultValue
        Unknown,
        Normal
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum WindowType {
        @JsonEnumDefaultValue
        Unknown,
        Normal
    }
}
