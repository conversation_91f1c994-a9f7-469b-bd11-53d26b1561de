package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.dao.entity.BaseEntity;
import cn.huolala.van.api.dao.entity.MetaEntity;
import cn.huolala.van.api.dao.entity.ProjectEntity;
import cn.huolala.van.api.dao.model.IdContentPair;
import cn.huolala.van.api.dao.repository.ProjectRepository;
import cn.huolala.van.api.exception.ForbiddenException;
import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.facade.model.gitlab.GitlabBranch;
import cn.huolala.van.api.facade.model.gitlab.GitlabMergeRequest;
import cn.huolala.van.api.facade.model.gitlab.GitlabProject;
import cn.huolala.van.api.facade.model.gitlab.GitlabWebhook;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.model.meta.BuildCacheInfo;
import cn.huolala.van.api.model.meta.DeployHookModel;
import cn.huolala.van.api.model.meta.MiniprogramInfoModel;
import cn.huolala.van.api.model.project.ProjectBoilerplate;
import cn.huolala.van.api.model.project.ProjectConfig;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.project.ProjectSummary;
import cn.huolala.van.api.model.roles.ProjectUserMeta;
import cn.huolala.van.api.model.roles.ProjectUserModel;
import cn.huolala.van.api.model.roles.RegionalInfoMap;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.service.feishu.HuolalaFeishuEnhanceService;
import cn.huolala.van.api.util.EncodingUtils;
import cn.huolala.van.api.util.StorageHelper;
import cn.huolala.van.api.util.FeishuUtils;
import cn.huolala.van.api.util.ProjectUtils;
import cn.lalaframework.lock.annotation.Lock;
import cn.lalaframework.storage.adapter.Storage;
import cn.lalaframework.logging.LoggerFactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.lark.oapi.service.contact.v3.model.User;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.huolala.api.constants.enums.MetaType.ProjectBuildCacheStatus;
import static cn.huolala.api.constants.enums.MetaType.ProjectDeployHookConfiguration;
import static java.lang.String.format;
import static java.util.Optional.ofNullable;
import static java.util.concurrent.CompletableFuture.supplyAsync;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
public class ProjectServiceImpl implements ProjectService {
    private static final String vanJson = "/van.json";
    private final ConcurrentHashMap<Long, String> idNameCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> nameIdCache = new ConcurrentHashMap<>();
    private final String[][] emailFormats = {
        {"@huolala.cn", "@lalamove.com"},
        {"@huolala.cn", "@xiaolachuxing.com"},
        {"@huolala.cn", "@uncle-delivery.com"},
        {".xl@", "@"},
    };
    private static final Logger LOGGER = LoggerFactory.getLogger();

    @Value("${van.repository.gitlab}")
    private String gitlabUrl;

    @Value("${van.repository.official_group:group-van}")
    private String gitlabGroup;

    @Value("${van.repository.webhook:http://gitlab.van.hll.hui.lu:24359/trigger/repository}")
    private String gitlabWebhookUrl;

    @Value("${van.feishu.apply_window.code:DE632D82-3767-4701-BBCC-E6F3250799F9}")
    private String approvalWindowCode;

    @Value("${van.feishu.apply_domain.forms:}")
    private String approvalForms;

    @Value("${van.feishu.apply_domain.code:DDF5D86F-15C4-468D-8C00-0F3CB47B1B6D}")
    private String approvalDomainCode;

    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private MetaService metaService;
    @Autowired
    private LoneService loneService;
    @Autowired
    private GitlabService gitlabService;
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private HuolalaFeishuEnhanceService huolalaFeishuEnhanceService;
    @Autowired
    private UserLogService userLogService;
    @Autowired
    private BuildTaskService buildTaskService;

    @Autowired
    private ObjectMapper mapper;
    @PersistenceContext
    private EntityManager entityManager;

    private static void appendQueryParameters(@NonNull ProjectSearchParams params,
                                              @NonNull StringBuilder sql,
                                              @NonNull Map<String, Object> args) {
        ProjectType type = params.getType();
        if (type != null) {
            sql.append("AND p.type = :type\n");
            args.put("type", type.ordinal());
        }

        String keyword = params.getKeyword();
        if (keyword != null && !keyword.isEmpty()) {
            sql.append("AND (\n");

            sql.append("  p.description LIKE :description\n");
            String e2kw = EncodingUtils.escapeLike(EncodingUtils.encodeGolangJsonWithoutQuotes(keyword));
            args.put("description", "%" + e2kw + "%");

            if (!EncodingUtils.containsUtf8mb4(keyword)) {
                sql.append("  OR p.name LIKE :name\n");
                sql.append("  OR p.config LIKE :configAppId\n");
                String ekw = EncodingUtils.escapeLike(keyword);
                args.put("name", "%" + ekw + "%");
                args.put("configAppId", "%\"appId\":\"" + ekw + "\"%");
            }

            sql.append(") \n");
        }
    }

    @NonNull
    @Override
    public Optional<ProjectModel> get(@Nullable Long id) {
        return ofNullable(id).flatMap(projectRepository::getById).map(ProjectModel::new);
    }

    @Override
    @NonNull
    public ProjectModel getNeverNull(@Nullable Long id) {
        return get(id).orElseThrow(() -> {
            String msg = format("The project #%d is not found", id);
            return new VanBadRequestException(msg);
        });
    }

    @Override
    @NonNull
    public ProjectModel getNeverNull(@Nullable IdOrName ion) {
        return getNeverNull(getId(ion));
    }

    @Override
    @NonNull
    public PageResult<ProjectSummary> searchForUser(@NonNull UserModel user, @NonNull ProjectSearchParams params) {
        CompletableFuture<Long> task = CompletableFuture.supplyAsync(() -> count(params));
        return new PageResult<>(makeModelListForUser(user, query(user, params), true), task.join());
    }

    private long count(@NonNull ProjectSearchParams params) {
        Map<String, Object> args = new HashMap<>();
        StringBuilder sql = new StringBuilder("/* ProjectServiceImpl.count */\n");
        sql.append("SELECT count(1) FROM projects p\n");

        sql.append("WHERE\n");
        sql.append("p.repository != ''\n");
        appendQueryParameters(params, sql, args);

        Query query = entityManager.createNativeQuery(sql.toString());
        args.forEach(query::setParameter);

        return mapper.convertValue(query.getSingleResult(), Long.class);
    }

    @NonNull
    private List<ProjectEntity> query(@NonNull UserModel user, @NonNull ProjectSearchParams params) {
        Map<String, Object> args = new HashMap<>();
        StringBuilder sql = new StringBuilder("/* ProjectServiceImpl.query */\n");
        sql.append("SELECT p.* FROM projects p\n");

        sql.append("LEFT JOIN project_stars ps ON p.id = ps.project_id AND ps.user_id = :userId\n");
        sql.append("LEFT JOIN project_users pu ON p.id = pu.project_id AND pu.user_id = :userId\n");
        args.put("userId", user.getId());

        sql.append("WHERE\n");
        sql.append("p.repository != ''\n");
        appendQueryParameters(params, sql, args);

        sql.append("ORDER BY\n");
        sql.append("ISNULL(ps.id),\n");
        sql.append("pu.role > 0 DESC,\n");
        sql.append("p.activated_at DESC\n");

        Query query = entityManager.createNativeQuery(sql.toString(), ProjectEntity.class);
        args.forEach(query::setParameter);

        @SuppressWarnings("unchecked") List<ProjectEntity> resultList = query.setMaxResults(params.getSize(20))
            .setFirstResult(params.getSize(20) * params.getPage(0)).getResultList();

        return resultList;
    }

    @Override
    @NonNull
    public List<ProjectSummary> findForUser(@NonNull UserModel user, @NonNull Set<Long> ids) {
        if (ids.isEmpty()) return Collections.emptyList();
        return makeModelListForUser(user, projectRepository.findByIds(ids), false);
    }

    @Override
    @NonNull
    public List<ProjectModel> find(@NonNull Set<Long> ids) {
        if (ids.isEmpty()) return Collections.emptyList();
        return projectRepository.findByIds(ids).stream().map(ProjectModel::new).collect(Collectors.toList());
    }

    @Override
    @NonNull
    public Stream<String> findNamesByTypes(ProjectType... types) {
        return projectRepository.findNamesByTypes(Arrays.stream(types).map(Enum::ordinal).collect(Collectors.toSet()))
            .stream();
    }

    /**
     * Dynamically convert entity to ProjectSummary or ProjectModel based on the user permission.
     * If the user is not a participant in a project, they shouldn't have access to the complete project information.
     */
    @NonNull
    private List<ProjectSummary> makeModelListForUser(@NonNull UserModel user,
                                                      @NonNull List<ProjectEntity> list,
                                                      boolean doNotCareSuperAdmin) {
        Set<Long> ids = list.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        Set<Long> aSet = userService.filterUserAccessibleProjectIds(ids, user, doNotCareSuperAdmin);
        return list.stream().map(i -> aSet.contains(i.getId()) ? new ProjectModel(i) : new ProjectSummary(i))
            .collect(Collectors.toList());
    }

    @NonNull
    @Override
    public String getNameById(Long id) {
        String res = findNameByIds(Collections.singletonList(id)).get(id);
        if (res == null) throw ResourceNotFoundException.create(ProjectModel.NAME, "id", id);
        return res;
    }

    @NonNull
    @Override
    public Long getIdByName(String name) {
        Long res = findIdByNames(Collections.singletonList(name)).get(name);
        if (res == null) throw ResourceNotFoundException.create(ProjectModel.NAME, "name", name);
        return res;
    }

    @Nullable
    private Long getId(@Nullable IdOrName ion) {
        if (ion == null) return null;
        Object value = ion.getValue();
        if (value instanceof String) return getIdByName((String) value);
        if (value instanceof Long) return (Long) value;
        return null;
    }

    @Nullable
    private String getName(@Nullable IdOrName ion) {
        if (ion == null) return null;
        Object value = ion.getValue();
        if (value instanceof String) return (String) value;
        if (value instanceof Long) getNameById((Long) value);
        return null;
    }

    @NonNull
    private <K, V> Set<K> copyExistedItemsFromCache(Collection<K> keys, Map<K, V> cache, Map<K, V> receiver) {
        Set<K> misSet = new HashSet<>();
        for (K key : keys) {
            V value = cache.get(key);
            if (value == null) {
                misSet.add(key);
            } else {
                receiver.put(key, value);
            }
        }
        return misSet;
    }

    @NonNull
    @Override
    public Map<String, Long> findIdByNames(Collection<String> names) {
        HashMap<String, Long> result = new HashMap<>();
        Set<String> misSet = copyExistedItemsFromCache(names, nameIdCache, result);
        if (misSet.isEmpty()) return result;
        for (IdContentPair s : projectRepository.findIdsByNames(misSet)) {
            result.put(s.getContent(), s.getId());
            nameIdCache.putIfAbsent(s.getContent(), s.getId());
            idNameCache.putIfAbsent(s.getId(), s.getContent());
        }
        return result;
    }

    @NonNull
    @Override
    public Map<Long, String> findNameByIds(Collection<Long> ids) {
        HashMap<Long, String> result = new HashMap<>();
        Set<Long> misSet = copyExistedItemsFromCache(ids, idNameCache, result);
        if (misSet.isEmpty()) return result;
        for (IdContentPair s : projectRepository.findNamesByIds(misSet)) {
            result.put(s.getId(), s.getContent());
            nameIdCache.putIfAbsent(s.getContent(), s.getId());
            idNameCache.putIfAbsent(s.getId(), s.getContent());
        }
        return result;
    }

    /**
     * DataPath: /:projectName/van.json
     *
     * @param projectId The project ID.
     * @return The parsed config struct.
     * <p>
     * TODO: Migrate this data to database.
     */
    @Override
    @NonNull
    public VanProjectConfig getVanProjectConfig(@NonNull Region region, long projectId) {
        String name = getNameById(projectId);
        return region.getStorage().getValueNeverNull(name + vanJson, VanProjectConfig.class);
    }

    @Override
    @NonNull
    @Lock(key = "partialUpdateVanProjectConfig:#{region}:#{projectId}")
    public Change<VanProjectConfig> partialUpdateVanProjectConfig(long projectId,
                                                                  @NonNull Region region,
                                                                  @NonNull NonNullConsumer<VanProjectConfig> handler) {
        final Storage storage = region.getStorage();
        final String name = getNameById(projectId);
        final String key = name + vanJson;
        final VanProjectConfig before = storage.getOptionalValue(key, VanProjectConfig.class).orElse(null);
        final VanProjectConfig after;
        if (before == null) {
            after = new VanProjectConfig();
        } else {
            after = mapper.convertValue(before, VanProjectConfig.class);
        }
        handler.accept(after);
        storage.putValue(key, after);
        Arrays.stream(Env.values()).parallel().forEach(env -> StorageHelper.touchLastModifiedFlag(storage, env));
        return new Change<>(before, after);
    }

    /**
     * Update partial project configuration without locking, just validating the old values.
     *
     * @return The old projectConfig.
     */
    @Override
    @NonNull
    @Lock(key = "partialUpdateConfig:#{projectId}")
    public Change<ProjectConfig> partialUpdateConfig(long projectId, @NonNull NonNullConsumer<ProjectConfig> handler) {
        final ProjectModel project = getNeverNull(projectId);
        try {
            final String oldConfig = project.fetchRawConfig();

            // Build the config as ObjectNode rather than ProjectConfig.
            // Because some properties outside the ProjectConfig model might be contained in the JSON,
            // and which are should be preserved when the config updating.
            final ObjectNode receiver;
            if (Strings.isNotBlank(oldConfig)) {
                receiver = mapper.readValue(oldConfig, ObjectNode.class);
            } else {
                receiver = mapper.createObjectNode();
            }

            final ProjectConfig after = mapper.convertValue(receiver, ProjectConfig.class);

            handler.accept(after);

            // Copy each property from the "after" to "receiver".
            mapper.convertValue(after, ObjectNode.class).fields()
                .forEachRemaining(i -> receiver.set(i.getKey(), i.getValue()));

            // Serialized back to JSON.
            final String newConfig = mapper.writeValueAsString(receiver);
            if (!newConfig.equals(oldConfig)) {
                projectRepository.updateConfig(projectId, newConfig);
            }

            return new Change<>(project.getConfig(), after);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
    }

    @Override
    @NonNull
    public Map<ProjectType, Integer> countEachTypes() {
        return projectRepository.countEachTypes().stream()
            .collect(Collectors.toMap(i -> ProjectType.createFromOrdinal(i[0]), i -> i[1], Integer::sum));
    }

    @Nullable
    @Override
    public BuildCacheInfo.Map getBuildCacheStatus(long projectId) {
        return metaService.getValue(ProjectBuildCacheStatus, projectId, BuildCacheInfo.Map.class);
    }

    @Override
    @NonNull
    @Lock(key = "updateDescription:#{projectId}")
    public Change<String> updateDescription(long projectId, @NonNull String description) {
        final ProjectModel project = getNeverNull(projectId);
        projectRepository.updateDescription(projectId, EncodingUtils.encodeGolangJson(description));
        return new Change<>(project.getDescription(), description);
    }

    @Override
    @NonNull
    public DeployHookModel getDeployHookConfig(long projectId) {
        return metaService.getOptional(ProjectDeployHookConfiguration, projectId, DeployHookModel.class)
            .orElseGet(DeployHookModel::new);
    }

    @Override
    @NonNull
    public Change<DeployHookModel> updateDeployHookConfig(long projectId, @NonNull DeployHookModel data) {
        return metaService.setValue(ProjectDeployHookConfiguration, projectId, DeployHookModel.class, data);
    }

    @NonNull
    @Override
    public Map<String, Long> findAllProjects() {
        return projectRepository.findAllProjects().stream()
            .collect(Collectors.toMap(IdContentPair::getContent, IdContentPair::getId));
    }

    @Override
    @NonNull
    public long countByName(@NonNull String name) {
        return projectRepository.countByName(name);
    }

    @Override
    public int pullUsersFromLone(long projectId, @NonNull String appId) {

        // Find current users from Van database.
        CompletableFuture<Map<String, ProjectUserModel>> tVanRoleMap = supplyAsync(
            () -> userService.findProjectUsers(projectId)
                .collect(Collectors.toMap(ProjectUserModel::getUserUniqId, i -> i)));
        // Find current users form Lone.
        CompletableFuture<Map<String, RegionalInfoMap>> tLoneRoleMap = supplyAsync(
            () -> loneService.loadRegionalRoleMap(appId));
        // Wait them concurrently.
        Map<String, ProjectUserModel> vanRoleMap = tVanRoleMap.join();
        Map<String, RegionalInfoMap> loneRoleMap = tLoneRoleMap.join();

        // The uniqId loaded from Lone may not be in Van user table; it needs to pull from SSO first.
        Map<String, UserModel> loneUserMap = loneRoleMap.keySet().stream()
            // If a userUniqId can be found in the vanRoleMap,
            // it indicates that this userUniqId must be in Van user table, and does not need to be pulled here.
            .filter(i -> !vanRoleMap.containsKey(i))
            .collect(Collectors.collectingAndThen(Collectors.toSet(), userService::pullUserFromSsoIfNeeded));

        return Stream.of(vanRoleMap, loneUserMap).flatMap(i -> i.keySet().stream()).map(uniqId -> {
                ProjectUserModel pum = vanRoleMap.get(uniqId);
                RegionalInfoMap newRim = ofNullable(loneRoleMap.get(uniqId)).orElseGet(RegionalInfoMap::new);
                Long userId;
                ProjectUserMeta meta;
                // If the project user is already existed, update it if the RegionalInfoMap has changed.
                if (pum != null) {
                    meta = pum.getMeta();

                    boolean metaChanged = !newRim.deepEquals(meta.getRegionInfo());
                    boolean roleChanged = newRim.getMaxRoleValue().ordinal() > pum.getRole().ordinal();

                    // If the RegionalInfoMap has changed, or the new role is greater than the current,
                    // skip this, nothing should be updated.
                    if (!metaChanged && !roleChanged) return null;

                    userId = pum.getUserId();
                }
                // Otherwise, the project user is not existed yet, wrap it.
                else {
                    UserModel user = loneUserMap.get(uniqId);
                    // If the userUniqId is not in the vanRoleMap, it must be in the loneUserMap.
                    Objects.requireNonNull(user);
                    userId = user.getId();
                    meta = new ProjectUserMeta();
                }
                meta.setRegionInfo(newRim);
                return Pair.of(userId, meta);
            }).filter(Objects::nonNull)
            // Collect into a Map and batch update.
            .collect(Collectors.collectingAndThen(Collectors.toMap(Pair::getKey, Pair::getValue),
                map -> userService.batchUpdateProjectUsers(projectId, map)));

    }

    @Override
    public boolean validateName(@NonNull String name, @NonNull UserModel user) {
        if (!StringUtils.hasText(name)) {
            throw new VanBadRequestException("Project name can't be empty.");
        }
        String safeURLNameRegex = "^[a-zA-Z0-9][-\\w+]{1,40}[a-zA-Z0-9]$";
        String notSafeHostNameRegex = "-{2,}";
        if (!Pattern.matches(safeURLNameRegex, name) || Pattern.matches(notSafeHostNameRegex, name)) {
            throw new VanBadRequestException("Project name is invalid.");
        }
        long count = countByName(name);
        if (count > 0) {
            throw new VanBadRequestException("Project name already exists.");
        }
        if (!userService.isSuperAdmin(user.getUniqId())) {
            if (name.startsWith("test-") || name.endsWith("workers")) {
                throw new VanBadRequestException("Project name is invalid.");
            }
        }
        return true;
    }

    @Override
    public void validateAppId(@NonNull String appId, @NonNull UserModel user) {
        if (!StringUtils.hasText(appId)) {
            throw new VanBadRequestException("appId can't be empty.");
        }
        String appIdRegex = "^[a-zA-Z][a-zA-Z\\-\\d]*[a-zA-Z\\d]$";
        if (!Pattern.matches(appIdRegex, appId)) {
            throw new VanBadRequestException("appId is invalid.");
        }
        // super admin can pass
        boolean isSuperAdmin = userService.isSuperAdmin(user.getUniqId());
        if (isSuperAdmin) {
            return;
        }
        // NOTE: no test role check in go, but it does in `isParticipant`
        boolean hasPermission = loneService.isParticipant(appId, user);
        if (!hasPermission) {
            throw new ForbiddenException(
                String.format(
                    "You don't have appId %s permission, please find appid master to apply for the role of the person in charge",
                    appId)
            );
        }
    }

    private boolean checkRepositoryInGitlab(@NonNull String repositoryPath) {
        try {
            // NOTE: if project not found, it should throw exception, so we return false
            GitlabProject gitlabProject = gitlabService.getProjectInfo(repositoryPath);
            return true;
        } catch (Exception e) {
            LOGGER.error("gitlab getProject error, ", e);
        }
        return false;
    }

    @Override
    public boolean validateRepository(@NonNull String link, @NonNull UserModel user) {
        if (!StringUtils.hasText(link)) {
            throw new VanBadRequestException("Git repository can't be empty.");
        }
        if (!link.startsWith("https://") && !link.startsWith("http://") && !link.startsWith("ssh://")) {
            throw new VanBadRequestException("Git repository protocol is invalid.");
        }
        String repositoryPath = ProjectUtils.repositoryPath(link);
        if (repositoryPath.isEmpty()) {
            throw new VanBadRequestException("Git repository is invalid.");
        }
        if (!checkRepositoryInGitlab(repositoryPath)) {
            throw new VanBadRequestException("Git repository does not exist.");
        }
        // super admin can pass, otherwise check dev permission
        if (userService.isSuperAdmin(user.getUniqId())) {
            return true;
        }
        boolean hasDevPermission;
        hasDevPermission = gitlabService.hasProjectDevPermission(repositoryPath, user.getEmail());
        if (!hasDevPermission) {
            // check other formats
            hasDevPermission = Arrays.stream(emailFormats)
                    .anyMatch(ef -> gitlabService.hasProjectDevPermission(
                            repositoryPath,
                            user.getEmail().replaceFirst(ef[0], ef[1])));
        }
        if (!hasDevPermission) {
            throw new VanBadRequestException("Git repository is inaccessible.");
        }
        return true;
    }

    private boolean isOfficialBoilerplate(GitlabProject project) {
        List<String> tags = project.getTagList() != null ? project.getTagList() : Collections.emptyList();
        return tags.stream().anyMatch(tag -> tag.equalsIgnoreCase("official"));
    }

    @Override
    @NonNull
    public List<ProjectBoilerplate> getBoilerplateList(@NonNull String type, @NonNull int groupId) {
        Stream<GitlabProject> stream = gitlabService.getGroupProjects(groupId);
        return Optional.ofNullable(stream).orElse(Stream.empty())
                .filter(project -> project.getName().startsWith(String.format("boilerplate-%s", type)))
                .sorted((a, b) -> {
                    if (isOfficialBoilerplate(a) && !isOfficialBoilerplate(b)) {
                        return -1;
                    } else if (a.equals(b)) {
                        return 0;
                    }
                    return 1;
                }).map(project -> new ProjectBoilerplate(
                        project.getId(),
                        project.getName(),
                        project.getDescription(),
                        project.getTagList()))
                .collect(Collectors.toList());
    }

    @Override
    public void createMiniprogramMeta(@NonNull Long metaId, @NonNull MiniprogramInfoModel miniprogramInfoModel) {
        MetaEntity metaEntity = new MetaEntity();
        metaEntity.setMetaId(metaId);
        metaEntity.setType(MetaType.MiniprogramInfo);
        try {
            metaEntity.setMeta(mapper.writeValueAsString(miniprogramInfoModel));
        } catch (JsonProcessingException e) {
            LOGGER.error("failed to serialize miniprogram meta", e);
            throw new VanBadRequestException("miniprogram meta is invalid.");
        }
        metaService.create(metaEntity);
    }

    @NonNull
    @Override
    public Long create(@NonNull  ProjectCreateParams params, @NonNull UserModel user) {
        // validate name
        validateName(params.getName(), user);
        // validate appId
        String appId = params.getConfig().getAppId();
        validateAppId(appId, user);

        boolean autoCreate = false;
        if (!StringUtils.hasText(params.getRepositoryUrl())) {
            // auto create
            autoCreate = true;
            String repositoryUrl = String.join("/", gitlabUrl, gitlabGroup, params.getName());
            params.setRepositoryUrl(repositoryUrl);
            // auto create so the repository should not exist
            String repositoryPath = ProjectUtils.repositoryPath(repositoryUrl);
            if (gitlabService.repositoryAlreadyExists(repositoryPath)) {
                throw new VanBadRequestException("Git repository already exists.");
            }
            params.setRepository(repositoryPath);
        } else {
            // create manually, verify the repository
            validateRepository(params.getRepositoryUrl(), user);
            String repositoryPath = ProjectUtils.repositoryPath(params.getRepositoryUrl());
            params.setRepository(repositoryPath);
        }
        // handle miniprogram project type
        if (params.getType() == ProjectType.Miniprogram) {
            if (params.getMiniprogramMeta() == null
                    || Objects.equals(params.getMiniprogramMeta().getAppId(), "")
                    || Objects.equals(params.getMiniprogramMeta().getPrivateKey(), "")
                    || params.getMiniprogramMeta().getType() == MiniprogramInfoModel.Type.Unknown) {
                throw new VanBadRequestException("miniprogram meta is invalid.");
            }
        }
        // save project
        ProjectEntity entity = new ProjectEntity();
        entity.setName(params.getName());
        entity.setType(params.getType());
        try {
            String rawConfig = mapper.writeValueAsString(params.getConfig());
            entity.setConfig(rawConfig);
        } catch (JsonProcessingException e) {
            throw new VanBadRequestException("Project config is invalid.");
        }
        entity.setRepository(params.getRepository());
        if (params.getDescription() != null) {
            entity.setDescription(EncodingUtils.encodeGolangJson(params.getDescription()));
        }

        long projectId = projectRepository.save(entity).getId();

        // create miniprogram meta
        if (params.getType() == ProjectType.Miniprogram && params.getMiniprogramMeta() != null) {
            createMiniprogramMeta(projectId, params.getMiniprogramMeta());
        }

        // add gitlab hook if repo doesn't have when create manually
        String repo = params.getRepository();
        if (!autoCreate && repo != null) {
            boolean hasWebhook = false;
            try {
                hasWebhook = gitlabService.getProjectWebhooks(repo).stream()
                        .map(GitlabWebhook::getUrl)
                        .anyMatch(url -> url.equals(gitlabWebhookUrl));
            } catch (Exception e) {
                LOGGER.error("failed to get project webhooks for repo: {}", repo, e);
            }
            if (!hasWebhook) {
                try {
                    gitlabService.addProjectWebhook(repo, gitlabWebhookUrl);
                } catch (Exception e) {
                    LOGGER.error("failed to add project webhook for repo: {}", repo, e);
                }
            }
        }
        return projectId;
    }

    @Override
    public int archive(long projectId) {
        ProjectModel project = getNeverNull(projectId);
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return projectRepository.updateRepository(project.getId(), "", now);
    }

    @NonNull
    @Override
    public ResponseEntity<String> getRawFile(@NonNull long projectId, @NonNull String path, @NonNull String ref) {
        ProjectModel project = getNeverNull(projectId);
        String bh = ref;
        // if none, use default branch
        if (!StringUtils.hasText(ref)) {
            GitlabProject gitlabProject = gitlabService.getProjectInfo(project.getRepository());
            bh = gitlabProject.getDefaultBranch();
        }
        byte[] data = gitlabService.getRepositoryRawFile(project.getRepository(), path, bh);
        if (data != null) {
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_TYPE, "text/plain;charset=UTF-8");
            return new ResponseEntity<>(new String(data, StandardCharsets.UTF_8), headers, HttpStatus.OK);
        } else {
            LOGGER.error("failed to fetch file with path: {}, ref: {}, project: {}", path, ref, project.getId());
            throw new InternalRequestException("failed to fetch resource");
        }
    }

    @NonNull
    @Override
    public String applyDomain(
            @NonNull ProjectModel project,
            @NonNull UserModel user,
            @NonNull String domainValue,
            @NonNull String domainType,
            String remark) {

        String publicAccessValue = ProjectUtils.isPublicDomain(domainValue) ? "ky0vuzfu-3ffck1jqvxh-0"
                : "ky0vuzg2-spfy49xlgrh-1";

        Map<String, String> words = new HashMap<>();
        words.put("van", "由 Van 提供");
        words.put("customize", "由用户自定义");

        ApprovalForm[] forms = null;
        if (!StringUtils.hasText(approvalForms)) {
            // set default
            forms = new ApprovalForm[] {
                    new ApprovalForm("widget16194952816110001", "radioV2", "knzho4fq-xe2z5arx6j-1"),
                    new ApprovalForm("widget16194953391670001", "input", ""),
                    new ApprovalForm("widget16194958454080001", "textarea", ""),
                    new ApprovalForm("widget16413473950500001", "radioV2", publicAccessValue),
            };
        } else {
            try {
                forms = mapper.readValue(approvalForms, ApprovalForm[].class);
            } catch (JsonProcessingException e) {
                LOGGER.error("apply_domain failed to parse approval forms", e);
                throw new InternalRequestException("parse approval forms error, " + e.getMessage());
            }
        }

        if (forms.length < 3) {
            LOGGER.error("apply_domain forms {}", Arrays.toString(forms));
            throw new InternalRequestException(
                    "apply_domain has wrong approval form length, expect 3 but got " + forms.length);
        }

        forms[1].setValue(domainValue);
        forms[2].setValue(String.format("申请 %s 在 Van 的生产入口；\n系统提示：【%s】； \n用户备注：%s", project.getName(),
                words.get(domainType), remark));

        User feishuUser = feishuService.getUserInfo(user.getUniqId());
        if (feishuUser == null || feishuUser.getUserId() == null) {
            throw new InternalRequestException("user not found in lark");
        }
        String code = huolalaFeishuEnhanceService.createApproval(approvalDomainCode, feishuUser.getUserId(), forms);
        // send feishu card to user
        FeishuCard card = FeishuUtils.buildDomainApprovalCard(code, project.getName());
        feishuService.sendCardToUser(card, user.getUniqId());

        CompletableFuture.runAsync(() -> {
            // add user log
            Map<String, String> metaMap = new HashMap<>();
            metaMap.put("code", code);
            userLogService.addLog(user.getId(), project.getId(), UserLogType.APPLY_ENTRYPOINT, "", metaMap);
        });
        return code;
    }

    @NonNull
    @Override
    public String mergeRequest(
            @NonNull ProjectModel project,
            @NonNull UserModel user,
            @NonNull long iid,
            @Nullable MergeMrParams params) {
        if (!gitlabService.checkUserMergePermission(project.getRepository(), iid, user.getUniqId())) {
            throw new InternalException("You do not have permission to merge this MR");
        }
        String commitMessage = "";
        boolean shouldRemoveSource;
        if (params != null) {
            commitMessage = params.getMergeCommitMessage();
            shouldRemoveSource = params.isShouldRemoveSourceBranch();
        } else {
            shouldRemoveSource = false;
        }
        if (commitMessage == null) {
            commitMessage = "";
        }
        String mergeCommitMessage = commitMessage
                + String.format("\n\n merged by @%s via Van\n", user.getUniqId());
        GitlabMergeRequest mr = gitlabService.mergeMergeRequest(project.getRepository(), iid, mergeCommitMessage,
                shouldRemoveSource);

        // note copied from go
        // NOTE: merge_request 创建时，如果没有设置 remove_branch 的话，
        // 即便是合并时，设置了 should_remove_source_branch 也不生效。手动删除分支。
        if (shouldRemoveSource) {
            try {
                GitlabBranch branch = gitlabService.getBranchInfo(project.getRepository(), mr.getSourceBranch());
                if (branch != null) {
                    gitlabService.deleteBranch(project.getRepository(), branch.getName());
                }
            } catch (InternalRequestException e) {
                // ignore 404 not found exception
                if (e.getStatus() != 404) {
                    throw new InternalRequestException(e);
                }
            }
        }

        CompletableFuture.runAsync(() -> {
            // add user log
            Map<String, String> meta = new HashMap<>();
            meta.put("iid", String.valueOf(iid));
            meta.put("remove", String.valueOf(shouldRemoveSource));
            userLogService.addLog(user.getId(), project.getId(), UserLogType.WORKFLOW_MERGE, "", meta);
        });

        return "success";
    }

    @NonNull
    @Override
    public MetaModel getWorkerPublishInfo(@NonNull long projectId, @NonNull long taskId) {
        ProjectModel project = getNeverNull(projectId);
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);

        if (task.getType() != BuildTaskType.NormalBuild) {
            throw new InternalRequestException("not support publish as sdk");
        }
        if (task.getStatus() != BuildTaskStatus.Done) {
            throw new InternalRequestException("could not publish");
        }
        MetaModel metaModel = metaService.get(taskId, MetaType.WorkersSDKPublished).orElse(null);
        if (metaModel == null) {
            // check package.json file
            String filekey = String.format("%s/%s/.sdk/package.json", project.getName(), String.valueOf(taskId));
            WorkerPackageJson pkg = Region.defaultStorage().getOptionalValue(filekey, WorkerPackageJson.class).orElse(null);
            if (pkg == null) {
                throw new InternalRequestException("missing sdk");
            }
            if (pkg.getVanSdkPkgHash() == null || pkg.getVanSdkPkgHash().isEmpty()) {
                throw new InternalRequestException("missing sdk hash");
            }
            throw new ResourceNotFoundException("sdk not found");
        }
        return metaModel;
    }

    @NonNull
    @Override
    public long publishWorker(@NonNull long projectId, @NonNull long taskId, @NonNull UserModel user) {
        ProjectModel project = getNeverNull(projectId);
        if (project.getType() != ProjectType.Workers) {
            throw new InternalRequestException("project type not support");
        }
        BuildTaskModel task = buildTaskService.getNeverNull(projectId, taskId);
        if (task.getStatus() != BuildTaskStatus.Done) {
            throw new InternalRequestException("cannot publish when task is not done");
        }
        MetaModel metaModel = metaService.get(taskId, MetaType.WorkersSDKPublished).orElse(null);
        if (metaModel != null) {
            throw new InternalRequestException("task already published");
        }
        MetaEntity entity = new MetaEntity(MetaType.WorkersSDKPublished, taskId);
        Map<String, String> m = new HashMap<>();
        m.put("name", user.getName());
        m.put("username", user.getUniqId());
        try {
            entity.setMeta(mapper.writeValueAsString(m));
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
        return metaService.create(entity);
    }

    @Override
    @Lock(key = "updateRepository:#{projectId}")
    public Change<String> updateRepository(@NonNull long projectId, @NonNull String repository) {
        ProjectModel project = getNeverNull(projectId);
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        projectRepository.updateRepository(projectId, repository, now);
        return new Change<>(project.getRepository(), repository);
    }
}
