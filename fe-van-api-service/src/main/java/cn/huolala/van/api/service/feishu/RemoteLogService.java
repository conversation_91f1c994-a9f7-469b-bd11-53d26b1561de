package cn.huolala.van.api.service.feishu;

import org.apache.logging.log4j.Level;

import java.util.Map;

/**
 * 将日志发送给远方服务
 */
public interface RemoteLogService {
    void log(Level level, String message);

    void log(Level level, String message, Map<String, String> tags);

    void log(Level level, String message, Throwable e);

    void log(Level level, String message, Map<String, String> tags, Throwable e);

    void info(String message);

    void info(String message, Map<String, String> tags);

    void error(String message);

    void error(String message, Map<String, String> tags);

    void error(String message, Throwable e);

    void error(String message, Map<String, String> tags, Throwable e);
}
