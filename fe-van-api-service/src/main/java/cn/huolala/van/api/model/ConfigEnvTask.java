package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.Env;
import lombok.Getter;
import org.springframework.lang.NonNull;

public class ConfigEnvTask {
    @NonNull
    @Getter
    private final Long taskId;

    @NonNull
    private final ConfigEnv env;

    public ConfigEnvTask(@NonNull ConfigEnv env, @NonNull Long taskId) {
        this.env = env;
        this.taskId = taskId;
    }

    @NonNull
    public Env getName() {
        return env.getName();
    }

    @NonNull
    public String getStableName() {
        return env.getStableName();
    }
}
