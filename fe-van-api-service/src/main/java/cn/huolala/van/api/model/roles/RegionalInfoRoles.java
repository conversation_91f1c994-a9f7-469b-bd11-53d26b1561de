package cn.huolala.van.api.model.roles;

import cn.huolala.van.api.dao.enums.Role;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Stream;

public class RegionalInfoRoles {
    @NonNull
    private final Set<Role> collection;

    public RegionalInfoRoles() {
        collection = new TreeSet<>(Comparator.comparingInt(Enum::ordinal));
    }

    @JsonCreator
    public RegionalInfoRoles(List<String> rawList) {
        this();
        rawList.stream().map(Role::valueOf).forEach(this::add);
    }

    public boolean notEmpty() {
        return !collection.isEmpty();
    }

    @CanIgnoreReturnValue
    public boolean add(Role e) {
        if (e == null || e == Role.NoRole) return false;
        return collection.add(e);
    }

    public void addAll(RegionalInfoRoles roles) {
        if (roles != null) collection.addAll(roles.collection);
    }

    public Stream<Role> stream() {
        return collection.stream();
    }

    @JsonValue
    public Set<Role> jsonValue() {
        return collection;
    }

    public Role getMaxRoleValue() {
        return collection.stream().reduce(Role.NoRole, (a, i) -> a.ordinal() > i.ordinal() ? a : i);
    }

    public boolean deepEquals(@Nullable RegionalInfoRoles roles) {
        if (roles == null) return false;
        return collection.equals(roles.collection);
    }
}
