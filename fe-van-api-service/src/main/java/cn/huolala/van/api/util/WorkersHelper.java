package cn.huolala.van.api.util;

import cn.huolala.van.api.exception.InternalRequestException;
import cn.lalaframework.soa.annotation.SOAContext;
import cn.lalaframework.soa.annotation.SOAParamsMethod;
import cn.lalaframework.soa.generic.SOAGenericService;
import cn.lalaframework.soa.generic.SOAMethodAttrs;
import org.springframework.lang.NonNull;

import static cn.lalaframework.soa.annotation.SOAParamsMode.BODY_JSON;
import static cn.lalaframework.soa.annotation.SOAResultMode.JSON_OBJECT;
import static cn.lalaframework.soa.generic.SOAGenericService.getService;

public class WorkersHelper {
    private WorkersHelper() {
    }

    @NonNull
    public static <R> R invoke(@NonNull String serviceName,
                               @NonNull String methodName,
                               @NonNull Object params,
                               @NonNull Integer timeoutInMs,
                               @NonNull Class<R> resultType) throws InternalRequestException {
        try {
            SOAMethodAttrs soaMethodAttrs = new SOAMethodAttrs(methodName, BODY_JSON, JSON_OBJECT);
            soaMethodAttrs.setParamsMethod(SOAParamsMethod.POST);
            soaMethodAttrs.setReadTimeoutMillis(timeoutInMs);
            SOAGenericService soaGenericService = getService("fe-van-workers-svc", "");
            SOAContext.getHeader().put("X-Van-Project", serviceName);
            return soaGenericService.call(resultType, soaMethodAttrs, (R) null, params);
        } catch (Throwable e) {
            throw new InternalRequestException(e);
        } finally {
            SOAContext.clearAll();
        }
    }
}
