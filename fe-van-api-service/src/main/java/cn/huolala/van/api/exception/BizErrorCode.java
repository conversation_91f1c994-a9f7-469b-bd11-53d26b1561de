package cn.huolala.van.api.exception;

import java.util.Objects;

/**
 * <AUTHOR>
 * @createTime 2020-07-27 4:41 PM
 * @description 业务的错误码
 */
public enum BizErrorCode {
    /**
     * 这个是默认的错误
     */
    SUCCESS(0, "svc.success", "success"),
    SYS_FATAL_ERR(9900, "svc.sys_software_err", "sever software  err"),
    SERVER_ERR(9901, "svc.sever_logic_err", "sever logic err"),
    CALL_API_ERR(9901, "svc.call_api_err", "call api error"),
    JSON_ENCODING_ERR(9902, "svc.json_codec_err", "json codec err"),
    REQUEST_BODY_NONE(9903, "svc.request_body_none", "request body null"),

    PARAMETER_ERR(9909, "svc.parameter.err", "parameter.err"),

    USER_AUTH_FAILED(4001, "svc.auth_failed", "user auth failed"),
    USER_NO_PERMISSION(4003, "svc.no_permission", "no permission"),

    DEMO_PARAM_ERROR(10001, "svc.demo_param_error", "demo param error");

    private final Integer ret;
    private final String code;
    private final String msg;

    BizErrorCode(Integer ret, String code, String msg) {
        this.ret = ret;
        this.code = code;
        this.msg = msg;
    }

    public Integer getRet() {
        return ret;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public BizException getException() {
        return new BizException(this.getRet(), this.getCode(), this.getMsg());
    }

    public BizException getException(String msg) {
        return new BizException(this.getRet(), this.getCode(), msg);
    }

    public BizException getException(Object data) {
        return new BizException(this.getRet(), this.getCode(), this.getMsg(), Objects.toString(data, ""));
    }
}
