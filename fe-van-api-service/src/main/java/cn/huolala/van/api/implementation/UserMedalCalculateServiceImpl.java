package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.dao.entity.UserEntity;
import cn.huolala.van.api.dao.entity.UserLogEntity;
import cn.huolala.van.api.dao.entity.UserMedalEntity;
import cn.huolala.van.api.dao.repository.UserLogRepository;
import cn.huolala.van.api.dao.repository.UserMedalRepository;
import cn.huolala.van.api.dao.repository.UserRepository;
import cn.huolala.van.api.model.feishu.MessageCardColumnSet;
import cn.huolala.van.api.service.MedalReceiveDecideService;
import cn.huolala.van.api.service.UserMedalCalculateService;
import cn.huolala.van.api.service.feishu.HuolalaFeishuEnhanceService;
import cn.huolala.van.api.service.feishu.RemoteLogService;
import com.lark.oapi.Client;
import com.lark.oapi.card.enums.MessageCardHeaderTemplateEnum;
import com.lark.oapi.card.model.*;
import com.lark.oapi.service.contact.v3.enums.GetUserUserIdTypeEnum;
import com.lark.oapi.service.contact.v3.model.GetUserReq;
import com.lark.oapi.service.contact.v3.model.GetUserResp;
import com.lark.oapi.service.im.v1.enums.CreateMessageReceiveIdTypeEnum;
import com.lark.oapi.service.im.v1.enums.MsgTypeEnum;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageReqBody;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Log4j2
public class UserMedalCalculateServiceImpl implements UserMedalCalculateService {
    private static final String HONOR_URL = "https://van.huolala.work/achievements";
    /**
     * 一个勋章，可以由多个操作日志组成，多个日志是或的关系
     */
    private final Map<MedalEnum, Set<UserLogType>> oneToOneMap = new HashMap<>();
    // 一个勋章，需要由多个操作日志组成，多个日志是与的关系
    private final Map<MedalEnum, Set<UserLogType>> oneToManyMap = new HashMap<>();
    private final Map<MedalEnum, String> medalFeishuImageKeyMap = new HashMap<>();
    @Resource(name = "van-workers")
    private RedisTemplate<String, String> keyValueRedisTemplate;
    @Value("${medal.notify.username:}")
    private List<String> allowNotifyUsernames;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserMedalRepository userMedalRepository;
    @Autowired
    private UserLogRepository userLogRepository;
    // 用户处理具备特殊逻辑的勋章
    @Autowired
    private List<MedalReceiveDecideService> medalReceiveDecideServices;
    @Autowired
    private HuolalaFeishuEnhanceService huolalaFeishuEnhanceService;
    @Autowired
    private RemoteLogService remoteLogService;
    @Resource(name = "van")
    private Client feishuClient;

    @SafeVarargs
    static private <T> Set<T> toSet(T... values) {
        return Stream.of(values).collect(Collectors.toSet());
    }

    @Value("${medal.feishu.img_key:}")
    public void setMedalFeishuImageKeyMap(String value) {
        if (StringUtils.isBlank(value)) {
            return;
        }
        String[] lines = value.split("\n");
        for (String s : lines) {
            String[] fields = s.trim().split(" ");
            if (fields.length < 2) {
                continue;
            }
            String key = fields[0].trim();
            String imageKey = fields[fields.length - 1].trim();
            medalFeishuImageKeyMap.put(MedalEnum.valueOf(key), imageKey);
        }
    }

    @PostConstruct
    private void initMap() {

        // one to one
        oneToOneMap.put(MedalEnum.COMPOSITE_BRANCH, toSet(UserLogType.COMPOSITE_BUILD));
        oneToOneMap.put(MedalEnum.CANARY_RELEASE, toSet(UserLogType.CANARY_DEPLOY));
        oneToOneMap.put(MedalEnum.LARK_RELEASE, toSet(UserLogType.FEISHU_DEPLOY_DEV, UserLogType.FEISHU_DEPLOY_PROD));
        oneToOneMap.put(MedalEnum.ROLLBACK_RELEASE, toSet(UserLogType.ROLLBACK_DEPLOY, UserLogType.FEISHU_ROLLBACK));
        oneToOneMap.put(MedalEnum.WATERMARK_SERVICE, toSet(UserLogType.SERVICE_WATERMARK));
        oneToOneMap.put(MedalEnum.SSO_SERVICE, toSet(UserLogType.SERVICE_SSO_GUARD, UserLogType.SERVICE_SSO_INJECT));
        oneToOneMap.put(MedalEnum.SWITCH_SERVICE, toSet(UserLogType.SERVICE_VERSION_SWITCH));
        oneToOneMap.put(MedalEnum.TUNNEL_SERVICE, toSet(UserLogType.SERVICE_TUNNEL));
        oneToOneMap.put(MedalEnum.VCONSOLE_SERVICE, toSet(UserLogType.SERVICE_VCONSOLE));
        oneToOneMap.put(MedalEnum.OFFLINE_PACKAGE, toSet(UserLogType.SERVICE_OFFWEB));
        oneToOneMap.put(MedalEnum.MANUAL_BUILD, toSet(UserLogType.MANUAL_BUILD));
        oneToOneMap.put(MedalEnum.BUILD_CACHE, toSet(UserLogType.BUILD_CACHE));
        oneToOneMap.put(MedalEnum.ONECLICK_RELEASE, toSet(UserLogType.ONE_KEY_RELEASE));
        oneToOneMap.put(MedalEnum.LIBRARY_RELEASE, toSet(UserLogType.NPM_RELEASE));
        oneToOneMap.put(MedalEnum.WORKERS_RELEASE, toSet(UserLogType.WORKERS_RELEASE));
        oneToOneMap.put(MedalEnum.WORKFLOW, toSet(UserLogType.WORKFLOW_CREATE, UserLogType.WORKFLOW_MERGE, UserLogType.WORKFLOW_REBASE));
        oneToOneMap.put(MedalEnum.MINIPROGRAM_RELEASE, toSet(UserLogType.MINIPROGRAM_PREVIEW, UserLogType.MINIPROGRAM_UPLOAD));
        oneToOneMap.put(MedalEnum.AUTO_TRACK, toSet(UserLogType.AUTO_TRACE_SERVICE));
        oneToOneMap.put(MedalEnum.USER_TRACE, toSet(UserLogType.USER_TRACE_SERVICE));
        oneToOneMap.put(MedalEnum.WINDOW_APPLY, toSet(UserLogType.APPLY_WINDOW));
        oneToOneMap.put(MedalEnum.RELEASE_PLAN, toSet(UserLogType.RELEASE_WITH_PDM_PLAN));
        oneToOneMap.put(MedalEnum.RELEASE_WITH_MOBILE, toSet(UserLogType.FEISHU_DEPLOY_PROD));
        oneToOneMap.put(MedalEnum.AI_LOG_ANALYSIS, toSet(UserLogType.AI_LOG_ANALYSIS));

        // one to many
        oneToManyMap.put(MedalEnum.PROJECT_RELEASE, toSet(UserLogType.DEPLOY_PROD, UserLogType.CANARY_DEPLOY, UserLogType.FEISHU_DEPLOY_PROD));
        oneToManyMap.put(MedalEnum.ALL_ROUNDER, toSet( // 下面几条都做了才能获得，堪称史上最强勋章
                UserLogType.NPM_RELEASE,
                UserLogType.WORKERS_RELEASE,
                UserLogType.DEPLOY_PROD,
                UserLogType.MINIPROGRAM_UPLOAD,
                UserLogType.MINIPROGRAM_PREVIEW
        ));

    }

    /**
     * Get user medals created before the specified timestamp.
     */
    private List<UserMedalEntity> listUserMedal(Long userId, LocalDateTime before) {
        return userMedalRepository.findByUserIdAndCreatedAtBeforeOrderByCreatedAtDesc(userId, before);
    }

    private void addMedal(UserEntity user, MedalEnum medalEnum, LocalDateTime receivedTime) {
        addMedal(user, medalEnum, receivedTime, null);
    }

    private void addMedal(UserEntity user, MedalEnum medalEnum, LocalDateTime receivedTime, String remark) {
        log.debug("用户 {} ({}) 获得勋章 {}", user.getUniqId(), user.getId(), medalEnum);
        UserMedalEntity userMedalEntity = new UserMedalEntity();
        userMedalEntity.setUserId(user.getId());
        userMedalEntity.setUniqName(medalEnum);
        // 需要减去一分钟，因为可能会失败重试，需要根据时间戳来判断是否已经领取过勋章
        userMedalEntity.setUpdatedAt(receivedTime.minusMinutes(1));
        userMedalEntity.setCreatedAt(receivedTime.minusMinutes(1));
        userMedalEntity.setRemark(remark == null ? "" : remark);
        userMedalRepository.save(userMedalEntity);
    }

    /**
     * 计算一对一的勋章，一条操作记录就能对应一条的勋章
     *
     * @param user userEntity
     * @param end  endTime
     */
    private void calculateOneToOneUserMedal(UserEntity user, LocalDateTime end, Set<MedalEnum> receivedMedal) {
        for (Map.Entry<MedalEnum, Set<UserLogType>> entry : oneToOneMap.entrySet()) {
            MedalEnum medalEnum = entry.getKey();
            Set<UserLogType> userLogTypes = entry.getValue();

            if (receivedMedal.contains(medalEnum)) {
                continue;
            }

            List<UserLogEntity> byUserIdAndTypeInAndUpdatedAtIsBefore = userLogRepository.findByUserIdAndTypeInAndUpdatedAtIsBefore(user.getId(), userLogTypes, end);
            if (byUserIdAndTypeInAndUpdatedAtIsBefore.size() == 0) {
                continue;
            }
            addMedal(user, medalEnum, end);
        }
    }

    private void calculateOneToManyUserMedal(UserEntity user, LocalDateTime end, Set<MedalEnum> receivedMedal) {
        for (Map.Entry<MedalEnum, Set<UserLogType>> entry : oneToManyMap.entrySet()) {
            MedalEnum medalEnum = entry.getKey();
            Set<UserLogType> userLogTypes = entry.getValue();

            if (receivedMedal.contains(medalEnum)) {
                continue;
            }

            List<UserLogEntity> byUserIdAndTypeInAndUpdatedAtIsBefore = userLogRepository.findByUserIdAndTypeInAndUpdatedAtIsBefore(user.getId(), userLogTypes, end);
            // 必须要所有的 user log 都存在，才能领取勋章
            if (byUserIdAndTypeInAndUpdatedAtIsBefore.size() != userLogTypes.size()) {
                continue;
            }
            addMedal(user, medalEnum, end);
        }
    }

    private Integer getUserJoinTime(String uniqId, String larkId) {
        log.debug("用户：{}, larkId: {}", uniqId, larkId);
        try {
            String key = "van:user:lark:joinTime:" + uniqId;
            String joinTimeCache = keyValueRedisTemplate.opsForValue().get(key);
            if (joinTimeCache != null && !joinTimeCache.isEmpty()) {
                // hit cache
                log.info("user: {}, joinTime: {} from cache", uniqId, joinTimeCache);
                return Integer.valueOf(joinTimeCache);
            }
            // no cache
            GetUserReq req = GetUserReq.newBuilder().userId(larkId).userIdType(GetUserUserIdTypeEnum.USER_ID).build();
            GetUserResp resp = feishuClient.contact().user().get(req);
            if (resp.success()) {
                Integer joinTime = resp.getData().getUser().getJoinTime();
                // save cache
                keyValueRedisTemplate.opsForValue().set(key, String.valueOf(joinTime));
                log.debug("user: {}, joinTime: {} save cache", uniqId, joinTime);
                return joinTime;
            }
            log.info("failed to getJoinTime, code: {}, msg: {}", resp.getCode(), resp.getMsg());
            return 0;
        } catch (Exception e) {
            log.info("user: {}, failed to getJoinTime, error: {}", uniqId, e);
            return 0;
        }
    }

    private void calculateUserAnniversaryMedal(UserEntity user) {
        String uniqName = user.getUniqId();
        log.debug("开始计算用户:{}, 入职周年勋章", uniqName);
        try {
            // NOTE: 这里选择一个一个查，原因看方法内
            String larkId = getLarkUserIdByUniqname(uniqName);
            if (larkId.isEmpty()) {
                return;
            }
            // 从lark获取用户入职时间来计算入职周年勋章
            Integer joinTime = getUserJoinTime(uniqName, larkId);
            log.debug("用户:{}, 飞书入职时间(s):{}", uniqName, joinTime);
            if (joinTime > 0) {
                LocalDate currentDate = LocalDate.now();
                LocalDate entryDate = Instant.ofEpochSecond(joinTime).atZone(ZoneId.systemDefault()).toLocalDate();
                // 入职年份
                int[] gutter = {1, 3, 5, 7, 10};
                MedalEnum[] medals = {
                        MedalEnum.HLL_ONE_YEAR,
                        MedalEnum.HLL_THREE_YEAR,
                        MedalEnum.HLL_FIVE_YEAR,
                        MedalEnum.HLL_SEVEN_YEAR,
                        MedalEnum.HLL_X_YEAR
                };

                List<UserMedalEntity> userMedalEntities = listUserMedal(user.getId(), getPreviousDateTime());

                Set<MedalEnum> receivedMedal = userMedalEntities.stream().map(UserMedalEntity::getUniqName)
                        .collect(Collectors.toSet());

                for (int i = 0; i < gutter.length; i++) {
                    LocalDate addEntryDate = entryDate.plusYears(gutter[i]);
                    LocalDateTime endTime = addEntryDate.atTime(0, 0, 0, 0);
                    if (!receivedMedal.contains(medals[i])) {
                        if (addEntryDate.isBefore(currentDate) || addEntryDate.isEqual(currentDate)) {
                            // NOTE: 时间取入职周年那天时间，可以保证不发送通知
                            // NOTE: 这里不加log记录了，没有绑定的project_id等因素
                            addMedal(user, medals[i], endTime);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.info("user: {}, failed to calculate anniversary medals", uniqName);
        }
    }

    private String getLarkUserIdByUniqname(String uniqName) {
        // NOTE: 如果用户已经离职，下面接口会报错，这种情况直接忽略
        List<String> uniqNames = Collections.singletonList(uniqName);
        try {
            log.info("获取用户：{}的larkId", uniqName);
            Map<String, String> userMap = huolalaFeishuEnhanceService.larkUserIdInUniqNames(uniqNames);
            return userMap.get(uniqName);
        } catch (Exception e) {
            log.info("获取用户: {}的larkId发生错误: {}", uniqName, e);
            return "";
        }
    }

    private void calculateUserMedal(UserEntity user, LocalDateTime endTime) {

        // 先获取用户 endTime 之前所有的勋章
        List<UserMedalEntity> userMedalEntities = listUserMedal(user.getId(), endTime);

        Set<MedalEnum> receivedMedal = userMedalEntities.stream().map(UserMedalEntity::getUniqName).collect(Collectors.toSet());

        calculateOneToOneUserMedal(user, endTime, receivedMedal);
        calculateOneToManyUserMedal(user, endTime, receivedMedal);

        for (MedalReceiveDecideService receiveDecideService : medalReceiveDecideServices) {
            if (receivedMedal.contains(receiveDecideService.medal())) {
                continue;
            }
            if (!receiveDecideService.receive(user, endTime)) {
                continue;
            }
            addMedal(user, receiveDecideService.medal(), endTime);
        }

    }

    // 获取今天零点的时间
    private LocalDateTime getPreviousDateTime() {
        LocalDateTime now = LocalDateTime.now();
        return now.withHour(0).withMinute(0).withSecond(0).withNano(0);
    }

    @Override
    public void calculate() {
        // 找到所有拥有项目的 user，所有勋章都是基于项目唯独的
        List<UserEntity> allProjectUser = userRepository.findAllProjectUser();
        for (UserEntity userEntity : allProjectUser) {
            calculateUserMedal(userEntity, getPreviousDateTime());
            calculateUserAnniversaryMedal(userEntity);
        }
    }

    @Override
    @Async
    public void calculateInBackGround() {
        try {
            calculate();
        } catch (Exception e) {
            remoteLogService.error("计算勋章失败", e);
        }
    }

    @Async
    public void notifyUserYesterdayMedal() {
        LocalDateTime yesterday = getPreviousDateTime().minusDays(1);
        notifyUserMedalInDate(yesterday, getPreviousDateTime(), "昨天");
    }

    private void notifyUserMedalInDate(LocalDateTime start, LocalDateTime end, String dateTitle) {
        try {
            // 获取昨日零点时间
            List<UserMedalEntity> userMedalEntities = userMedalRepository.findByCreatedAtAfterAndCreatedAtBefore(start, end);
            if (userMedalEntities.size() == 0) {
                return;
            }
            Set<Long> allUserIds = userMedalEntities.stream().map(UserMedalEntity::getUserId).collect(Collectors.toSet());
            if (allUserIds.size() == 0) {
                return;
            }

            List<UserEntity> userEntities = userRepository.findByIds(allUserIds);

            // 根据 uniqName 获取到 飞书 userId
            Map<String, String> larkUserIdMapping = huolalaFeishuEnhanceService.larkUserIdInUniqNames(userEntities.stream().map(UserEntity::getUniqId).collect(Collectors.toList()));
            if (larkUserIdMapping.isEmpty()) {
                return;
            }

            Map<UserEntity, List<UserMedalEntity>> userMedalMap = combineUserAndMedal(userEntities, userMedalEntities);
            int total = 0;
            for (Map.Entry<UserEntity, List<UserMedalEntity>> entry : userMedalMap.entrySet()) {
                boolean ok = notifyUser(entry.getKey(), entry.getValue(), larkUserIdMapping.get(entry.getKey().getUniqId()), dateTitle);
                total += ok ? 1 : 0;
            }
            remoteLogService.info(String.format("成功给 %d（%d） 个用户发送勋章通知", total, userMedalMap.size()));
        } catch (Exception e) {
            remoteLogService.error("发送勋章通知失败", e);
        }
    }

    @Async
    public void notifyUserLastWeekMedal() {
        LocalDateTime prevWeek = getPreviousDateTime().minusDays(7);
        notifyUserMedalInDate(prevWeek, getPreviousDateTime(), "上周");
    }

    private Map<UserEntity, List<UserMedalEntity>> combineUserAndMedal(List<UserEntity> userEntities, List<UserMedalEntity> userMedalEntities) {
        Map<UserEntity, List<UserMedalEntity>> map = new HashMap<>();
        for (UserEntity userEntity : userEntities) {
            List<UserMedalEntity> userMedalEntityList = userMedalEntities.stream().filter(userMedalEntity -> userMedalEntity.getUserId().equals(userEntity.getId())).collect(Collectors.toList());
            map.put(userEntity, userMedalEntityList);
        }
        return map;
    }

    private String renderMedalCardURL(List<UserMedalEntity> userMedalEntities) {
        List<String> medalNames = userMedalEntities.stream().map(item -> "key=" + item.getUniqName().name()).collect(Collectors.toList());
        return HONOR_URL + "?" + String.join("&", medalNames);
    }

    private MessageCardElement[] buildMedalElement(List<UserMedalEntity> medalEntities) {
        List<UserMedalEntity> medalEntitiesPlaceHolder = new ArrayList<>(medalEntities);
        // 不超过 3 个时，补全到 3 个，用 null 填充
        for (int i = 0; i < 3 - medalEntities.size(); i++) {
            medalEntitiesPlaceHolder.add(null);
        }
        List<MessageCardElement> elements = new ArrayList<>();

        elements.add(MessageCardColumnSet.builder()
                .flexMode("flow")
                .columns(medalEntitiesPlaceHolder.stream().map(medal -> {
                            List<MessageCardElement> imgElements = new ArrayList<>();
                            if (medal != null) {
                                String imgKey = medalFeishuImageKeyMap.get(medal.getUniqName());
                                if (imgKey == null) {
                                    imgKey = medal.getUniqName().getLarkImgKey();
                                }
                                imgElements.add(
                                        MessageCardImage.newBuilder()
                                                .imgKey(imgKey)
                                                .alt(MessageCardPlainText.newBuilder().content("").build())
                                                .preview(false)
                                                .build());
                            }
                            return MessageCardColumnSet.Column.builder()
                                    .weight(1)
                                    .width("weighted")
                                    .verticalAlign("center")
                                    .elements(imgElements.toArray(new MessageCardElement[0]))
                                    .build();
                        }
                ).toArray(MessageCardColumnSet.Column[]::new))
                .build());

        return elements.toArray(new MessageCardElement[0]);
    }

    private boolean notifyUser(UserEntity user, List<UserMedalEntity> userMedalEntities, String larkUserId, String dateTitle) {
        if (userMedalEntities.size() == 0 || larkUserId == null) {
            return false;
        }

        if (allowNotifyUsernames != null && allowNotifyUsernames.size() > 0 && !allowNotifyUsernames.contains(user.getUniqId())) {
            // 用在测试环境测试
            return false;
        }

        String value = LocalDate.now().toString();

        String key = String.format("fe-van-api-svc:medal:notify:%d:%s", user.getId(), value);

        String cache = keyValueRedisTemplate.opsForValue().get(key);

        if (StringUtils.isNotEmpty(cache)) {
            // 确保补偿任务不重复发送
            // 如果 cache 不会空，说明当前日期已经发送过了
            return false;
        }

        MessageCard card = MessageCard.newBuilder()
                .config(MessageCardConfig.newBuilder()
                        .enableForward(true)
                        .build())
                .cardLink(MessageCardURL.newBuilder()
                        .pcUrl(renderMedalCardURL(userMedalEntities))
                        .url("").androidUrl("").iosUrl("")
                        .build())
                .header(MessageCardHeader.newBuilder()
                        .title(MessageCardPlainText.newBuilder().content(String.format("🎉 %s获得 %d 枚勋章！", dateTitle, userMedalEntities.size())).build())
                        .template(MessageCardHeaderTemplateEnum.BLUE)
                        .build())
                .elements(buildMedalElement(userMedalEntities))
                .build();

        CreateMessageReq req = CreateMessageReq.newBuilder()
                .receiveIdType(CreateMessageReceiveIdTypeEnum.USER_ID)
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .msgType(MsgTypeEnum.MSG_TYPE_INTERACTIVE.getValue())
                        .receiveId(larkUserId)
                        .content(card.String())
                        .build())
                .build();
        try {
            CreateMessageResp createMessageResp = feishuClient.im().message().create(req);
            if (!createMessageResp.success()) {
                log.error(String.format("给用户 %s 发送勋章通知失败, %d,%s", user.getUniqId(), createMessageResp.getCode(), createMessageResp.getMsg()), createMessageResp.getError());
            } else {
                keyValueRedisTemplate.opsForValue().set(key, "send", Duration.ofHours(24));
                return true;
            }
        } catch (Exception e) {
            log.error(String.format("给用户 %s 发送勋章通知失败", user.getUniqId()), e);
        }
        return false;
    }
}
