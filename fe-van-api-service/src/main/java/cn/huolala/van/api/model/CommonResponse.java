package cn.huolala.van.api.model;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.Arrays;

public interface CommonResponse {
    @NonNull
    @CanIgnoreReturnValue
    static CommonResponse createAndAssert(@NonNull HttpRequestBase request, @NonNull CloseableHttpResponse response) {
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        CommonResponse cm;
        try {
            cm = mapper.readValue(response.getEntity().getContent(), CommonResponse.class);
        } catch (IOException e) {
            throw new InternalMappingException(e);
        }
        int status = response.getStatusLine().getStatusCode();
        if (status != 200 || !cm.success()) {
            StringBuilder message = new StringBuilder();
            message.append("name: ");
            message.append(cm.fetchName());
            message.append(", ");
            message.append("message: ");
            message.append(cm.fetchMessage());
            try {
                String jsonData = mapper.writeValueAsString(cm.fetchData());
                message.append(", ");
                message.append("data: ");
                message.append(jsonData);
            } catch (JsonProcessingException e) {
                // Ignore
            }
            throw new InternalRequestException(status, request.getURI(), message.toString());
        }
        return cm;
    }

    @JsonCreator
    static CommonResponse create(JsonNode node) {
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        Object instance = Arrays.stream(CommonResponse.class.getDeclaredClasses())
                .filter(clz -> clz != Unknown.class)
                .filter(clz -> Arrays.stream(clz.getDeclaredFields()).allMatch(i -> node.has(i.getName())))
                .findFirst().map(clz -> mapper.convertValue(node, clz)).orElse(null);
        if (instance instanceof CommonResponse) return (CommonResponse) instance;
        try {
            return new Unknown(mapper.writeValueAsString(node));
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
    }

    // TODO: The `execute` method shouldn't be implemented here, moving it to a utility class is better.

    @Nullable
    static <T> T execute(@NonNull HttpRequestBase req) {
        return execute(req, null);
    }

    @Nullable
    static <T> T execute(@NonNull HttpRequestBase req, @Nullable Class<T> clz) {
        CloseableHttpResponse res;
        CloseableHttpClient httpClient = ApplicationContextUtil.getBean(CloseableHttpClient.class);
        try {
            res = httpClient.execute(req);
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
        CommonResponse cr = createAndAssert(req, res);
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        Object data = cr.fetchData();
        if (data == null || clz == null) return null;
        return mapper.convertValue(data, clz);
    }

    @NonNull
    default String fetchMessage() {
        if (this instanceof CodeMessage) return ((CodeMessage<?>) this).getMessage();
        if (this instanceof SpringError) return ((SpringError) this).getMessage();
        if (this instanceof Unknown) return ((Unknown) this).getJson();
        return "";
    }

    @NonNull
    default String fetchName() {
        if (this instanceof CodeMessage) return ((CodeMessage<?>) this).getCode();
        if (this instanceof SpringError) return ((SpringError) this).getError();
        return "Unknown";
    }

    default boolean success() {
        return false;
    }

    @Nullable
    default Object fetchData() {
        if (this instanceof CodeMessage) return ((CodeMessage<?>) this).getData();
        return null;
    }

    /**
     * This response struct is usually used by PHP services.
     *
     * @param <T>
     */
    @Getter
    @Setter
    class CodeMessage<T> implements CommonResponse {
        private String code;
        private String message;
        @Nullable
        private T data;

        @Override
        public boolean success() {
            return "200".equals(this.getCode());
        }
    }

    /**
     * This response struct is usually used by Java EasyOpen services.
     *
     * @param <T>
     */
    @Getter
    @Setter
    class EasyOpenResponse<T> implements CommonResponse {
        private String ret;
        private String code;
        private String msg;
        @Nullable
        private T data;

        @Override
        public boolean success() {
            return "0".equals(this.getRet());
        }
    }

    /**
     * TODO: This class may not be used?
     */
    @Getter
    @Setter
    class SpringError implements CommonResponse {
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
        private OffsetDateTime timestamp;
        private String error;
        private String message;
        private String path;
        private int status;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    class Unknown implements CommonResponse {
        private String json;
    }
}
