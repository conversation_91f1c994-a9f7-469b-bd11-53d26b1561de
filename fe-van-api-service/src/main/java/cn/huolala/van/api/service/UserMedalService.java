package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.projection.MedalRankingProjection;
import cn.huolala.van.api.model.UserMedalModel;
import org.springframework.data.domain.Page;
import org.springframework.lang.NonNull;

import java.util.Collection;
import java.util.List;

public interface UserMedalService {
    List<UserMedalModel> list(Long userId);

    Long add(Long userId, MedalEnum name, String remark);

    long countMedalReceived(MedalEnum name);

    @NonNull
    Page<MedalRankingProjection> rank(@NonNull Collection<String> excludeUsers, int page, int size);
}
