package cn.huolala.van.api.implementation;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.service.HuolalaAIService;
import cn.huolala.van.api.service.OpenaiService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lark.oapi.Client;
import com.lark.oapi.service.im.v1.model.ReplyMessageReq;
import com.lark.oapi.service.im.v1.model.ReplyMessageReqBody;
import com.lark.oapi.service.im.v1.model.ReplyMessageResp;
import com.unfbx.chatgpt.OpenAiClient;
import com.unfbx.chatgpt.entity.chat.ChatCompletion;
import com.unfbx.chatgpt.entity.chat.ChatCompletionResponse;
import com.unfbx.chatgpt.entity.chat.Message;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Log4j2
public class OpenaiServiceImpl implements OpenaiService {

    @Autowired
    private OpenAiClient openAiClient;

    @Resource(name = "van")
    private Client feishuClient;

    @Resource(name = "van-workers")
    private RedisTemplate<String, Message> redisTemplate;

    @Resource(name = "van-workers")
    private RedisTemplate<String, Integer> redisTemplateForTemperature;

    @Autowired
    private HuolalaAIService huolalaAIService;

    @Value("${openai.key}")
    private String key;

    @Value("${openai.host}")
    private String host;
    @Autowired
    private ObjectMapper mapper;

    private String getReplyText(String account, String text, int temperature) {
        ChatCompletionResponse chatCompletionResponse = huolalaAIService.azure35ChatCompletion(account, ChatCompletion.builder()
                .temperature(temperature / 10.0)
                .messages(Arrays.asList(
                        Message.builder().role(Message.Role.SYSTEM).content("你是一个精通编程的程序员").build(),
                        Message.builder().role(Message.Role.USER).content(text).build()

                ))
                .build());
        if (!chatCompletionResponse.getChoices().isEmpty()) {
            return chatCompletionResponse.getChoices().get(0).getMessage().getContent();
        }
        return "未知错误";
    }

    private void feishuReply(String messageId, String text) {
        String content;
        try {
            content = mapper.writeValueAsString(new TextMessage(text));
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }

        ReplyMessageReq replyMessageReq = ReplyMessageReq.newBuilder()
                .messageId(messageId)
                .replyMessageReqBody(ReplyMessageReqBody.newBuilder()
                        .content(content)
                        .msgType("text")
                        .build()).build();

        try {
            ReplyMessageResp reply = feishuClient.im().message().reply(replyMessageReq);
            log.debug("feishuChat reply:{}", reply);
        } catch (Exception e) {
            log.error("feishuChat error", e);
        }
    }

    @Async
    public void feishuChat(String account, String messageId, String text) {
        String replyText = "未知错误";
        try {
            replyText = getReplyText(account, text, 7);
        } catch (Exception e) {
            log.error("feishuChat openai error", e);
        }
        feishuReply(messageId, replyText);
    }

    private Message getReply(String account, List<Message> prevMessages, String text, int temperature) {
        prevMessages.add(0, Message.builder().role(Message.Role.SYSTEM).content("你是一个精通编程的程序员").build());

        Message message = Message.builder().role(Message.Role.USER).content(text).build();
        redisTemplate.opsForList().leftPop(key);
        redisTemplate.opsForList().rightPush(key, message);
        prevMessages.add(message);

        ChatCompletionResponse chatCompletionResponse = huolalaAIService.azure35ChatCompletion(account, ChatCompletion.builder()
                .temperature(temperature / 10.0)
                .messages(prevMessages)
                .build());

        if (chatCompletionResponse.getChoices().size() > 0) {
            return chatCompletionResponse.getChoices().get(0).getMessage();
        }
        return null;
    }

    private String getReplyText(String account, String chatId, String openId, String text) {
        String key = String.format("fe-van-api-svc:gpt:s:%s:%s", chatId, openId);
        if ("重新开始".equals(text)) {
            redisTemplate.delete(key);
            return "好的，之前的对话已经清除";
        }

        String temperatureKey = String.format("fe-van-api-svc:gpt:t:%s", openId);
        if (text.startsWith("设置温度")) {
            // text 的格式是 "设置温度 7"，从 text 中提取出温度
            int temperature = Integer.parseInt(text.substring(4).trim());
            if (temperature < 0 || temperature > 10) {
                return "温度只能在 0 到 10 之间";
            }
            redisTemplateForTemperature.opsForValue().set(temperatureKey, temperature);
            return String.format("温度已经为你设置为 %d", temperature);
        }

        List<Message> prevMessages = redisTemplate.opsForList().range(key, 0, 5);
        redisTemplate.expire(key, 12, TimeUnit.HOURS);

        if (prevMessages == null) {
            prevMessages = new ArrayList<>();
        }

        Integer integer = redisTemplateForTemperature.opsForValue().get(temperatureKey);
        Message reply = getReply(account, prevMessages, text, integer == null ? 7 : integer);

        if (reply == null) {
            return null;
        }

        redisTemplate.opsForList().rightPushAll(
                key,
                Message.builder().role(Message.Role.USER).content(text).build(),
                reply
        );
        return reply.getContent();
    }

    @Async
    public void feishuChatSession(String account, String chatId, String openid, String messageId, String text) {
        String replyText = getReplyText(account, chatId, openid, text);
        feishuReply(messageId, replyText);
    }

    @AllArgsConstructor
    private static class TextMessage {
        public String text;
    }
}
