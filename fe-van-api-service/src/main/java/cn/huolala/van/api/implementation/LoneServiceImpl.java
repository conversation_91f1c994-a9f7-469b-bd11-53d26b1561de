package cn.huolala.van.api.implementation;

import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.model.CmdbAppRegion;
import cn.huolala.van.api.model.lone.*;
import cn.huolala.van.api.model.roles.RegionalInfoMap;
import cn.huolala.van.api.service.LoneService;
import cn.huolala.van.api.util.PhpUtils;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class LoneServiceImpl implements LoneService {
    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private CloseableHttpClient httpClient;

    @Value("${lone.basicURL}")
    private String basicURL;

    @Value("${lone.secret}")
    private String secret;

    @Value("${lone.appKey}")
    private String appKey;

    @NonNull
    private <T> Optional<T> request(String path, Map<String, ?> params, JavaType dataType) {
        LoneResponse<T> cbs;
        try {
            HttpPost httpPost = new HttpPost(basicURL + path);
            List<NameValuePair> list = params.entrySet().stream()
                    .map(i -> new BasicNameValuePair(i.getKey(), i.getValue().toString()))
                    .collect(Collectors.toList());
            list.add(new BasicNameValuePair("appkey", appKey));
            list.add(new BasicNameValuePair("_sign", PhpUtils.sign(secret, list)));
            httpPost.setEntity(new UrlEncodedFormEntity(list, StandardCharsets.UTF_8));
            CloseableHttpResponse res = httpClient.execute(httpPost);
            JavaType t = mapper.getTypeFactory().constructParametricType(LoneResponse.class, dataType);
            cbs = mapper.readValue(res.getEntity().getContent(), t);
        } catch (IOException exception) {
            throw new InternalRequestException(exception);
        }
        cbs.assertOk();
        return Optional.ofNullable(cbs.getData());
    }

    @NonNull
    private <T> Optional<T> request(String path, Map<String, ?> params, Class<T> clz) {
        return request(path, params, mapper.constructType(clz));
    }

    @Override
    @NonNull
    public List<LoneApp> getBusListByUser(@NonNull LoneRegion region, @NonNull LoneEnv env, @NonNull String userUniq) {
        Map<String, Object> params = new HashMap<>();
        params.put("region", region.ordinal());
        params.put("env", env.ordinal());
        params.put("user", userUniq);
        JavaType type = mapper.getTypeFactory().constructParametricType(List.class, LoneApp.class);
        Optional<List<LoneApp>> res = request("/index.php?_g=api&_m=appid&_a=getBusListByUser", params, type);
        return res.orElseGet(Collections::emptyList);
    }

    @Override
    @Nullable
    public LoneAppDetail getOneAppidInfo(@NonNull LoneRegion region, @NonNull LoneEnv env, @NonNull String appId) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", appId);
        params.put("region", region.ordinal());
        params.put("env", env.ordinal());
        Optional<LoneAppDetail> res = request("/index.php?_g=api&_m=appid&_a=getOneAppidInfo", params, LoneAppDetail.class);
        return res.orElse(null);
    }

    @Nullable
    @Override
    public LoneAppSummary getOneAppidInfoSummary(@NonNull LoneRegion region, @NonNull LoneEnv env, @NonNull String appId) {
        Map<String, Object> params = new HashMap<>();
        params.put("appid", appId);
        params.put("region", region.ordinal());
        params.put("env", env.ordinal());
        Optional<LoneAppSummary> res = request("/index.php?_g=api&_m=appid&_a=getOneAppidInfo", params, LoneAppSummary.class);
        return res.orElse(null);
    }

    @Override
    @NonNull
    public List<LoneAppSummary> getAllAppid(@Nullable String keyword) {
        Map<String, Object> params = new HashMap<>();
        if (keyword != null) params.put("keyword", keyword);
        JavaType type = mapper.getTypeFactory().constructParametricType(List.class, LoneAppSummary.class);
        Optional<List<LoneAppSummary>> res = request("/index.php?_g=api&_m=appid&_a=getAllAppid", params, type);
        return res.orElseGet(Collections::emptyList);
    }

    @Override
    @NonNull
    public Map<String, RegionalInfoMap> loadRegionalRoleMap(@NonNull String appId) {
        return LoneRegion.supportedRegions().parallel()
                // Fetch appInfo concurrently from Lone in each region.
                .map(region -> Pair.of(region, getOneAppidInfo(region, LoneEnv.prd, appId)))
                // Convert to sequential Stream.
                .collect(Collectors.toList()).stream()
                // Flatten the result for each role.
                .flatMap(e ->
                        Optional.ofNullable(e.getValue()).map(LoneAppDetail::getBusUser).map(bus -> Stream.of(
                                Triple.of(e.getKey(), Role.AdminRole, bus.getLeaderList()),
                                Triple.of(e.getKey(), Role.DevRole, bus.getDevelopList()),
                                Triple.of(e.getKey(), Role.TestRole, bus.getTesterList())
                        )).orElseGet(Stream::empty)
                )
                // Flatten the result for each user, and covert LoneRegion to CmdbAppRegion.
                .flatMap(i ->
                        Optional.ofNullable(i.getRight()).map(Collection::stream).orElseGet(Stream::empty)
                                .map(uniqId -> Triple.of(uniqId, CmdbAppRegion.from(i.getLeft()), i.getMiddle())))
                // Group by userUniqId and collect into a RegionalInfoMap.
                .collect(Collectors.groupingBy(
                        Triple::getLeft,
                        Collectors.mapping(i -> Pair.of(i.getMiddle(), i.getRight()), RegionalInfoMap.collect())
                ));
    }

    @Nullable
    @Override
    public LoneAppWindow getAppWindow(@NonNull String appId, @NonNull LoneRegion region) {
        Map<String, String> params = new HashMap<>();
        params.put("region", String.valueOf(region.ordinal()));
        params.put("env", "4"); // prod
        params.put("appid", appId);
        params.put("appkey", appKey);
        Optional<LoneAppWindow> res = request("/index.php?_g=api&_m=windows&_a=getBusRelwindow", params, LoneAppWindow.class);
        return res.orElse(null);
    }
}
