package cn.huolala.van.api.model.project;

import cn.huolala.van.api.dao.entity.ProjectEntity;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.lalaframework.soa.exception.BusinessException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

@Getter
public class ProjectModel extends ProjectSummary {
    public static final String NAME = "project";

    @ApiModelProperty("A git repository path in format `${group}/${repo}`, such as 'hll-fe/van-web'.\n" +
            "If the project has been archived, this value is reset to an empty string.")
    @NonNull
    private final String repository;

    @NonNull
    private final ProjectConfig config;

    @NonNull
    private final OffsetDateTime updatedAt;

    @NonNull
    private final OffsetDateTime createdAt;

    public ProjectModel(@NonNull ProjectEntity e) {
        super(e);
        repository = e.getRepository();
        config = parseRawConfig(e.getConfig());
        ZoneId zone = ZoneId.systemDefault();
        updatedAt = e.getUpdatedAt().atZone(zone).toOffsetDateTime();
        createdAt = e.getCreatedAt().atZone(zone).toOffsetDateTime();
    }

    @NonNull
    private static ProjectConfig parseRawConfig(@Nullable String config) {
        ProjectConfig model = null;
        if (config != null && !config.isEmpty()) {
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            try {
                model = mapper.readValue(config, ProjectConfig.class);
            } catch (JsonProcessingException e) {
                throw new InternalMappingException(e);
            }
        }
        if (model == null) model = new ProjectConfig();
        return model;
    }

    @Nullable
    public static ProjectModel from(@Nullable ProjectEntity pe) {
        if (pe == null) return null;
        return new ProjectModel(pe);
    }

    @NonNull
    public String fetchAndAssertAppId() {
        String appId = config.getAppId();
        if (appId == null) throw new BusinessException("Lone appId is not found from config of project (" + this + ")");
        return appId;
    }

    @Nullable
    public String fetchRawConfig() {
        return entity.getConfig();
    }
}
