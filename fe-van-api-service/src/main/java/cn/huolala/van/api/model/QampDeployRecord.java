package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import com.fasterxml.jackson.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;
import java.util.Collection;

import static java.util.stream.Collectors.joining;

@Getter
@Setter
public class QampDeployRecord {
    private String gitUrl;
    private String serviceName;
    private String branch;
    private String commitId;
    private String serviceOwner;
    private String env;
    private String developLanguage;
    private String deployUser;
    private String status;
    private String message;
    @JsonProperty("deploy_type")
    private DeployType deployType;
    @JsonProperty("window_type")
    private WindowType windowType;
    @JsonProperty("release_type")
    private ReleaseType releaseType;
    @JsonProperty("duration_times")
    private Integer durationTimes;
    private QampRegion region;
    @JsonProperty("iteration_id")
    private String iterationId;
    @JsonProperty("is_gray")
    private QampGray isGray;
    private RepeatedlyDeploy isRepeatedlyDeploy;
    @JsonProperty("gra_version")
    private String graVersion;

    public QampDeployRecord(@NonNull UserModel user,
                            @NonNull ProjectModel project,
                            @NonNull Collection<BuildTaskModel> tasks,
                            @NonNull Env dEnv,
                            @NonNull Region dRegion,
                            @Nullable UserBase owner,
                            @Nullable Long planId) {
        gitUrl = String.format("ssh://*********************:56358/%s.git", project.getRepository());
        serviceName = project.fetchAndAssertAppId();
        branch = tasks.stream().map(BuildTaskModel::getBranch).collect(joining(","));
        commitId = tasks.stream().map(BuildTaskModel::getHash).collect(joining(","));
        graVersion = tasks.stream().map(BuildTaskModel::getId).map(Object::toString).collect(joining(","));
        deployUser = user.getUniqId();
        env = dEnv.name();
        status = "success";
        developLanguage = "javascript";
        deployType = DeployType.Frontend;
        windowType = WindowType.Normal;
        releaseType = ReleaseType.Normal;
        isRepeatedlyDeploy = RepeatedlyDeploy.No;
        isGray = QampGray.No;
        region = QampRegion.from(dRegion);
        if (owner != null) serviceOwner = owner.getUserUniqId();
        if (planId != null) iterationId = planId.toString();
    }

    public enum ReleaseType {
        Unknown,
        Normal,
        Republish;

        @NonNull
        @JsonCreator
        public static ReleaseType create(@Nullable Object raw) {
            if (raw instanceof String) {
                try {
                    raw = Integer.parseInt((String) raw);
                } catch (NumberFormatException e) {
                    return EnumUtils.getEnum(ReleaseType.class, (String) raw, Unknown);
                }
            }
            if (raw instanceof Number) {
                int index = ((Number) raw).intValue();
                if (index < values().length) return values()[index];
            }
            return Unknown;
        }

        @JsonValue
        public String toJson() {
            return String.valueOf(this.ordinal());
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum WindowType {
        @JsonEnumDefaultValue
        Unknown,

        @ApiModelProperty("常规窗口")
        Normal,
        @ApiModelProperty("临时窗口")
        Temporary,
        @ApiModelProperty("灰度窗口")
        Grayscale,
        @ApiModelProperty("全局窗口")
        Global,
        Unused_5,
        Unused_6,
        @ApiModelProperty("扩容发布")
        Expansion
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum DeployType {
        @JsonEnumDefaultValue
        Unknown,
        Unused_1,
        Unused_2,
        @ApiModelProperty("前端发布")
        Frontend
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum QampRegion {
        @JsonEnumDefaultValue
        Unknown, China, Singapore, India, Latin;

        public static QampRegion from(Region region) {
            switch (region) {
                case cn:
                    return China;
                case sin:
                    return Singapore;
                case sao:
                    return Latin;
                case bom:
                    return India;
                default:
                    return Unknown;
            }
        }
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum QampGray {
        @JsonEnumDefaultValue
        Unknown, No, Yes
    }

    public enum RepeatedlyDeploy {
        Unknown("未知"), Yes("是"), No("否");

        private final String value;

        RepeatedlyDeploy(String value) {
            this.value = value;
        }

        @NonNull
        @JsonCreator
        public static RepeatedlyDeploy create(@Nullable Object raw) {
            if (raw instanceof String) {
                return Arrays.stream(values()).filter(i -> i.value.equals(raw)).findFirst().orElse(Unknown);
            }
            if (raw instanceof Number) {
                int index = ((Number) raw).intValue();
                if (index < values().length) return values()[index];
            }
            return Unknown;
        }

        @JsonValue
        public String toJson() {
            return value;
        }
    }
}