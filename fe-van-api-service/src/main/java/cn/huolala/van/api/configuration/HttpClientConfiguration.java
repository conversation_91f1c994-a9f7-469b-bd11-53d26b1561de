package cn.huolala.van.api.configuration;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HttpClientConfiguration {
    @Bean
    public CloseableHttpClient httpClient() {
        RequestConfig config = RequestConfig.custom()
                .setConnectionRequestTimeout(100)
                .setConnectTimeout(5000)
                .setSocketTimeout(5000)
                .setRedirectsEnabled(true)
                .setMaxRedirects(5)
                .build();
        return HttpClientBuilder.create()
                .setMaxConnTotal(1000)
                .setMaxConnPerRoute(100)
                .setDefaultRequestConfig(config)
                .build();
    }
}
