package cn.huolala.van.api.service;

import cn.huolala.van.api.model.*;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

public interface MonitorRecordService {
    void sendUserProjectsYesterDayMonitorRecordMessage(List<String> userUniqIds);

    void sendProjectsYesterDayMonitorRecordMessage();

    @NonNull
    List<ImportantProjectView> getImportantProjects(@NonNull ImportantProjectsParams params);

    void clearImportantProjectsCache();

    @NonNull
    List<SimpleMonitorRecordModel> query(
            long projectId,
            int limit,
            double minimalPv,
            @Nullable OffsetDateTime startTime,
            @Nullable OffsetDateTime endTime);

    @NonNull
    List<ImportantProjectView> getImportantProjectsWithoutCache(@NonNull ImportantProjectsParams params);

    @NonNull
    List<Optional<MonitorRecordDetail>> batchGetDetail(@NonNull List<IdDatePair> idpList);

    public void saveLightAppProjectScoreByName(String projectName) throws Exception;
}
