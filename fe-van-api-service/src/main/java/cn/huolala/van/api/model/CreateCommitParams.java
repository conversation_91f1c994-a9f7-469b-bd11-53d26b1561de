
package cn.huolala.van.api.model;

import cn.huolala.van.api.facade.model.gitlab.*;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class CreateCommitParams {
    @JsonProperty("branch")
    private String branch;
    @JsonProperty("commit_message")
    private String commitMessage;
    @JsonProperty("start_branch")
    private String startBranch;
    @JsonProperty("author_email")
    private String authorEmail;
    @JsonProperty("author_name")
    private String authorName;
    @JsonProperty("actions")
    private List<GitlabCommitAction> actions;
}
