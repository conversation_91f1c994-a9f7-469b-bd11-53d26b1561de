package cn.huolala.van.api.model;

import com.google.errorprone.annotations.CanIgnoreReturnValue;
import lombok.Getter;
import org.jetbrains.annotations.Contract;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

@Getter
public class Change<T> {
    @Nullable
    private final T before;
    @Nullable
    private final T after;

    public Change(@Nullable final T before, @Nullable final T after) {
        this.before = before;
        this.after = after;
    }

    @NonNull
    public <R> Change<R> mapIfNonNull(@NonNull Function<T, R> mapper) {
        return new Change<>(
                Optional.ofNullable(before).map(mapper).orElse(null),
                Optional.ofNullable(after).map(mapper).orElse(null)
        );
    }

    @NonNull
    public <R> Change<R> map(@NonNull Function<T, R> mapper) {
        final Change<R> res = new Change<>(mapper.apply(before), mapper.apply(after));
        Objects.requireNonNull(res.getAfter());
        return res;
    }

    public boolean isChanged() {
        return !Objects.equals(before, after);
    }

    @Nullable
    @Contract(value = "!null->!null", pure = true)
    public T getBeforeOrElse(T defaultValue) {
        return before == null ? defaultValue : before;
    }

    @Nullable
    @Contract(value = "!null->!null", pure = true)
    public T getAfterOrElse(T defaultValue) {
        return after == null ? defaultValue : after;
    }

    @NonNull
    public T getBeforeOrThrow(@NonNull Supplier<RuntimeException> exceptionSupplier) {
        if (before != null) return before;
        throw exceptionSupplier.get();
    }

    @NonNull
    public T getAfterOrThrow(@NonNull Supplier<RuntimeException> exceptionSupplier) {
        if (after != null) return after;
        throw exceptionSupplier.get();
    }

    @CanIgnoreReturnValue
    public void ifChanged(NonNullConsumer<Change<T>> consumer) {
        if (isChanged()) consumer.accept(this);
    }
}
