package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.van.api.dao.entity.MetaEntity;
import cn.huolala.van.api.dao.model.IdContentPair;
import cn.huolala.van.api.dao.repository.MetaRepository;
import cn.huolala.van.api.exception.DirtyDataException;
import cn.huolala.van.api.model.Change;
import cn.huolala.van.api.model.MetaModel;
import cn.huolala.van.api.model.NonNullConsumer;
import cn.huolala.van.api.model.Processor;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.events.ProjectEventContent;
import cn.huolala.van.api.model.events.ProjectEventType;
import cn.huolala.van.api.model.meta.TaskSyncInfo;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.MetaService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.AbstractMap.SimpleEntry;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.time.Instant.ofEpochSecond;

@Service
public class MetaServiceImpl implements MetaService {
    @Autowired
    private MetaRepository metaRepository;
    @PersistenceContext
    private EntityManager entityMapper;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private BuildTaskService buildTaskService;

    @Override
    @NonNull
    public Optional<MetaModel> get(@NonNull Long metaId, @NonNull MetaType type) {
        Optional<MetaEntity> metaEntityOptional = metaRepository.get(type.ordinal(), metaId);
        return metaEntityOptional.map(MetaModel::from);
    }

    @Override
    @NonNull
    public <T> Optional<T> getOptional(@NonNull MetaType type, @NonNull Long metaId, @NonNull Class<T> valueType) {
        return metaRepository.findPairByMetaId(type.ordinal(), metaId).map(json -> {
            try {
                return mapper.readValue(json, valueType);
            } catch (JsonProcessingException e) {
                throw DirtyDataException.create(e);
            }
        });
    }

    @Override
    @Nullable
    public <T> T getValue(@NonNull MetaType type, @NonNull Long metaId, @NonNull Class<T> valueType) {
        return getOptional(type, metaId, valueType).orElse(null);
    }

    @Override
    @Nullable
    public String getValue(@NonNull MetaType type, @NonNull Long metaId) {
        return metaRepository.findPairByMetaId(type.ordinal(), metaId).orElse(null);
    }

    @Override
    @NonNull
    public Map<Long, String> getValue(@NonNull MetaType type, @NonNull Collection<Long> metaIds) {
        if (metaIds.isEmpty()) return Collections.emptyMap();
        return metaRepository.findPairByMetaIds(type.ordinal(), metaIds).stream()
                .filter(i -> i.getContent() != null)
                .collect(Collectors.toMap(IdContentPair::getId, IdContentPair::getContent));
    }

    @Override
    @NonNull
    public <T> Map<Long, T> getValue(@NonNull MetaType type, @NonNull Collection<Long> metaIds,
                                     @NonNull Class<T> valueType) {
        if (metaIds.isEmpty()) return Collections.emptyMap();
        return metaRepository.findPairByMetaIds(type.ordinal(), metaIds).stream().map(i -> {
            T value = MetaModel.parseMeta(i.getContent(), valueType);
            return value == null ? null : new SimpleEntry<>(i.getId(), value);
        }).filter(Objects::nonNull).collect(Collectors.toMap(SimpleEntry::getKey, SimpleEntry::getValue));
    }

    @NonNull
    @Override
    @Transactional
    public <T> Change<T> setValue(@NonNull MetaType type,
                                  @NonNull Long id,
                                  @NonNull Class<T> modelType,
                                  @Nullable T newValue) {
        return partialUpdateValue(type, Collections.singletonList(id), modelType, o -> newValue).get(id);
    }

    @NonNull
    @Override
    @Transactional
    public <T> Change<T> partialUpdateValue(@NonNull MetaType type,
                                            @NonNull Long id,
                                            @NonNull Class<T> javaType,
                                            @NonNull Processor<T> processor) {
        return partialUpdateValue(type, Collections.singleton(id), javaType, processor).get(id);
    }

    @NonNull
    @Override
    @Transactional
    public Change<MetaModel> partialUpdate(@NonNull MetaType type,
                                           @NonNull Long id,
                                           @NonNull NonNullConsumer<MetaModel> consumer) {
        return batchModify(type, Collections.singleton(id), consumer).get(id);
    }

    @Override
    @Transactional
    @NonNull
    public <T> Map<Long, Change<T>> partialUpdateValue(@NonNull MetaType type,
                                                       @NonNull Collection<Long> metaIds,
                                                       @NonNull Class<T> javaType,
                                                       @NonNull Processor<T> processor) {
        return batchModify(type, metaIds, me -> {
            T newValue = processor.apply(MetaModel.parseMeta(me.getMeta(), javaType));
            me.setMeta(MetaModel.serializeMeta(newValue));
        }).entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                e -> e.getValue().mapIfNonNull(i -> MetaModel.parseMeta(i.getMeta(), javaType)))
        );
    }

    private Map<Long, Change<MetaModel>> batchModify(@NonNull MetaType type,
                                                     @NonNull Collection<Long> metaIds,
                                                     @NonNull NonNullConsumer<MetaModel> consumer) {
        Map<Long, MetaModel> oldMap = metaRepository
                .findForUpdate(type.ordinal(), metaIds)
                .stream().collect(Collectors.toMap(MetaEntity::getMetaId, MetaModel::from));

        Map<Long, Change<MetaModel>> changeMap = metaIds.stream().map(metaId -> {
            final MetaModel oldData = oldMap.get(metaId);
            final MetaModel newData = new MetaModel(type, metaId);
            if (oldData != null) {
                newData.setMeta(oldData.getMeta());
                newData.setCreatedAt(oldData.getCreatedAt());
                newData.setUpdatedAt(oldData.getUpdatedAt());
            }
            consumer.accept(newData);
            return Pair.of(metaId, new Change<>(oldData, newData));
        }).collect(Collectors.toMap(Pair::getKey, Pair::getValue));

        batchUpdate(changeMap.values().stream().map(Change::getAfter).collect(Collectors.toSet()));

        return changeMap;
    }

    @CanIgnoreReturnValue
    private int batchUpdate(@NonNull Collection<MetaModel> entities) {
        Object[] parameters = entities.stream()
                .flatMap(i -> Stream.of(i.getType().ordinal(), i.getMetaId(), i.getMeta())).toArray();
        if (parameters.length == 0) return 0;
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO metas (type, meta_id, meta) VALUES ");
        for (int i = 0; i < parameters.length; i += 3) {
            if (i > 0) sql.append(", ");
            sql.append("(?, ?, ?)");
        }
        sql.append(" ON DUPLICATE KEY UPDATE meta = VALUES(meta)");
        Query query = entityMapper.createNativeQuery(sql.toString());
        for (int i = 0; i < parameters.length; i++) query.setParameter(i + 1, parameters[i]);
        return query.executeUpdate();
    }

    @NonNull
    @Override
    public Stream<ProjectEvent> findMetaEvents(long key, int limit) {
        LocalDateTime ifModifiedSince = ofEpochSecond(key).atZone(ZoneId.systemDefault()).toLocalDateTime();
        List<MetaEntity> entities = metaRepository.findAfter(ifModifiedSince, limit);
        Map<Long, Long> idMap = entities.stream().map(MetaEntity::getMetaId)
                .collect(Collectors.collectingAndThen(Collectors.toSet(), buildTaskService::findProjectIdsByTaskIds));
        return entities.stream().map(i -> createProjectEvent(i, idMap));
    }

    @Override
    public boolean hasValue(@NonNull MetaType metaType, long taskId) {
        return metaRepository.exists(metaType.ordinal(), taskId) > 0;
    }

    @NonNull
    private ProjectEvent createProjectEvent(@NonNull MetaEntity entity, @NonNull Map<Long, Long> idMap) {
        long key = entity.getUpdatedAt().atZone(ZoneId.systemDefault()).toEpochSecond();
        Long taskId = entity.getMetaId();
        ProjectEventType type;
        ProjectEventContent content;
        switch (entity.getType()) {
            case LLMSyncTaskStatus:
                type = ProjectEventType.TaskSyncEvent;
                content = new ProjectEventContent.TaskSyncEvent(taskId, "LLM", buildTaskSyncInfo(entity));
                break;
            case UdSyncTaskStatus:
                type = ProjectEventType.TaskSyncEvent;
                content = new ProjectEventContent.TaskSyncEvent(taskId, "UD", buildTaskSyncInfo(entity));
                break;
            default:
                type = ProjectEventType.UnknownEvent;
                content = new ProjectEventContent.Unknown(null);
        }
        return new ProjectEvent(key, type, idMap.get(taskId), content, null);
    }

    @Nullable
    private TaskSyncInfo buildTaskSyncInfo(@NonNull MetaEntity entity) {
        TaskSyncInfo info;
        try {
            info = mapper.readValue(entity.getMeta(), TaskSyncInfo.class);
        } catch (JsonProcessingException e) {
            info = null;
        }
        return info;
    }

    @Override
    @NonNull
    public Long create(@NonNull MetaEntity entity) {
        return metaRepository.save(entity).getId();
    }
}
