package cn.huolala.van.api.model.coupe;

import cn.huolala.van.api.model.MiniProgramScoreSummary;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

public class CoupeVanConfig {
	@JSONField(name = "mini_progaram_score_summary")
	@JsonProperty("mini_progaram_score_summary")
	private String miniProgramScoreSummaryString;

	private MiniProgramScoreSummary miniProgramScoreSummary;

	public MiniProgramScoreSummary getMiniProgramScoreSummary() {
		if (miniProgramScoreSummary == null) {
			miniProgramScoreSummary = JSON.parseObject(this.miniProgramScoreSummaryString,
					MiniProgramScoreSummary.class);
		}

		return miniProgramScoreSummary;
	}
}
