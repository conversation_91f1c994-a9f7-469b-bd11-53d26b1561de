package cn.huolala.van.api.model.tasks;

import lombok.Getter;
import org.springframework.lang.NonNull;

@Getter
public enum TaskFileName implements TaskPath {
    BuildLog("%s/log/%d", "build log"),
    JsonTree("%s/tree/%d.json", "json tree"),
    TextTree("%s/tree/%d.txt", "text tree");

    @NonNull
    private final String template;
    @NonNull
    private final String name;

    TaskFileName(@NonNull String template, @NonNull String name) {
        this.template = template;
        this.name = name;
    }
}
