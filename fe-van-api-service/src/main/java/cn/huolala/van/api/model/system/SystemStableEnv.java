package cn.huolala.van.api.model.system;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class SystemStableEnv {
    @JsonProperty("stableNumber")
    private int stableNumber;

    @JsonProperty("name")
    private String name;

    @JsonProperty("stableEnv")
    private List<String> stableEnv;

    public SystemStableEnv(String name, int stableNumber) {
        this.name = name;
        this.stableNumber = stableNumber;
    }
}
