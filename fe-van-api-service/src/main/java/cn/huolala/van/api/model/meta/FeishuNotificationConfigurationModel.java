package cn.huolala.van.api.model.meta;

import cn.huolala.van.api.model.ConfigEnv;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.util.List;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@NoArgsConstructor
public class FeishuNotificationConfigurationModel {
    @Nullable
    private String branch;
    @Nullable
    private List<String> devEnv;

    public boolean match(@Nullable String branchName, @Nullable ConfigEnv env) {
        // If the branch is null or empty or star, it means a wildcard that can match all branches.
        boolean branchMatched = branch == null || branch.isEmpty() || branch.equals("*") || branch.equals(branchName);

        // If the devEnv is null, it means a wildcard that can match all envs.
        boolean envMatched = devEnv == null || env == null || devEnv.contains(env.getName().name());

        return branchMatched && envMatched;
    }
}
