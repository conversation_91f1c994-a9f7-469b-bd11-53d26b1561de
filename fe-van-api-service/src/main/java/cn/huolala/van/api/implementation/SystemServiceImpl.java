package cn.huolala.van.api.implementation;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.ObjectMapper;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.VanHook;
import cn.huolala.van.api.model.system.*;
import cn.huolala.van.api.service.SystemService;

@Service
public class SystemServiceImpl implements SystemService {

    // TODO: Why is the hooks.json placed in OSS? Is migrating it to database the better solution?
    private static final String globalHooksFileName = ".van/hooks.json";

    @Value("${van.repository.module:gitlab}")
    private String module;

    @Value("${van.repository.gitlab:https://gitlab.huolala.cn}")
    private String gitlabUrl;

    @Value("${van.deploy.pre:}")
    private String pre;

    @Value("${van.devConfig:}")
    private String dev;

    @Value("${van.deploy.devRichHostSuffix:.huolala.cn,.huolala.work}")
    private String devRichHostSuffix;

    @Value("${van.triggerBaseURL:http://gitlab.van.hll.hui.lu:24359}")
    private String triggerBaseUrl;

    @Autowired
    private ObjectMapper mapper;

    @NonNull
    @Override
    public SystemConfig getConfig() {

        SystemConfig systemConfig = new SystemConfig();

        SystemDomain[] preList = null;
        if (pre != null && !pre.isEmpty()) {
            try {
                preList = mapper.readValue(pre, SystemDomain[].class);
            } catch (IOException e) {
                throw new InternalRequestException(e);
            }
        }
        SystemDeployEnv[] devList = null;
        if (dev != null && !dev.isEmpty()) {
            try {
                devList = mapper.readValue(dev, SystemDeployEnv[].class);
            } catch (IOException e) {
                throw new InternalRequestException(e);
            }
        }
        systemConfig.setRepository(module);
        systemConfig.setGitUrl(gitlabUrl);
        systemConfig.setPre(preList != null); // test it
        // handle env
        if (devList != null && devList.length > 0) {
            List<SystemStableEnv> seList = Arrays.stream(devList).map(d -> {
                SystemStableEnv se = new SystemStableEnv(d.getName(), d.getStableNumber());
                List<String> envList = IntStream.range(0, d.getStableNumber())
                        .mapToObj(n -> n == 0 ? "stable" : String.format("stable-%d", n))
                        .collect(Collectors.toList());
                se.setStableEnv(envList);
                return se;
            }).collect(Collectors.toList());
            systemConfig.setEnv(seList);
        }

        systemConfig.setAllowedDevDomain(
                // remove dot prefix
                Arrays.stream(devRichHostSuffix.split(",")).map(v -> {
                    if (v.startsWith(".")) {
                        return v.substring(1);
                    }
                    return v;
                }).collect(Collectors.toList()));

        Map<BuildTaskStatus, String> statusMap = new HashMap<>();
        statusMap.put(BuildTaskStatus.Ready, "waiting");
        statusMap.put(BuildTaskStatus.Running, "running");
        statusMap.put(BuildTaskStatus.Done, "success");
        statusMap.put(BuildTaskStatus.UploadFailed, "failed");
        statusMap.put(BuildTaskStatus.Failed, "failed");
        statusMap.put(BuildTaskStatus.Timeout, "timeout");
        statusMap.put(BuildTaskStatus.PushFailed, "push_failed");
        statusMap.put(BuildTaskStatus.ModifyGitlabFailed, "gitlab_error");

        systemConfig.setBuildStatusMapping(statusMap);

        List<Role> roles = new ArrayList<>();
        roles.add(Role.NoRole);
        roles.add(Role.TestRole);
        roles.add(Role.DevRole);
        roles.add(Role.AdminRole);
        systemConfig.setRoles(roles);

        systemConfig.setTriggerWebhook(triggerBaseUrl + "/trigger/repository");

        return systemConfig;
    }

    @Override
    public VanHook[] getGlobalHooks() {
        return Region.defaultStorage().getOptionalValue(globalHooksFileName, VanHook[].class).orElse(null);
    }

    @Override
    public void updateGlobalHooks(VanHook[] hooks) {
        Region.defaultStorage().putValue(globalHooksFileName, hooks);
    }
}
