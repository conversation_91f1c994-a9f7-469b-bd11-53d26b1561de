package cn.huolala.van.api.model.feishu;

import cn.huolala.van.api.dao.entity.FeishuSubscriptionEntity;
import cn.huolala.van.api.model.UserModel;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

@Getter
public class FeishuSubscriptionModel {
    @NonNull
    private final Long id;
    @NonNull
    private final UserModel user;
    @NonNull
    private final Long projectId;
    @NonNull
    private final String chatId;
    @NonNull
    private final FeishuChatInfoModel chat;
    @NonNull
    private final OffsetDateTime createdAt;
    @NonNull
    private final OffsetDateTime updatedAt;

    private FeishuSubscriptionModel(@NonNull FeishuSubscriptionEntity entity,
                                    @NonNull FeishuChatInfoModel chat,
                                    @NonNull UserModel user) {
        this.id = entity.getId();
        this.user = user;
        this.projectId = entity.getProjectId();
        this.chatId = entity.getChatId();
        this.chat = chat;
        final ZoneId z = ZoneId.systemDefault();
        this.createdAt = entity.getCreatedAt().atZone(z).toOffsetDateTime();
        this.updatedAt = entity.getUpdatedAt().atZone(z).toOffsetDateTime();
    }

    @Nullable
    public static FeishuSubscriptionModel from(@Nullable FeishuSubscriptionEntity entity,
                                               @Nullable FeishuChatInfoModel chat,
                                               @Nullable UserModel user) {
        if (entity == null || chat == null || user == null) return null;
        return new FeishuSubscriptionModel(entity, chat, user);
    }
}
