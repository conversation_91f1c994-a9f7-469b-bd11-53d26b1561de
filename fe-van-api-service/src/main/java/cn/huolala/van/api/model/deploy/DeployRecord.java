package cn.huolala.van.api.model.deploy;

import cn.huolala.van.api.dao.entity.DeployHistoryEntity;
import cn.huolala.van.api.model.ConfigEnv;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

@Getter
public class DeployRecord extends UserBase {
    private final long id;
    @NonNull
    private final Long projectId;
    @Nullable
    private final ConfigEnv env;
    @Nullable
    private final Long taskId;
    @NonNull
    private final OffsetDateTime createdAt;
    @NonNull
    private final OffsetDateTime updatedAt;

    private DeployRecord(@NonNull DeployHistoryEntity entity, @NonNull UserModel user) {
        super(user.getUniqId(), user.getName());
        id = entity.getId();
        projectId = entity.getProjectId();
        env = ConfigEnv.fromString(entity.getEnv());
        taskId = entity.getTaskId();
        createdAt = entity.getCreatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
        updatedAt = entity.getUpdatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }

    @Nullable
    public static DeployRecord create(@Nullable DeployHistoryEntity entity, @Nullable UserModel user) {
        if (entity == null || user == null) return null;
        return new DeployRecord(entity, user);
    }
}
