package cn.huolala.van.api.model.feishu;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static cn.huolala.van.api.model.feishu.FeishuCard.TagContent.md;
import static cn.huolala.van.api.util.ModelUtils.requireNonNullInGetter;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FeishuCard {
    @NonNull
    private Config config = new Config();
    @Nullable
    private Link cardLink;
    @NonNull
    private Header header = new Header();
    @NonNull
    private List<Element<?>> elements = new ArrayList<>();

    public FeishuCard(String title, Template template, Boolean wideScreenMode) {
        getConfig().setWideScreenMode(wideScreenMode);
        getHeader().setTitle(md(title));
        getHeader().setTemplate(template);
    }

    public void addElement(@NonNull Element<?> element) {
        elements.add(element);
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public enum TagType {
        plain_text, lark_md
    }

    public enum Template {
        DEFAULT, BLUE, WATHET, TURQUOISE, GREEN, YELLOW, ORANGE, RED, CARMINE, VIOLET, PURPLE, INDIGO, GREY;

        @JsonCreator
        public static Template createFromString(String name) {
            if (name == null || name.isEmpty()) return DEFAULT;
            return Template.valueOf(name.toUpperCase());
        }

        @JsonValue
        public String toJson() {
            return this.name().toLowerCase();
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Config {
        private boolean wideScreenMode;
        private boolean enableForward;
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Link {
        private String url;
        private String androidUrl;
        private String iosUrl;
        private String pcUrl;
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class Header {
        // only support plain_text
        private TagContent title;
        // https://open.feishu.cn/document/ukTMukTMukTM/ukTNwUjL5UDM14SO1ATN
        private Template template;
    }

    @Getter
    @Setter
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @NoArgsConstructor
    public static class Element<T extends Element<T>> {
        protected String tag;
        protected String layout;
        protected TagContent text;
        protected List<Field> fields;
        protected List<Element<?>> elements;
        protected TagContent alt;
        protected String imgKey;
        protected TagContent placeholder;

        public Element(@NonNull String tag) {
            this.tag = tag;
        }

        @NonNull
        @CanIgnoreReturnValue
        @SuppressWarnings("unchecked")
        public T appendTo(@NonNull FeishuCard card) {
            card.addElement(this);
            return (T) this;
        }

        public static class DivElement extends Element<DivElement> {
            public DivElement() {
                super("div");
            }

            @NonNull
            @CanIgnoreReturnValue
            public DivElement addField(@Nullable Field field) {
                if (field != null) {
                    if (fields == null) fields = new ArrayList<>();
                    fields.add(field);
                }
                return this;
            }

            @NonNull
            @CanIgnoreReturnValue
            public DivElement addFields(@NonNull Collection<Field> fields) {
                fields.forEach(this::addField);
                return this;
            }
        }

        public static class NoteElement extends Element<NoteElement> {
            public NoteElement() {
                super("note");
            }

            @NonNull
            @CanIgnoreReturnValue
            public NoteElement addNote(@Nullable Note note) {
                if (note != null) {
                    if (elements == null) elements = new ArrayList<>();
                    elements.add(note);
                }
                return this;
            }
        }

        public static class HrElement extends Element<HrElement> {
            public HrElement() {
                super("hr");
            }
        }

        /**
         * @see <a href="https://open.feishu.cn/document/feishu-cards/card-components/containers/form-container"></a>
         */
        // NOTE: feishu card json-1.0
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class FormElement extends Element<FormElement> {
            private String name;
            private Confirm confirm;
            public FormElement() {
                super("form");
            }
        }

        /**
         * @see <a href="https://open.feishu.cn/document/feishu-cards/card-components/interactive-components/input"></a>
         */
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class InputElement extends Element<InputElement> {
            private String name;
            private Boolean required;
            private String defaultValue;
            private TagContent label;
            private String labelPosition; // top left
            private String inputType; // text multiline_text password
            private Integer rows;
            private Object value;

            public InputElement() {
                super("input");
            }
        }

        /**
         * @see <a href="https://open.feishu.cn/document/feishu-cards/card-components/interactive-components/single-select-dropdown-menu"></a>
         */
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class SelectStaticElement extends Element<SelectStaticElement> {
            private String name;
            private Boolean required;
            private List<SelectStaticOption> options;
            private Object value;

            public SelectStaticElement() {
                super("select_static");
                options = new ArrayList<>();
            }


            @NonNull
            @CanIgnoreReturnValue
            public SelectStaticElement addOption(@NonNull SelectStaticOption option) {
                options.add(option);
                return this;
            }

            @Getter
            @Setter
            @AllArgsConstructor
            @NoArgsConstructor
            public static class SelectStaticOption {
                private TagContent text;

                private String value;

                public SelectStaticOption(String text, String value) {
                    this(TagContent.txt(text), value);
                }

                @NonNull
                @CanIgnoreReturnValue
                public SelectStaticOption appendTo(@NonNull SelectStaticElement parent) {
                    parent.addOption(this);
                    return this;
                }
            }
        }

        /**
         * @see <a href="https://open.feishu.cn/document/feishu-cards/card-components/interactive-components/button"></a>
         */
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class ButtonElement extends Element<ButtonElement> {
            private String name;
            private Confirm confirm;
            private FeishuActionValue.ButtonType type;
            private Object value;
            private String actionType; // in form container | form_submit form_reset

            public ButtonElement() {
                super("button");
            }
        }


        @Getter
        @Setter
        public static class ActionElement extends Element<ActionElement> {
            private List<Action<?>> actions;

            public ActionElement() {
                super("action");
            }

            @NonNull
            @CanIgnoreReturnValue
            public ActionElement addAction(Action<?> action) {
                if (actions == null) actions = new ArrayList<>();
                actions.add(action);
                return this;
            }
        }

        /**
         * @see <a href="https://open.feishu.cn/document/ukTMukTMukTM/uYzM3QjL2MzN04iNzcDN/component-list/common-components-and-elements"></a>
         */
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        @NoArgsConstructor
        public abstract static class Action<T extends Action<T>> {
            @Nullable
            private String tag;
            @Nullable
            private FeishuActionValue value;
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private Confirm confirm;
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private Boolean disabled;
            @JsonInclude(JsonInclude.Include.NON_NULL)
            private TagContent disabledTips;

            protected Action(@NonNull String tag) {
                this.tag = tag;
            }

            @NonNull
            @CanIgnoreReturnValue
            @SuppressWarnings("unchecked")
            public T appendTo(@NonNull ActionElement element) {
                element.addAction(this);
                return (T) this;
            }

            @NonNull
            public String getTag() {
                requireNonNullInGetter(tag);
                return tag;
            }

            @NonNull
            @CanIgnoreReturnValue
            @SuppressWarnings("unchecked")
            public T confirm(Confirm value) {
                setConfirm(value);
                return (T) this;
            }

            @NonNull
            @CanIgnoreReturnValue
            @SuppressWarnings("unchecked")
            public T disable(@Nullable String tip) {
                if (tip == null) {
                    disabled = false;
                    disabledTips = null;
                } else {
                    disabled = true;
                    disabledTips = TagContent.txt(tip);
                }
                return (T) this;
            }

        }

        /**
         * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-components/interactive-components/overflow"></a>
         */
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class OverflowAction extends Action<OverflowAction> {
            private List<OverflowOption> options;

            public OverflowAction() {
                super("overflow");
                options = new ArrayList<>();
            }

            public OverflowAction(@NonNull FeishuActionValue value) {
                this();
                setValue(value);
            }

            @NonNull
            @CanIgnoreReturnValue
            public OverflowAction addOption(@NonNull OverflowOption option) {
                options.add(option);
                return this;
            }

            @Getter
            @Setter
            @AllArgsConstructor
            public static class OverflowOption {
                private TagContent text;

                private String value;

                @NonNull
                @CanIgnoreReturnValue
                public OverflowOption appendTo(@NonNull OverflowAction parent) {
                    parent.addOption(this);
                    return this;
                }
            }
        }

        /**
         * @see <a href="https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-components/interactive-components/button"></a>
         */
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        @NoArgsConstructor
        public static class ButtonAction extends Action<ButtonAction> {
            private FeishuActionValue.ButtonType type;
            private TagContent text;
            private FeishuActionValue value;
            private String url;

            public ButtonAction(@NonNull FeishuActionValue.ButtonType type, @NonNull String text) {
                super("button");
                this.type = type;
                this.text = md(text);
            }

            public ButtonAction(@NonNull FeishuActionValue.ButtonType type, @NonNull String text, @NonNull FeishuActionValue value) {
                this(type, text);
                this.value = value;
            }

            public ButtonAction url(@NonNull String url) {
                this.url = url;
                return this;
            }

            @Getter
            @Setter
            @JsonInclude(JsonInclude.Include.NON_EMPTY)
            @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
            public static class Option {
                private TagContent text;
                private String value;
                @JsonProperty("URL")
                private String url;
            }
        }

        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        public static class Field {
            private TagContent text;
            @JsonProperty("is_short")
            private boolean isShort;

            public Field(@NonNull String markdown) {
                this.text = md(markdown);
            }

            @NonNull
            public static Field label(@NonNull String title, @Nullable String content) {
                return new Field(String.format("**%s**：%s", title, content == null ? "" : content));
            }

            @NonNull
            public static Field label(@NonNull String title, @NonNull String template, Object... args) {
                return new Field(String.format("**%s**：%s", title, String.format(template, args)));
            }

            @NonNull
            @CanIgnoreReturnValue
            public Field shorten() {
                this.isShort = true;
                return this;
            }

            @NonNull
            @CanIgnoreReturnValue
            public Field appendTo(@NonNull DivElement element) {
                element.addField(this);
                return this;
            }
        }

        // https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-components/content-components/note
        @Getter
        @Setter
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
        public static class Note extends Element<Note> {
            private String tag;
            private String content;
            private String imgKey;

            public static Note txt(@NonNull String content) {
                Note note = new Note();
                note.setTag("plain_text");
                note.setContent(content);
                return note;
            }

            @NonNull
            @CanIgnoreReturnValue
            public Note appendTo(@NonNull NoteElement element) {
                element.addNote(this);
                return this;
            }
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class TagContent {
        @NonNull
        private TagType tag = TagType.plain_text;
        @NonNull
        private String content = "";

        @NonNull
        public static TagContent txt(@NonNull String content) {
            return new TagContent(TagType.plain_text, content);
        }

        @NonNull
        public static TagContent md(@NonNull String content) {
            return new TagContent(TagType.lark_md, content);
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Confirm {
        private TagContent title;
        private TagContent text;

        public Confirm(String title, String text) {
            this(md(title), md(text));
        }
    }
}
