package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.Env;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

@Getter
@Setter
public class EnvRegionSnippet extends EnvRegion {
    @NonNull
    private Snippet snippet;

    public EnvRegionSnippet(@NonNull Env env, @NonNull Region region, @NonNull Snippet snippet) {
        super(env, region);
        this.snippet = snippet;
    }
}
