package cn.huolala.van.api.model.roles;

import cn.huolala.van.api.dao.entity.ProjectUserEntity;
import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.model.CmdbAppRegion;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.service.UserService;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Optional;
import java.util.stream.Stream;

@Getter
public class ProjectUserModel extends UserBase {
    @NonNull
    @JsonIgnore
    private final Long userId;

    @NonNull
    private final Role role;

    @NonNull
    private final Long projectId;

    @NonNull
    private final ProjectUserMeta meta;

    private ProjectUserModel(@NonNull ProjectUserEntity entity, @NonNull UserModel user) {
        super(user.getUniqId(), user.getName());
        userId = entity.getUserId();
        role = entity.getRole();
        projectId = entity.getProjectId();
        meta = ProjectUserMeta.createFromJson(entity.getMeta());
    }

    @Nullable
    public static ProjectUserModel fromEntity(@Nullable ProjectUserEntity entity) {
        if (entity == null) return null;
        UserService userService = ApplicationContextUtil.getBean(UserService.class);
        UserModel user = userService.getById(entity.getUserId());
        if (user == null) return null;
        if (!UserBase.uniqIdDetector.matcher(user.getUniqId()).matches()) return null;
        return new ProjectUserModel(entity, user);
    }

    @NonNull
    private Optional<RegionalInfoMap> fetchRim() {
        return Optional.of(meta).map(ProjectUserMeta::getRegionInfo);
    }

    @NonNull
    private Stream<UserRoleModel> flatMetaRoles() {
        return fetchRim().map(i -> i.entrySet().stream()).orElseGet(Stream::empty)
                .flatMap(ie -> {
                    CmdbAppRegion region = ie.getKey();
                    return ie.getValue().getRoles().stream().filter(i -> i != Role.NoRole).map(rv ->
                            new UserRoleModel(userUniqId, userName, region, rv)
                    );
                });
    }

    /**
     * TODO: Use `getRoleBitMap` instead.
     */
    public boolean checkRole(Role requiredRole) {
        if (role == requiredRole) return true;
        return fetchRim().map(i -> i.checkRole(requiredRole)).orElse(false);
    }

    public int getRoleBitmap() {
        return role.bitValue | fetchRim().map(RegionalInfoMap::getRoleBitmap).orElse(Role.NoRole.bitValue);
    }

    public Stream<UserRoleModel> flatRoles() {
        Stream.Builder<UserRoleModel> sb = Stream.builder();

        // Add roles from the info.
        flatMetaRoles().forEach(sb);

        // If the role is greater than info.getMaxRoleValue, it means the role is set by Van.
        if (role.ordinal() > fetchRim().map(RegionalInfoMap::getMaxRoleValue).orElse(Role.NoRole).ordinal()) {
            sb.accept(new UserRoleModel(userUniqId, userName, null, role));
        }

        return sb.build();
    }
}
