package cn.huolala.van.api.model.meta;

import cn.huolala.van.api.model.PdmInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PublishMetaInfo {
    @Nullable
    @JsonProperty("PlanID")
    private String planId;
    @Nullable
    @JsonProperty("Meta")
    private String meta;

    @NonNull
    public PdmInfo toPdmInfo() {
        return new PdmInfo(planId, meta);
    }
}
