package cn.huolala.van.api.model;

import java.util.List;

import cn.huolala.van.api.model.elatic.*;
import lombok.Data;

@Data
public class MiniProgramDetail {
	private long pv;
	private long uv;
	private List<FieldData> systemInfo;
	private ApiData api;
	private ApiData failApi;
	private FieldUpperDetail loadPackage;
	private FieldUpperDetail appLaunch;
	private FieldUpperDetail pageLoad;
	private FieldUpperDetail evaluateScript;
	private MetricData metric;
	private Error error;

	@Data
	public static class FieldUpperDetail {
		private Double upperValue;
		private List<FieldUpperData> upperDetail;
	}

	@Data
	public static class Error {
		private int notResolveErrorTotal;
		private int todayResolveErrorTotal;
		private List<FieldData> detail;
	}
}
