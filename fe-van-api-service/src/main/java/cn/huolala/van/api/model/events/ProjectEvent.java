package cn.huolala.van.api.model.events;

import cn.huolala.van.api.dao.entity.BuildTaskEntity;
import cn.huolala.van.api.dao.entity.ProjectEventsEntity;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.time.ZoneId;

@Getter
@Setter
public class ProjectEvent {
    @JsonIgnore
    @NonNull
    private final Long key;

    @NonNull
    private final ProjectEventType type;

    @NonNull
    private final Long projectId;

    @NonNull
    private final ProjectEventContent content;

    @Nullable
    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonProperty("app_id")
    private String appId;

    @NonNull
    private String hash;

    @Nullable
    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private String json;

    public ProjectEvent(@NonNull Long key,
                        @NonNull ProjectEventType type,
                        @NonNull Long projectId,
                        @NonNull ProjectEventContent content,
                        @Nullable String appId) {
        this.key = key;
        this.type = type;
        this.projectId = projectId;
        this.content = content;
        this.appId = appId;
        this.hash = buildHash(key, type, projectId, content);
    }

    @NonNull
    public static ProjectEvent create(@NonNull ProjectEventsEntity e) {
        ProjectEventType type = EnumUtils.getEnum(ProjectEventType.class, e.getName(), ProjectEventType.UnknownEvent);
        return new ProjectEvent(e.getId(), type, e.getProject(), type.buildContent(e.getContent()), e.getAppId());
    }

    @NonNull
    public static ProjectEvent create(@NonNull BuildTaskEntity e) {
        ProjectEventContent.TaskEvent content = new ProjectEventContent.TaskEvent(e.getId(), e.getStatus());
        long key = e.getUpdatedAt().atZone(ZoneId.systemDefault()).toEpochSecond();
        return new ProjectEvent(key, ProjectEventType.TaskEvent, e.getProjectId(), content, null);
    }

    @NonNull
    private static String buildHash(Object... args) {
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        try {
            String json = mapper.writeValueAsString(args);
            return DigestUtils.md5DigestAsHex(json.getBytes(StandardCharsets.UTF_8));
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
    }

    @NonNull
    @Deprecated
    @SuppressWarnings({"java:S1123", "java:S1133"})
    @ApiModelProperty("The property has been deprecated, use `projectId` instead.")
    public String getProject() {
        return projectId.toString();
    }

    @Override
    @NonNull
    public String toString() {
        if (json != null) return json;
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        try {
            json = mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
        return json;
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof ProjectEvent)) return false;
        return getHash().equals(((ProjectEvent) obj).getHash());
    }

    @Override
    public int hashCode() {
        return getHash().hashCode();
    }
}
