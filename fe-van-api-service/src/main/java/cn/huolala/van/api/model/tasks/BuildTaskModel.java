package cn.huolala.van.api.model.tasks;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.van.api.dao.entity.BuildTaskEntity;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;

import static cn.huolala.van.api.util.ModelUtils.parseStringJson;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang.StringUtils.defaultString;

@Getter
public class BuildTaskModel extends UserBase {
    public static final String NAME = "build task";

    private final long id;
    @NonNull
    private final String hash;
    @NonNull
    private final String branch;
    @NonNull
    private final String commitMessage;
    @NonNull
    private final BuildTaskType type;
    @NonNull
    private final BuildTaskStatus status;
    @NonNull
    private final Long projectId;
    @NonNull
    private final OffsetDateTime updatedAt;
    @NonNull
    private final OffsetDateTime createdAt;

    /**
     * This field is used in the backend only, should not be exposed to the frontend.
     */
    @NonNull
    @JsonIgnore
    private final String buildId;

    /**
     * This field has been deprecated, and its remaining in place is only to keep compatibility with Golang service.
     */
    @NonNull
    @JsonIgnore
    private final String legacyEmail;

    /**
     * This field has been deprecated, and its remaining in place is only to keep compatibility with Golang service.
     */
    @NonNull
    @JsonIgnore
    private final String legacyUsername;

    public BuildTaskModel(@NonNull BuildTaskEntity entity, @NonNull UserModel user) {
        super(user.getUniqId(), user.getName());

        id = ofNullable(entity.getId()).orElse(0L);
        hash = defaultString(entity.getHash());

        branch = defaultString(entity.getBranch());
        commitMessage = parseStringJson(entity.getCommitMessage());

        buildId = defaultString(entity.getBuildId());
        projectId = ofNullable(entity.getProjectId()).orElse(0L);

        status = ofNullable(entity.getStatus()).orElse(BuildTaskStatus.Ready);
        type = ofNullable(entity.getType()).orElse(BuildTaskType.NormalBuild);

        ZoneId sd = ZoneId.systemDefault();
        updatedAt = entity.getUpdatedAt().atZone(sd).toOffsetDateTime();
        createdAt = entity.getCreatedAt().atZone(sd).toOffsetDateTime();

        legacyEmail = defaultString(entity.getEmail());
        legacyUsername = defaultString(entity.getUsername());
    }

    @NonNull
    public static BuildTaskModel create(@NonNull BuildTaskEntity e, @Nullable UserModel u) {
        if (u == null) {
            u = UserModel.vanBot.getUniqId().equals(e.getUsername())
                    ? UserModel.vanBot
                    : UserModel.unknown;
        }
        return new BuildTaskModel(e, u);
    }
}
