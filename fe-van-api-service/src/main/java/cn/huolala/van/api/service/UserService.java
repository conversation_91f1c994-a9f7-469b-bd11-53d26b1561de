package cn.huolala.van.api.service;

import cn.huolala.van.api.dao.enums.Role;
import cn.huolala.van.api.model.Change;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.roles.ProjectUserMeta;
import cn.huolala.van.api.model.roles.ProjectUserModel;
import cn.huolala.van.api.model.roles.UserRoleModel;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.ToLongFunction;
import java.util.stream.Stream;

public interface UserService {
    @Nullable
    UserModel getByUniqId(@Nullable String uniqId);

    @NonNull
    Stream<UserModel> getByUniqId(@Nullable Collection<String> uniqIds);

    @Nullable
    UserModel getById(@Nullable Long id);

    @NonNull
    Map<Long, UserModel> getById(@NonNull Collection<Long> ids);

    @NonNull
    Set<Long> filterUserAccessibleProjectIds(@NonNull Collection<Long> ids, @NonNull UserModel user);

    @NonNull
    Set<Long> filterUserAccessibleProjectIds(
            @NonNull Collection<Long> ids,
            @NonNull UserModel user,
            boolean doNotCareSuperAdmin);

    @NonNull
    Map<Long, UserBase.Simple> findProjectOwner(@Nullable Set<Long> projectIds);

    @NonNull
    Stream<ProjectUserModel> findProjectUsers(@NonNull Set<Long> projectIds, @NonNull Role role);

    @NonNull
    Stream<ProjectUserModel> findProjectUsers(@Nullable Long projectId);

    @NonNull
    Stream<ProjectUserModel> findProjectUsers(@NonNull Set<Long> projectIds);

    @NonNull
    List<UserRoleModel> findRoles(Long projectId);

    @NonNull
    Map<Long, List<UserRoleModel>> findRoles(Set<Long> ids);

    @NonNull
    Map<String, UserModel> pullUserFromSsoIfNeeded(Collection<String> uniqIds);

    @Nullable
    UserModel pullUserFromSsoIfNeeded(String uniqId);

    long getTotalUserCount();

    int batchUpdateProjectUsers(@NonNull Long projectId, @NonNull Map<Long, ProjectUserMeta> data);

    @NonNull
    @CanIgnoreReturnValue
    Change<Role> updateProjectUser(@NonNull Long projectId, @NonNull Long userId, @NonNull Role role);

    @NonNull
    List<Long> findStaredProjectIdListByUserId(@NonNull Long userId);

    long removeProjectUser(Long projectId, Long userId);

    @CanIgnoreReturnValue
    int deleteStars(@NonNull Long userId, @NonNull List<Long> projectIds);

    @CanIgnoreReturnValue
    int insertStars(@NonNull Long userId, @NonNull List<Long> projectIds);

    @NonNull
    List<ProjectUserModel> findByProjectIdsAndUserId(Set<Long> ids, long userId);

    boolean isSuperAdmin(String userUniqId);

    boolean isParticipant(@Nullable UserModel user, @Nullable Long projectId);

    boolean isAdministrator(@Nullable UserModel user, @Nullable Long projectId);

    @NonNull
    Stream<UserBase.Simple> searchUser(@NonNull String keyword,
                                       @NonNull Long searcherId,
                                       @Nullable Long projectId,
                                       int limit);

    @NonNull
    <E, R> List<R> buildListWithUserModel(@NonNull Collection<E> list,
                                          @NonNull ToLongFunction<E> getUserId,
                                          @NonNull BiFunction<E, UserModel, R> toModel);

    @NonNull
    <E, R> List<R> buildListWithUserModel(@NonNull Collection<E> list,
                                          @NonNull Function<E, String> getUserUniqId,
                                          @NonNull BiFunction<E, UserModel, R> toModel);

    @NonNull
    <E, R> Stream<R> buildStreamWithUserModel(@NonNull Collection<E> entities,
                                              @NonNull Function<E, String> getUserUniqId,
                                              @NonNull BiFunction<E, UserModel, R> toModel);

    default int computeRoleBitmap(@NonNull UserModel user, long projectId) {
        return isSuperAdmin(user.getUniqId()) ? 7
                : findByProjectIdsAndUserId(Collections.singleton(projectId), user.getId())
                .stream().findFirst().map(ProjectUserModel::getRoleBitmap).orElse(0);
    }
}
