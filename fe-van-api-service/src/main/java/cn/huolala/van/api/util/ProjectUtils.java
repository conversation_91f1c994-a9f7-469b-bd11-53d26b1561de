
package cn.huolala.van.api.util;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.springframework.util.StringUtils;

import cn.lalaframework.logging.LoggerFactory;

public class ProjectUtils {

    private static String[] publicDomains = new String[] { ".huolala.cn", ".xiaolachuxing.com", ".xlcx.cn",
            ".lalafin.net", ".lalamove.com", ".uncle-delivery.com" };

    private static final Logger LOGGER = LoggerFactory.getLogger();

    public static String repositoryPath(String repositoryUrl) {
        try {
            URI uri = new URI(repositoryUrl);
            String path = uri.getPath();
            String repoPath = "";
            if (!StringUtils.hasText(path)) {
                repoPath = "";
            } else if (path.endsWith(".git")) {
                repoPath = path.substring(1, path.length() - 4);
            } else {
                repoPath = path.substring(1);
            }
            LOGGER.info("parse repositoryUrl {}, get repositoryPath {}", repositoryUrl, repoPath);
            return repoPath;
        } catch (URISyntaxException e) {
            LOGGER.error("failed to parse repositoryUrl to url with {}, error {}", repositoryUrl, e.getMessage());
            return "";
        }
    }

    public static boolean isPublicDomain(String domain) {
        return Stream.of(publicDomains).anyMatch(p -> domain.endsWith(p));
    }

}
