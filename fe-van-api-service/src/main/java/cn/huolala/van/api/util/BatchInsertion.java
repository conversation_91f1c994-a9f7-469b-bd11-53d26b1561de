package cn.huolala.van.api.util;

import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.lang.NonNull;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.function.Function;

public class BatchInsertion<T> {
    @NonNull
    private final String tableName;
    @NonNull
    private final List<Map.Entry<String, Function<T, ?>>> fields;

    @SafeVarargs
    public BatchInsertion(@NonNull String tableName, @NonNull Map.Entry<String, Function<T, ?>>... fields) {
        if (fields.length == 0) throw new InternalException("Cannot construct BatchInsertion with empty field list");
        this.tableName = tableName;
        this.fields = Arrays.asList(fields);
    }

    private static <T> void appendGroup(@NonNull StringBuilder target,
                                        @NonNull Collection<T> collection,
                                        @NonNull Function<T, CharSequence> toString) {
        for (Iterator<T> i = collection.iterator(); i.hasNext(); ) {
            target.append(toString.apply(i.next()));
            if (i.hasNext()) target.append(", ");
        }
    }

    private static Object toPlainValue(Object value) {
        if (value == null) return null;
        if (value instanceof Enum) return ((Enum<?>) value).ordinal();
        if (value instanceof Number || value instanceof String || value instanceof Boolean) {
            return value;
        }
        try {
            ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
            return mapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
    }

    @NonNull
    public Query buildQuery(@NonNull Collection<T> args) {
        if (args.isEmpty()) throw new InternalException("The args must not be empty here");

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ");
        sql.append(tableName);
        sql.append("(");
        appendGroup(sql, fields, Map.Entry::getKey);
        sql.append(")\nVALUES ");

        StringBuilder vt = new StringBuilder("(");
        appendGroup(vt, fields, i -> "?");
        vt.append(")");

        appendGroup(sql, args, i -> vt);

        EntityManager entityManager = ApplicationContextUtil.getBean(EntityManager.class);
        Query query = entityManager.createNativeQuery(sql.toString());
        int position = 0;
        for (T arg : args) {
            for (Map.Entry<String, Function<T, ?>> field : fields) {
                query.setParameter(++position, toPlainValue(field.getValue().apply(arg)));
            }
        }
        return query;
    }
}
