package cn.huolala.van.api.implementation;

import cn.huolala.van.api.dao.entity.ProjectDependencyEntity;
import cn.huolala.van.api.dao.entity.ProjectDependencyEntity.Type;
import cn.huolala.van.api.dao.repository.ProjectDependenciesRepository;
import cn.huolala.van.api.exception.DirtyDataException;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.BuildTaskService;
import cn.huolala.van.api.service.ProjectDependencyService;
import cn.huolala.van.api.service.TaskResourceService;
import com.fasterxml.jackson.databind.ObjectMapper;
import groovy.lang.Tuple4;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Optional.ofNullable;

@Service
public class ProjectDependencyServiceImpl implements ProjectDependencyService {
    private static final HashSet<String> popularDependencies = new HashSet<>(Arrays.asList(
        // frameworks
        "vue", "react", "react-dom",

        // libraries
        "antd", "antd-mobile", "ahooks",

        "vant", "element-ui", "element-plus", "view-design",

        // tools
        "@vue/cli-service", "umi", "react-scripts", "vite", "typescript", "rollup", "webpack"));

    @Autowired
    private ProjectDependenciesRepository projectDependenciesRepository;
    @Autowired
    private TaskResourceService taskResourceService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private EntityManager entityMapper;

    @Override
    @NonNull
    public List<ProjectDependencyEntity> findUsesByPackageName(@Nullable String packageName) {
        if (packageName == null) return Collections.emptyList();
        return projectDependenciesRepository.findUsesByPackageName(packageName);
    }

    @Transactional
    @Override
    public int saveTasks(@NonNull Collection<BuildTaskModel> tasks) {
        Set<Long> ids = tasks.stream().map(BuildTaskModel::getId).collect(Collectors.toSet());
        Set<Long> existedIds = projectDependenciesRepository.filterTaskIds(ids);
        List<Tuple4<Long, Type, String, String>> rows = tasks.stream().filter(i -> !existedIds.contains(i.getId()))
            .parallel().flatMap(task -> {
                HttpEntity<InputStream> entity = taskResourceService.getTaskMeta(task, AnalyseReport.fileName);
                if (entity == null) return Stream.empty();
                InputStream body = entity.getBody();
                if (body == null) return Stream.empty();
                try {
                    AnalyseReport report = mapper.readValue(body, AnalyseReport.class);
                    if (report == null) return Stream.empty();
                    return report.flattenWithTaskId(task.getId());
                } catch (IOException e) {
                    throw new DirtyDataException("Cannot parse " + AnalyseReport.fileName, e);
                }
            }).collect(Collectors.toList());
        return batchInsert(rows);
    }

    /**
     * TODO: Refactor with BatchInsertion
     */
    @Transactional
    public int batchInsert(@Nullable List<Tuple4<Long, Type, String, String>> data) {
        if (data == null || data.isEmpty()) return 0;

        Object[] parameters = data.stream()
            .flatMap(i -> Stream.of(i.getFirst(), i.getSecond().ordinal(), i.getThird(), i.getFourth())).toArray();

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO project_dependencies (task_id, type, name, version) VALUES ");
        for (int i = 0; i < parameters.length; i += 4) {
            if (i > 0) sql.append(", ");
            sql.append("(?, ?, ?, ?)");
        }

        Query query = entityMapper.createNativeQuery(sql.toString());
        for (int i = 0; i < parameters.length; i++) query.setParameter(i + 1, parameters[i]);
        return query.executeUpdate();
    }


    @Getter
    @Setter
    private static class AnalyseReport {
        public static final String fileName = "van-hook-dependence-analysis-task_result.json";

        private String content;
        private AnalyseReport.Meta meta;

        @NonNull
        public Stream<Tuple4<Long, Type, String, String>> flattenWithTaskId(@NonNull Long taskId) {
            if (meta == null) return Stream.empty();
            return Stream.of(fix(meta.getCommonDependencies()).filter(i -> popularDependencies.contains(i.getKey()))
                    .map(e -> new Tuple4<>(taskId, Type.detect(e.getKey()), e.getKey(), e.getValue())),
                fix(meta.getScopeDependencies()).map(
                    e -> new Tuple4<>(taskId, Type.detect(e.getKey()), e.getKey(), e.getValue())),
                fix(meta.getIndirectScopeDependencies()).map(
                    e -> new Tuple4<>(taskId, Type.INDIRECT, e.getKey(), e.getValue()))).flatMap(i -> i);
        }

        @NonNull
        private Stream<Map.Entry<String, String>> fix(@Nullable Map<String, String> map) {
            return ofNullable(map).orElseGet(Collections::emptyMap).entrySet().stream()
                .filter(i -> i.getKey() != null && i.getValue() != null);
        }

        @Getter
        @Setter
        public static class Meta {
            private Map<String, String> scopeDependencies;
            private Map<String, String> indirectScopeDependencies;
            private Map<String, String> commonDependencies;
        }
    }
}
