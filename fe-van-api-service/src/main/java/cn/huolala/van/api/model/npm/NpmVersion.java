
package cn.huolala.van.api.model.npm;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NpmVersion {
    private String id;
    private String name;
    private String version;
    private String description;
    private String readme;
    private String readmeFilename;
    private String gitHead;
    private String nodeVersion;
    private String npmVersion;
    private Map<String, Object> pkg;
    private List<NpmMaintainer> maintainers;
    private NpmDist dist;
}
