package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.van.api.dao.entity.MetaEntity;
import cn.huolala.van.api.model.Change;
import cn.huolala.van.api.model.MetaModel;
import cn.huolala.van.api.model.NonNullConsumer;
import cn.huolala.van.api.model.Processor;
import cn.huolala.van.api.model.events.ProjectEvent;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

public interface MetaService {
    @NonNull
    Optional<MetaModel> get(@NonNull Long metaId, @NonNull MetaType type);

    @NonNull
    <T> Optional<T> getOptional(@NonNull MetaType type, @NonNull Long metaId, @NonNull Class<T> valueType);

    @Nullable
    <T> T getValue(@NonNull MetaType type, @NonNull Long metaId, @NonNull Class<T> valueType);

    @Nullable
    String getValue(@NonNull MetaType type, @NonNull Long metaId);

    @NonNull
    <T> Map<Long, T> getValue(@NonNull MetaType type, @NonNull Collection<Long> metaIds,
                              @NonNull Class<T> valueType);

    @NonNull
    Map<Long, String> getValue(@NonNull MetaType type, @NonNull Collection<Long> metaIds);

    @NonNull
    <T> Change<T> setValue(@NonNull MetaType type,
                           @NonNull Long id,
                           @NonNull Class<T> modelType,
                           @Nullable T newValue);

    @NonNull
    <T> Change<T> partialUpdateValue(@NonNull MetaType type,
                                     @NonNull Long id,
                                     @NonNull Class<T> javaType,
                                     @NonNull Processor<T> processor);

    @NonNull
    <T> Map<Long, Change<T>> partialUpdateValue(@NonNull MetaType type,
                                                @NonNull Collection<Long> metaIds,
                                                @NonNull Class<T> javaType,
                                                @NonNull Processor<T> processor);

    @NonNull
    Change<MetaModel> partialUpdate(@NonNull MetaType type,
                                    @NonNull Long id,
                                    @NonNull NonNullConsumer<MetaModel> consumer);

    @NonNull
    Stream<ProjectEvent> findMetaEvents(long key, int limit);

    boolean hasValue(@NonNull MetaType metaType, long taskId);

    @NonNull
    Long create(@NonNull MetaEntity entity);
}
