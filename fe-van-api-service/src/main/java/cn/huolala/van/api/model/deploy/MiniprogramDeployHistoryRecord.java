package cn.huolala.van.api.model.deploy;

import cn.huolala.van.api.dao.entity.MiniprogramDeployHistoryEntity;
import cn.huolala.van.api.dao.enums.MiniprogramDeployType;

import java.time.OffsetDateTime;
import java.time.ZoneId;

import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Setter
@Getter
public class MiniprogramDeployHistoryRecord {
    private final Long id;
    @NonNull
    private final Long projectId;
    @NonNull
    private final Long taskId;
    @NonNull
    private final Long deployTaskId;
    @NonNull
    private final Long creatorId;
    @NonNull
    private final MiniprogramDeployType type;
    @NonNull
    private final Integer robot;
    @Nullable
    private final String version;
    @Nullable
    private final String description;
    @NonNull
    private final OffsetDateTime createdAt;
    @NonNull
    private final OffsetDateTime updatedAt;

    public MiniprogramDeployHistoryRecord(@NonNull MiniprogramDeployHistoryEntity entity) {
        this.id = entity.getId();
        this.projectId = entity.getProjectId();
        this.taskId = entity.getTaskId();
        this.deployTaskId = entity.getDeployTaskId();
        this.creatorId = entity.getCreatorId();
        this.type = entity.getType();
        this.robot = entity.getRobot();
        this.version = entity.getVersion();
        this.description = entity.getDescription();
        this.createdAt = entity.getCreatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
        this.updatedAt = entity.getUpdatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }
}
