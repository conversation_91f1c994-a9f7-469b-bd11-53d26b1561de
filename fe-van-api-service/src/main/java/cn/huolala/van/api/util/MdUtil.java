package cn.huolala.van.api.util;

import cn.lalaframework.tools.util.StringUtil;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class MdUtil {
    private MdUtil() {
    }

    public static DocBuilder doc() {
        return new DocBuilder();

    }

    public static UlBuilder ul() {
        return new UlBuilder();
    }

    public static TableBuilder table(String... headers) {
        return new TableBuilder(headers);
    }

    public static String link(Object text, Object url) {
        return String.format("[%s](%s)", text, url);
    }

    public static String bold(Object text) {
        return String.format("**%s**", text);
    }

    @NonNull
    public static String crlf2br(Object s) {
        return s.toString().replaceAll("\r\n|\r|\n", "<br/>");
    }

    public static class UlBuilder {
        private final List<String> list;
        private final int indent;

        public UlBuilder() {
            this(new ArrayList<>(), 0);
        }

        public UlBuilder(List<String> list, int indent) {
            this.list = list;
            this.indent = indent;
        }

        public UlBuilder indent() {
            return new UlBuilder(list, indent + 1);
        }

        public UlBuilder add(@NonNull Object label, @Nullable Object value) {
            add(label + "：" + value);
            return this;
        }

        public UlBuilder add(@NonNull Object text) {
            list.add(StringUtil.repeat("  ", indent) + "- " + crlf2br(text));
            return this;
        }

        public String build() {
            return StringUtil.join(list, "\n") + "\n";
        }

        public void appendTo(DocBuilder doc) {
            list.forEach(doc::add);
        }
    }

    public static class TableBuilder {
        private final List<String> list;

        public TableBuilder(String... headers) {
            list = new ArrayList<>();
            add(headers);
            String[] s = new String[headers.length];
            Arrays.fill(s, "-");
            add(s);
        }

        public TableBuilder add(Object... rowCells) {
            StringBuilder sb = new StringBuilder();
            sb.append("|");
            for (Object s : rowCells) {
                sb.append(" ");
                sb.append(s == null ? "-" : crlf2br(s));
                sb.append(" |");
            }
            list.add(sb.toString());
            return this;
        }

        public String build() {
            return StringUtil.join(list, "\n") + "\n";
        }
    }

    public static class DocBuilder {
        private final List<String> list;

        public DocBuilder() {
            this.list = new ArrayList<>();
        }

        public DocBuilder h3(@NonNull String text) {
            list.add("### " + text);
            return this;
        }

        public String build() {
            return StringUtil.join(list, "\n") + "\n";
        }

        public DocBuilder add(String line) {
            list.add(line);
            return this;
        }
    }
}
