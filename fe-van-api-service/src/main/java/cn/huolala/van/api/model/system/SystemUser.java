package cn.huolala.van.api.model.system;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

import cn.huolala.van.api.model.UserModel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SystemUser {

    @JsonProperty("id")
    private long id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("uniqId")
    private String uniqId;

    @JsonProperty("email")
    private String email;

    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    @JsonProperty("updatedAt")
    private LocalDateTime updatedAt;

    @JsonProperty("isSuperAdmin")
    private boolean isSuperAdmin;

    public SystemUser(UserModel userModel) {
        this.id = userModel.getId();
        this.name = userModel.getName();
        this.email = userModel.getEmail();
        this.uniqId = userModel.getUniqId();
        this.createdAt = userModel.getCreatedAt();
        this.updatedAt = userModel.getUpdatedAt();
    }
}
