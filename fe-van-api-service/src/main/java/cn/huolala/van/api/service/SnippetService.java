package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.EnvRegionName;
import cn.huolala.van.api.model.EnvRegionSnippet;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.Snippet;
import org.springframework.lang.NonNull;

import java.util.List;

public interface SnippetService {
    @NonNull
    List<EnvRegionSnippet> findInProject(long projectId, @NonNull List<EnvRegionName> requests);

    void updateInProject(
            long projectId,
            @NonNull Env env,
            @NonNull Region region,
            @NonNull List<Snippet.Update> updates);

    @NonNull
    List<EnvRegionSnippet> findInGlobal(@NonNull List<EnvRegionName> requests);

    void updateInGlobal(
            @NonNull Env env,
            @NonNull Region region,
            @NonNull List<Snippet.Update> updates);
}
