package cn.huolala.van.api.model.feishu;

import cn.huolala.van.api.model.ConfigEnv;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.ButtonAction;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.OverflowAction;
import cn.lalaframework.config.core.PropertyConfigurer;
import cn.lalaframework.spring.ApplicationContextUtil;
import cn.lalaframework.tools.util.HexUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Map;

import static cn.huolala.van.api.util.ModelUtils.requireNonNullInGetter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class FeishuActionValue {
    private String type;
    private String createdAt;
    private String chatId;
    private String sign;

    protected FeishuActionValue(@NonNull String type) {
        this.type = type;
    }

    @NonNull
    public ButtonAction wrapAsButton(@NonNull String text) {
        return new ButtonAction(ButtonType.DEFAULT, text, this);
    }

    @NonNull
    public ButtonAction wrapAsButton(@NonNull String text, @NonNull ButtonType type) {
        return new ButtonAction(type, text, this);
    }

    @NonNull
    public OverflowAction wrapAsOverflowAction() {
        return new OverflowAction(this);
    }

    @SneakyThrows
    @CanIgnoreReturnValue
    public FeishuActionValue sign() {
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        createdAt = String.valueOf(System.currentTimeMillis());
        sign = null;

        MessageDigest md = MessageDigest.getInstance("md5");
        mapper.convertValue(this, new TypeReference<Map<String, String>>() {
            }).entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue)
            .map(i -> i.getBytes(StandardCharsets.UTF_8)).forEach(md::update);

        String secret = PropertyConfigurer.getString("van.feishu.secret", "");
        md.update(secret.getBytes(StandardCharsets.UTF_8));

        sign = HexUtil.encodeHexStr(md.digest());

        return this;
    }

    public FeishuActionValue chatId(@Nullable String chatId) {
        this.chatId = chatId;
        return this;
    }

    public enum ButtonType {
        DEFAULT, PRIMARY, DANGER;

        @JsonCreator
        public static ButtonType createFromString(String name) {
            if (name == null || name.isEmpty()) return DEFAULT;
            return ButtonType.valueOf(name.toUpperCase());
        }

        @JsonValue
        public String toJson() {
            return this.name().toLowerCase();
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ConfirmToRollback extends FeishuActionValue {
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long projectId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long canaryId;

        public ConfirmToRollback() {
            super("confirmToRollback");
        }

        public ConfirmToRollback(@NonNull Long projectId, @NonNull Long canaryId) {
            this();
            this.projectId = projectId;
            this.canaryId = canaryId;
        }

        @NonNull
        public Long getCanaryId() {
            requireNonNullInGetter(canaryId);
            return canaryId;
        }

        @NonNull
        public Long getProjectId() {
            requireNonNullInGetter(projectId);
            return projectId;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PerformRollback extends FeishuActionValue {
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long projectId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long canaryId;

        public PerformRollback() {
            super("performRollback");
        }

        public PerformRollback(@NonNull Long projectId, @NonNull Long canaryId) {
            this();
            this.projectId = projectId;
            this.canaryId = canaryId;
        }

        @NonNull
        public Long getCanaryId() {
            requireNonNullInGetter(canaryId);
            return canaryId;
        }

        @NonNull
        public Long getProjectId() {
            requireNonNullInGetter(projectId);
            return projectId;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class ConfirmToRelease extends FeishuActionValue {
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long projectId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long taskId;

        public ConfirmToRelease() {
            super("confirmToRelease");
        }

        public ConfirmToRelease(@NonNull Long projectId, @NonNull Long canaryId) {
            this();
            this.projectId = projectId;
            this.taskId = canaryId;
        }

        @NonNull
        public Long getTaskId() {
            requireNonNullInGetter(taskId);
            return taskId;
        }

        @NonNull
        public Long getProjectId() {
            requireNonNullInGetter(projectId);
            return projectId;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PerformRelease extends FeishuActionValue {
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long projectId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long taskId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long headPoint;

        public PerformRelease() {
            super("performRelease");
        }

        public PerformRelease(@NonNull Long projectId, @NonNull Long taskId, @Nullable Long headPoint) {
            this();
            this.projectId = projectId;
            this.taskId = taskId;
            this.headPoint = headPoint;
        }

        @NonNull
        public Long getTaskId() {
            requireNonNullInGetter(taskId);
            return taskId;
        }

        @NonNull
        public Long getProjectId() {
            requireNonNullInGetter(projectId);
            return projectId;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PerformDeploy extends FeishuActionValue {
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long projectId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long taskId;
        @Nullable
        private String env;

        public PerformDeploy() {
            super("performDeploy");
        }

        public PerformDeploy(@NonNull Long projectId, @NonNull Long taskId, @NonNull ConfigEnv env) {
            this();
            this.projectId = projectId;
            this.taskId = taskId;
            this.env = env.toString();
        }

        public PerformDeploy(@NonNull Long projectId, @NonNull Long taskId) {
            this();
            this.projectId = projectId;
            this.taskId = taskId;
        }

        @NonNull
        public Long getTaskId() {
            requireNonNullInGetter(taskId);
            return taskId;
        }

        @NonNull
        public Long getProjectId() {
            requireNonNullInGetter(projectId);
            return projectId;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    public static class PerformMiniprogramPreview extends FeishuActionValue {
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long projectId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private Long taskId;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private String robot;
        @JsonFormat(shape = JsonFormat.Shape.STRING)
        private String deployType; // preview upload
        @Nullable
        private String version;
        @Nullable
        private String description;

        public PerformMiniprogramPreview() {
            super("performMiniprogramPreview");
        }

        public PerformMiniprogramPreview(@NonNull Long projectId,
                                         @NonNull Long taskId,
                                         @NonNull String robot,
                                         @NonNull String deployType,
                                         @Nullable String version,
                                         @Nullable String description) {
            this();
            this.projectId = projectId;
            this.taskId = taskId;
            this.robot = robot;
            this.deployType = deployType;
            this.version = version;
            this.description = description;
        }

    }

}
