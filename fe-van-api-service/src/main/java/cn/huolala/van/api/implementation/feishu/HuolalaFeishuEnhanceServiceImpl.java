package cn.huolala.van.api.implementation.feishu;

import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.model.ApprovalForm;
import cn.huolala.van.api.service.feishu.HuolalaFeishuEnhanceService;
import cn.lalaframework.logging.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lark.oapi.Client;
import com.lark.oapi.core.response.BaseResponse;
import com.lark.oapi.service.approval.v4.model.CreateInstanceReq;
import com.lark.oapi.service.approval.v4.model.CreateInstanceResp;
import com.lark.oapi.service.approval.v4.model.InstanceCreate;
import com.lark.oapi.service.contact.v3.enums.BatchGetIdUserUserIdTypeEnum;
import com.lark.oapi.service.contact.v3.enums.BatchUserUserIdTypeEnum;
import com.lark.oapi.service.contact.v3.enums.GetUserUserIdTypeEnum;
import com.lark.oapi.service.contact.v3.model.*;

import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class HuolalaFeishuEnhanceServiceImpl implements HuolalaFeishuEnhanceService {

    private static final Logger LOGGER = LoggerFactory.getLogger();

    private static final String[] EMAIL_SUFFIXES = {"@huolala.cn", "@lalamove.com"};


    @Autowired
    private CloseableHttpClient httpClient;

    @Resource(name = "van")
    private Client feishuClient;

    @Autowired
    private ObjectMapper mapper;

    @Override
    @Nullable
    public String getUniqNameByLarkUserId(String larkUserId) {
        if (StringUtils.isEmpty(larkUserId)) {
            return null;
        }
        try {
            GetUserResp getUserResp = feishuClient.contact().user().get(
                GetUserReq.newBuilder()
                    .userId(larkUserId)
                    .userIdType(GetUserUserIdTypeEnum.USER_ID)
                    .build()
            );
            return getFeishuData(getUserResp).getUser().getEnName();
        } catch (Exception e) {
            throw new InternalRequestException(String.format("get sso account name from feishu failed, %s", larkUserId), e);
        }
    }

    private <T> T getFeishuData(BaseResponse<T> resp) {
        if (resp == null) {
            throw new InternalRequestException("Feishu response is null");
        }
        if (!resp.success()) {
            throw new InternalRequestException(String.format("Feishu request failed: %s", resp.getMsg()));
        }
        if (resp.getData() == null) {
            throw new InternalRequestException("Feishu response data is null");
        }
        return resp.getData();
    }

    @Override
    @Nullable
    public Map<String, String> uniqNamesInLarkUserId(List<String> larkUserIds) {
        if (larkUserIds == null || larkUserIds.isEmpty()) {
            return null;
        }
        BatchUserReq req = BatchUserReq.newBuilder()
            .userIdType(BatchUserUserIdTypeEnum.USER_ID)
            .userIds(larkUserIds.toArray(new String[0]))
            .build();
        BatchUserResp resp;
        try {
            resp = feishuClient.contact().user().batch(req);
        } catch (Exception e) {
            throw new InternalRequestException("get uniq names from feishu failed, larkUserIds: " + larkUserIds, e);
        }
        return Arrays.stream(getFeishuData(resp).getItems())
            .collect(Collectors.toMap(User::getUserId, User::getEnName));
    }

    @Override
    @NonNull
    public Map<String, String> larkUserIdInUniqNames(@Nullable Collection<String> uniqIds) {
        if (uniqIds == null || uniqIds.isEmpty()) return Collections.emptyMap();

        return Arrays.asList(EMAIL_SUFFIXES).parallelStream().map(suffix -> {
                BatchGetIdUserReqBody body = BatchGetIdUserReqBody.newBuilder()
                    .emails(uniqIds.stream().map(uniqId -> uniqId + suffix).toArray(String[]::new))
                    .build();
                BatchGetIdUserReq req = BatchGetIdUserReq.newBuilder()
                    .userIdType(BatchGetIdUserUserIdTypeEnum.USER_ID)
                    .batchGetIdUserReqBody(body)
                    .build();
                BatchGetIdUserResp batchGetIdUserResp;
                try {
                    batchGetIdUserResp = feishuClient.contact().user().batchGetId(req);
                } catch (Exception e) {
                    throw new InternalRequestException(String.format("get lark user id from uniqId failed, %s", uniqIds), e);
                }

                return Arrays.stream(getFeishuData(batchGetIdUserResp).getUserList()).filter(user -> user.getUserId() != null)
                    .collect(Collectors.toMap((user) -> user.getEmail().replaceAll(suffix, ""), UserContactInfo::getUserId));
            }).flatMap(map -> map.entrySet().stream())
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    // NOTE: use new feishu api to create approval, please check the doc below
    // https://open.feishu.cn/document/server-docs/approval-v4/instance/create
    @NonNull
    @Override
    public String createApproval(@NonNull String approvalCode, @NonNull String userId, ApprovalForm[] forms) {
        String formContent = "";
        try {
            formContent = mapper.writeValueAsString(forms);
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
        if (formContent.isEmpty()) {
            throw new InternalRequestException("approval form content cannot be empty");
        }
        InstanceCreate instanceCreate = InstanceCreate.newBuilder()
                .approvalCode(approvalCode)
                .userId(userId)
                .form(formContent)
                .build();
        CreateInstanceReq req = CreateInstanceReq.newBuilder().instanceCreate(instanceCreate).build();
        try {
            CreateInstanceResp resp = feishuClient.approval().instance().create(req);
            if (!resp.success()) {
                throw new InternalRequestException(
                        "failed to create feishu approval, " + resp.getMsg() + " code " + resp.getCode());
            }
            return getFeishuData(resp).getInstanceCode();
        } catch (Exception e) {
            LOGGER.error("feishu approval instance create error", e);
            throw new InternalRequestException(e);
        }
    }

}
