package cn.huolala.van.api.service;

import cn.huolala.van.api.model.PmisReleasePlan;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

public interface PmisService {
    @NonNull
    List<PmisReleasePlan> getAvailableReleasePlanList(@NonNull ProjectModel project, @Nullable Region region);

    void notify(@NonNull UserModel user,
                @NonNull ProjectModel project,
                @NonNull List<BuildTaskModel> tasks,
                long launchId);
}
