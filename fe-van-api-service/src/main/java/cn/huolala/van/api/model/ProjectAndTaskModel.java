package cn.huolala.van.api.model;

import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.NonNull;

@Getter
@AllArgsConstructor
public class ProjectAndTaskModel {
    @NonNull
    private final ProjectModel project;
    @NonNull
    private final BuildTaskModel task;
}
