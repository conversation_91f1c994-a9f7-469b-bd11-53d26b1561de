package cn.huolala.van.api.service;

import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.events.ProjectEventContent;
import cn.huolala.van.api.model.events.ProjectEventListener;
import cn.huolala.van.api.model.events.ProjectEventType;
import cn.huolala.van.api.model.project.ProjectModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

public interface EventService {
    void add(@NonNull UserModel user,
             @NonNull ProjectModel project,
             @NonNull ProjectEventContent content);

    @NonNull
    List<ProjectEvent> findEvents(Long projectId, ProjectEventType type);

    @NonNull
    ProjectEventListener createListener(long projectId, @Nullable String lastEventId);
}
