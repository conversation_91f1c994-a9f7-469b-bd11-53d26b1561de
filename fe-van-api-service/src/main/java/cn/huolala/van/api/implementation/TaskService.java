package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.service.MonitorRecordService;
import cn.huolala.van.api.service.ProjectService;
import cn.lalaframework.job.sdk.HllXxlJobManager;
import cn.lalaframework.job.sdk.annotation.HllXxlJob;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;
import java.util.List;

@Component
public class TaskService {
    @Autowired
    private MonitorRecordService monitorRecordService;

    @Autowired
    private ProjectService projectService;

    @HllXxlJob(value = "getMiniProgramScore")
    public void getMiniPorgramScore() throws Exception {
        String command = HllXxlJobManager.getJobParam();
        try {
            HllXxlJobManager.log(command);
            List<String> projectNames = JSON.parseArray(command, String.class);
            for (String projectName : projectNames) {
                try {
                    HllXxlJobManager.log("caculate project " + projectName + " miniProgram score start");
                    monitorRecordService.saveLightAppProjectScoreByName(projectName);
                    HllXxlJobManager.log("caculate project " + projectName + " miniProgram score success");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            HllXxlJobManager.log(e.getMessage(), e);
            HllXxlJobManager.handleFail(e.getMessage());
        }
    }

    @HllXxlJob(value = "getAllMiniProgramScore")
    public void getAllMiniPorgramScore() throws Exception {
        try {
            List<String> projectNames = projectService.findNamesByTypes(ProjectType.Miniprogram)
                    .collect(Collectors.toList());
            for (String projectName : projectNames) {
                try {
                    monitorRecordService.saveLightAppProjectScoreByName(projectName);
                    HllXxlJobManager.log("caculate project " + projectName + " miniProgram score success");
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            HllXxlJobManager.log(e.getMessage(), e);
            HllXxlJobManager.handleFail(e.getMessage());
        }
    }

    @HllXxlJob(value = "createMonitorRecordScoreTaskByUsers")
    public void createMonitorRecordScoreTaskByUsers() {
        String command = HllXxlJobManager.getJobParam();
        try {
            HllXxlJobManager.log(command);
            List<String> users = JSON.parseArray(command, String.class);
            monitorRecordService.sendUserProjectsYesterDayMonitorRecordMessage(users);
        } catch (Exception e) {
            HllXxlJobManager.log(e.getMessage(), e);
            HllXxlJobManager.handleFail(e.getMessage());
        }
    }

    @HllXxlJob(value = "createMonitorRecordScoreTask")
    public void createMonitorRecordScoreTask() {
        try {
            monitorRecordService.sendProjectsYesterDayMonitorRecordMessage();
        } catch (Exception e) {
            HllXxlJobManager.log(e.getMessage(), e);
            HllXxlJobManager.handleFail(e.getMessage());
        }
    }
}
