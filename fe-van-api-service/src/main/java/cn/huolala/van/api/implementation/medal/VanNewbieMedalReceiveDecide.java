package cn.huolala.van.api.implementation.medal;

import cn.huolala.api.constants.enums.MedalEnum;
import cn.huolala.van.api.dao.entity.UserEntity;
import cn.huolala.van.api.service.MedalReceiveDecideService;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class VanNewbieMedalReceiveDecide implements MedalReceiveDecideService {

    @Override
    public boolean receive(UserEntity user, LocalDateTime end) {
        // 进入到这里，说明用户一定有项目
        return true;
    }

    @Override
    public MedalEnum medal() {
        return MedalEnum.VAN_NEWBIE;
    }
}
