package cn.huolala.van.api.implementation;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.PmisService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static cn.huolala.van.api.util.EncodingUtils.buildQueryString;

@Service
public class PmisServiceImpl implements PmisService {
    @Value("${van.pdm.api.url:}")
    private String url;

    @Autowired
    private ObjectMapper mapper;

    @NonNull
    private static String toPmisRegion(@NonNull Region region) {
        String pmisRegion;
        switch (region) {
            case bom:
                pmisRegion = "3";
                break;
            case sao:
                pmisRegion = "4";
                break;
            case sin:
                pmisRegion = "2";
                break;
            default:
                throw new VanBadRequestException(String.format("Unsupported pmis region '%s'", region));
        }
        return pmisRegion;
    }

    @Override
    @NonNull
    public List<PmisReleasePlan> getAvailableReleasePlanList(@NonNull ProjectModel project, @Nullable Region region) {
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("releaseEnv", "prd"));
        params.add(new BasicNameValuePair("serviceName", project.getConfig().getAppId()));
        StringBuilder sb = new StringBuilder(url);
        if (region == null || region == Region.defaultRegion()) {
            sb.append("/polaris-gateway/pmis/getAvailableReleasePlanList");
        } else {
            params.add(new BasicNameValuePair("region", toPmisRegion(region)));
            sb.append("/polaris-gateway/pmis/llm/getLlmReleasePlan");
        }
        sb.append(buildQueryString(params));
        PmisReleasePlan[] arr = CommonResponse.execute(new HttpGet(sb.toString()), PmisReleasePlan[].class);
        if (arr == null) return Collections.emptyList();
        return Arrays.asList(arr);
    }

    @Override
    public void notify(@NonNull UserModel user,
                       @NonNull ProjectModel project,
                       @NonNull List<BuildTaskModel> tasks,
                       long launchId) {
        HttpPost req = new HttpPost(url + "/polaris-gateway/pmis/insertReleaseRecord");

        PmisReleaseRecord pmis = new PmisReleaseRecord(user, project, tasks, launchId);

        try {
            req.setHeader("Content-Type", "application/json");
            String jsonString = mapper.writeValueAsString(pmis);
            req.setEntity(new StringEntity(jsonString, ContentType.APPLICATION_JSON));
        } catch (IOException e) {
            throw new InternalMappingException(e);
        }

        CommonResponse.execute(req);
    }
}
