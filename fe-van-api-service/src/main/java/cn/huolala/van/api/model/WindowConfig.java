package cn.huolala.van.api.model;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class WindowConfig {
    private boolean global;
    private boolean pdm;
    private boolean office;

    private List<String> week;
    private List<String> appid;
    @JsonProperty("pdm_appid")
    private List<String> pdmAppid;
    @JsonProperty("custom_range")
    private Map<String, CustomRange> customRange;
    @JsonProperty("default_range")
    private String defaultRange;
    @JsonProperty("special_range")
    private Map<String, String> specialRange;
    @JsonProperty("idc_range")
    private Map<Region, RegionRangeValue> idcRange;


    @Getter
    @Setter
    public static class Range {
        private int week;
        private RangeSpan range;
    }

    @Getter
    @Setter
    public static class RangeSpan {
        private String start;
        private String end;
    }

    @Getter
    @Setter
    public static class CustomRange {
        private String description;
        private List<Range> range;
    }

    @Getter
    @Setter
    public static class RegionRangeValue {
        @JsonProperty("default") // default is a keyword in java
        private String regionDefault;

        private Map<String, String> special;

        private boolean global;
    }
}
