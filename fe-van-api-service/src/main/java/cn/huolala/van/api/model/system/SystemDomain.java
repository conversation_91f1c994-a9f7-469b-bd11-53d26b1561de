package cn.huolala.van.api.model.system;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;


@Getter
@Setter
@NoArgsConstructor
public class SystemDomain {

    @JsonProperty("host")
    private String host;

    @JsonProperty("staticDomain")
    private String staticDomain;


    @JsonProperty("richHostSuffix")
    private String richHostSuffix;

    @JsonProperty("isLan")
    private boolean isLan;
}
