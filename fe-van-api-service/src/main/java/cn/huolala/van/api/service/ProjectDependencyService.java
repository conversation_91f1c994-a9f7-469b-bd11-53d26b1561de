package cn.huolala.van.api.service;

import cn.huolala.van.api.dao.entity.ProjectDependencyEntity;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface ProjectDependencyService {
    @NonNull
    List<ProjectDependencyEntity> findUsesByPackageName(@Nullable String packageName);

    @Transactional
    int saveTasks(@NonNull Collection<BuildTaskModel> tasks);
}
