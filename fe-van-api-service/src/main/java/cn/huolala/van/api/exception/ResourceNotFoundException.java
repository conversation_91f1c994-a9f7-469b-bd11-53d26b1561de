package cn.huolala.van.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.function.Supplier;

@ResponseStatus(code = HttpStatus.NOT_FOUND)
public class ResourceNotFoundException extends RuntimeException {
    public ResourceNotFoundException(String message) {
        super(message);
    }

    public ResourceNotFoundException(String message, Exception cause) {
        super(message, cause);
    }

    /**
     * Create a ResourceNotFoundException.
     *
     * @param resourceName Lower case is recommended.
     */
    public static ResourceNotFoundException create(@NonNull String resourceName,
                                                   @NonNull String keyName,
                                                   @Nullable Object key) {
        String msg = String.format("The %s is not found where %s = %s", resourceName, keyName, key);
        return new ResourceNotFoundException(msg);
    }

    public static Supplier<ResourceNotFoundException> curryResourceNotFoundException(@NonNull String resourceName,
                                                                                     @NonNull String keyName,
                                                                                     @Nullable Object key) {
        return () -> create(resourceName, keyName, key);
    }
}
