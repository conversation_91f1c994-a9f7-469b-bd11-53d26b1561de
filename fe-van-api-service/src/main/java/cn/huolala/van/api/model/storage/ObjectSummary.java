package cn.huolala.van.api.model.storage;

import cn.lalaframework.storage.adapter.ObjectResult;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * TODO: Remove this class, use StorageObject directly.
 */
@Getter
@Setter
@NoArgsConstructor
public class ObjectSummary {
    /**
     * The key under which this object is stored
     */
    private String key;

    private String eTag;

    private long size;

    private Date lastModified;

    public static ObjectSummary from(ObjectResult o) {
        ObjectSummary n = new ObjectSummary();
        n.setKey(o.getKey());
        n.setETag(o.getETag());
        n.setSize(o.getSize());
        n.setLastModified(o.getLastModified());
        return n;
    }
}
