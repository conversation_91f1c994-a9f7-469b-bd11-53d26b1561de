package cn.huolala.van.api.model.storage;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.http.entity.ContentType;

@Getter
@Setter
@Builder
public class ObjectSimpleMetadata {
    public static final ObjectSimpleMetadata Json = ObjectSimpleMetadata.builder()
            .contentType(ContentType.APPLICATION_JSON.getMimeType()).build();
    private String contentType;
}
