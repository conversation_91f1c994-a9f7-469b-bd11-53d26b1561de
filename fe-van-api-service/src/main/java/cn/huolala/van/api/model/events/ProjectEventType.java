package cn.huolala.van.api.model.events;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@JsonFormat(shape = JsonFormat.Shape.STRING)
public enum ProjectEventType {
    @JsonEnumDefaultValue
    UnknownEvent(ProjectEventContent.Unknown.class),
    EditProjectConfigEvent(ProjectEventContent.EditProjectConfig.class),
    EditProjectDetailEvent(ProjectEventContent.EditProjectDetail.class),
    LaunchProdEvent(ProjectEventContent.LaunchProd.class),
    LaunchTestEvent(ProjectEventContent.LaunchTest.class),
    TaskEvent(ProjectEventContent.TaskEvent.class),
    TaskSyncEvent(ProjectEventContent.TaskSyncEvent.class);

    private final Class<? extends ProjectEventContent> contentType;

    ProjectEventType(Class<? extends ProjectEventContent> contentType) {
        this.contentType = contentType;
    }

    public Class<? extends ProjectEventContent> getContentType() {
        return this.contentType;
    }

    @NonNull
    public ProjectEventContent buildContent(@Nullable String raw) {
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        try {
            return mapper.readValue(raw == null || raw.isEmpty() ? "{}" : raw, contentType);
        } catch (JsonProcessingException exception) {
            throw new InternalMappingException(exception);
        }
    }
}
