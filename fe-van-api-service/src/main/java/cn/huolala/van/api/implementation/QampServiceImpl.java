package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.QampService;
import cn.huolala.van.api.service.UserService;
import cn.lalaframework.config.core.PropertyConfigurer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collection;
import java.util.Collections;

@Service
public class QampServiceImpl implements QampService {
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private UserService userService;

    @Override
    public void notify(@NonNull UserModel user,
                       @NonNull ProjectModel project,
                       @NonNull Collection<BuildTaskModel> tasks,
                       @NonNull Env env,
                       @NonNull Region region,
                       @Nullable Long launchId) {
        String url = PropertyConfigurer.getString("van.qamp.api.url");
        HttpPost request = new HttpPost(url + "/lalaplat/deployFront");

        final Long projectId = project.getId();
        UserBase.Simple owner = userService.findProjectOwner(Collections.singleton(projectId)).get(projectId);

        QampDeployRecord qamp = new QampDeployRecord(user, project, tasks, env, region, owner, launchId);

        try {
            String jsonString = mapper.writeValueAsString(qamp);
            request.setEntity(new StringEntity(jsonString, ContentType.APPLICATION_JSON));
        } catch (IOException e) {
            throw new InternalMappingException(e);
        }

        CommonResponse.execute(request);
    }
}
