package cn.huolala.van.api.model.meta;

import cn.huolala.service.util.crypto.CryptoUtils;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

@Getter
@Setter
public class MiniprogramInfoModel {
    @Nullable
    @Deprecated
    private Long projectId;

    @Nullable
    private String appId;
    @Nullable
    private Type type;
    @Nullable
    private Platform platform;

    @Nullable
    private String privateKey;
    @Nullable
    private String projectPath;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private boolean encrypted;

    @Nullable
    public String getPrivateKey() {
        return encrypted ? fetchEncryptedPrivateKey() : privateKey;
    }

    @Nullable
    public String fetchEncryptedPrivateKey() {
        if (privateKey == null) return null;
        return CryptoUtils.encodeMD5(privateKey);
    }

    public void encrypt() {
        encrypted = true;
    }

    public void decrypt() {
        encrypted = false;
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum Type {
        @JsonEnumDefaultValue
        Unknown,
        Program,
        ProgramPlugin,
        Game,
        GamePlugin,
    }

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    public enum Platform {
        @JsonEnumDefaultValue
        Unknown,
        Wechat,
    }
}
