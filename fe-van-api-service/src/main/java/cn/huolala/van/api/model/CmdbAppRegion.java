package cn.huolala.van.api.model;

import cn.huolala.van.api.model.lone.LoneRegion;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Arrays;

public enum CmdbAppRegion {
    DOMESTIC("1", "cn", "国内"),
    OVERSEAS("2", "over_sea", "海外");

    public final String loneEnumStr;

    public final String description;

    public final String golangEnumStr;

    CmdbAppRegion(String loneEnumStr, String golangEnumStr, String description) {
        this.loneEnumStr = loneEnumStr;
        this.description = description;
        this.golangEnumStr = golangEnumStr;
    }

    @JsonCreator
    public static CmdbAppRegion fromString(String value) {
        return Arrays.stream(CmdbAppRegion.values())
                .filter(i -> i.loneEnumStr.equals(value) || i.golangEnumStr.equals(value))
                .findFirst().orElse(null);
    }

    @NonNull
    public static CmdbAppRegion from(@Nullable LoneRegion region) {
        return region != LoneRegion.China ? OVERSEAS : DOMESTIC;
    }
}
