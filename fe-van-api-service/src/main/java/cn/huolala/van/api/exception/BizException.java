package cn.huolala.van.api.exception;

import cn.lalaframework.exception.ServiceException;

/**
 * <AUTHOR>
 * @createTime 2020-07-30 10:03 AM
 * @description 业务异常，很可能异常当中带数据
 */
public class BizException extends ServiceException {

    private static final long serialVersionUID = 1L;

    private Object data;

    public BizException(int ret, String code, String msg, Object data) {
        this.data = data;
        this.ret = ret;
        this.code = code;
        this.msg = msg;
    }

    public BizException(BizErrorCode code) {
        this.ret = code.getRet();
        this.code = code.getCode();
        this.msg = code.getMsg();
    }

    public BizException(BizErrorCode code, String msg) {
        this.ret = code.getRet();
        this.code = code.getCode();
        this.msg = msg;
    }

    public BizException(int ret, String code, String msg) {
        this.ret = ret;
        this.code = code;
        this.msg = msg;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    @Override
    public Integer getRet() {
        return ret;
    }

    public void setRet(int ret) {
        this.ret = ret;
    }

    @Override
    public String getMsg() {
        return msg;
    }

    @Override
    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }
}