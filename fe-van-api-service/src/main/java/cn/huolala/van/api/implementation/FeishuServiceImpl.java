package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.van.api.dao.entity.FeishuSubscriptionEntity;
import cn.huolala.van.api.dao.repository.FeishuSubscriptionRepository;
import cn.huolala.van.api.exception.BizErrorCode;
import cn.huolala.van.api.exception.FeishuServiceException;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.implementation.feishu.HuolalaFeishuEnhanceServiceImpl;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.feishu.*;
import cn.huolala.van.api.model.feishu.FeishuActionValue.ConfirmToRelease;
import cn.huolala.van.api.model.feishu.FeishuActionValue.ConfirmToRollback;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.ActionElement;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.ButtonAction;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.DivElement;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.HrElement;
import cn.huolala.van.api.model.meta.FeishuNotificationConfigurationModel;
import cn.huolala.van.api.model.meta.PublishMetaInfo;
import cn.huolala.van.api.model.project.ProjectConfig;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.FeishuUtils;
import cn.huolala.van.api.util.VanUtils;
import cn.lalaframework.tools.thread.ThreadUtil;
import cn.lalaframework.tools.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.response.BaseResponse;
import com.lark.oapi.core.response.RawResponse;
import com.lark.oapi.core.token.AccessTokenType;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.bitable.v1.model.*;
import com.lark.oapi.service.contact.v3.model.GetUserReq;
import com.lark.oapi.service.contact.v3.model.GetUserRespBody;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.docx.v1.enums.RawContentDocumentLangEnum;
import com.lark.oapi.service.docx.v1.model.*;
import com.lark.oapi.service.im.v1.enums.MsgTypeEnum;
import com.lark.oapi.service.im.v1.enums.ReceiveIdTypeEnum;
import com.lark.oapi.service.im.v1.model.*;
import com.lark.oapi.service.sheets.v3.model.Sheet;
import com.lark.oapi.service.sheets.v3.model.*;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceReq;
import com.lark.oapi.service.wiki.v2.model.GetNodeSpaceRespBody;
import com.lark.oapi.service.wiki.v2.model.Node;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static cn.huolala.van.api.model.feishu.FeishuCard.Element.Field.label;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

@Service
public class FeishuServiceImpl implements FeishuService {
    private final ConcurrentHashMap<String, CacheItem<List<Object>>> localCacheForSheet = new ConcurrentHashMap<>();
    @Autowired
    private MetaService metaService;
    @Autowired
    private FeishuSubscriptionRepository feishuSubscriptionRepository;

    @Resource(name = "doc")
    private Client client;
    @Resource(name = "van")
    private Client vanClient;
    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private HuolalaFeishuEnhanceServiceImpl feishuEnhanceService;

    @Resource(name = "van-workers")
    private RedisTemplate<String, String> keyValueRedisTemplate;
    @Resource(name = "van-workers")
    private RedisTemplate<String, User> userRedisTemplate;
    @Autowired
    private UserService userService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private DeployService deployService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private HistoryService historyService;

    @NonNull
    private static String buildFeishuUserInfoCacheKey(String i) {
        return "fe-van-api-svc:feishuUserInfo:" + i;
    }

    @NonNull
    private <T> Optional<T> getDataFromResponse(BaseResponse<T> resp) {
        if (!resp.success()) {
            throw FeishuServiceException.create(resp.getCode(), resp.getMsg(), resp.getRequestId());
        }
        return Optional.ofNullable(resp.getData());
    }

    @NonNull
    private <T> Optional<T> getDataFromResponse(ThrowingSupplier<BaseResponse<T>> supplier) {
        try {
            return getDataFromResponse(supplier.get());
        } catch (FeishuServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new FeishuServiceException(e);
        }
    }

    @Nullable
    private FeishuChatInfoModel getChatInfo(@Nullable String chatId) {
        try {
            GetChatReq req = GetChatReq.newBuilder().chatId(chatId).build();
            return getDataFromResponse(client.im().chat().get(req))
                    .map(i -> new FeishuChatInfoModel(chatId, i.getName(), i.getAvatar())).orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    @NonNull
    public List<Object> getSheetWithoutCache(GetSheetRangeParams params) {
        // https://open.feishu.cn/document/server-docs/docs/sheets-v3/data-operation/reading-a-single-range
        return rawGet(
                "/open-apis/sheets/v2/spreadsheets/:spreadsheetToken/values/:range",
                params,
                FeishuSpreadsheetRespBody.class
        ).map(FeishuSpreadsheetRespBody::getValueRange)
                .map(FeishuSpreadsheetValueRange::getValues)
                .orElseGet(Collections::emptyList);
    }

    @Nullable
    private User getUserInfoByFeishuUserId(@Nullable String feishuUserId) {
        if (feishuUserId == null) return null;
        GetUserReq req = GetUserReq.newBuilder().userIdType("user_id").userId(feishuUserId).build();
        return getDataFromResponse(() -> vanClient.contact().user().get(req))
                .map(GetUserRespBody::getUser).orElse(null);
    }

    @Override
    @Nullable
    public User getUserInfo(@Nullable String uniqId) {
        if (StringUtil.isBlank(uniqId)) return null;
        return batchGetUserInfo(Collections.singletonList(uniqId)).stream().findFirst().orElse(null);
    }

    @Override
    @NonNull
    public List<User> batchGetUserInfo(@Nullable Collection<String> uniqIds) {
        if (uniqIds == null || uniqIds.isEmpty()) return Collections.emptyList();

        // Attempt to load from Redis.
        List<User> cached = uniqIds.stream()
                .map(FeishuServiceImpl::buildFeishuUserInfoCacheKey)
                .collect(Collectors.collectingAndThen(toList(), Optional::of))
                .map(userRedisTemplate.opsForValue()::multiGet)
                .orElse(Collections.emptyList()).stream()
                .filter(Objects::nonNull).collect(toList());

        // Find missing cached Set<UniqId>.
        Set<String> cachedUniqIds = cached.stream().map(User::getEnName).collect(toSet());
        Set<String> missingUniqIds = uniqIds.stream().filter(i -> !cachedUniqIds.contains(i)).collect(toSet());

        // Load missing items from remote.
        Map<String, User> loaded = feishuEnhanceService.larkUserIdInUniqNames(missingUniqIds)
                .values().parallelStream()
                .map(this::getUserInfoByFeishuUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(User::getEnName, i -> i, (a, b) -> b));

        // In background.
        ThreadUtil.execute(() -> {
            // Update cache for loaded items.
            missingUniqIds.parallelStream()
                    // If a user is not found by a uniqId, wrap an enName-only user object as the "Not Found" placeholder.
                    // This can be stored in the cache to avoid unnecessary remote queries.
                    .map(u -> loaded.containsKey(u) ? loaded.get(u) : User.newBuilder().enName(u).build())
                    .forEach(this::updateCachedFeishuUser);
            // Update cache for cached items if staled.
            updateStaledUserInfoIfNeeded(cached);
        });

        // Merge cached and loaded items map.
        return Stream.concat(
                // Filter out "Not Found" placeholders.
                cached.stream().filter(i -> i.getUserId() != null),
                loaded.values().stream()
        ).collect(toList());
    }

    private void updateCachedFeishuUser(@Nullable User user) {
        if (user == null) return;
        String key = buildFeishuUserInfoCacheKey(user.getEnName());
        Duration expires = Duration.ofDays(30);
        userRedisTemplate.opsForValue().set(key, user, expires);
        long now = System.currentTimeMillis();
        keyValueRedisTemplate.opsForValue().set(key + ":lastModified", String.valueOf(now), expires);
    }

    private void updateStaledUserInfoIfNeeded(@NonNull List<User> cached) {
        // Get lastModified for cached items from Redis.
        List<Instant> listOfLastModified = cached.stream()
                .map(i -> buildFeishuUserInfoCacheKey(i.getEnName()) + ":lastModified")
                .collect(Collectors.collectingAndThen(toList(), Optional::of))
                .map(keyValueRedisTemplate.opsForValue()::multiGet)
                .orElseGet(() -> new ArrayList<>(cached.size()))
                .stream().map(i -> i == null ? null : Instant.ofEpochMilli(Long.parseLong(i)))
                .collect(toList());

        // Filter staled items, retrieve from remote, and update cache.
        final Instant stales = Instant.now().minus(24, ChronoUnit.HOURS);
        IntStream.range(0, cached.size()).parallel()
                .mapToObj(i -> Pair.of(cached.get(i), listOfLastModified.get(i)))
                .filter(i -> i.getValue() == null || i.getValue().isBefore(stales))
                .map(i -> getUserInfoByFeishuUserId(i.getKey().getUserId()))
                .forEach(this::updateCachedFeishuUser);
    }

    @NonNull
    @Override
    public List<FeishuSubscriptionModel> findSubscriptions(long projectId) {
        List<FeishuSubscriptionEntity> entities = feishuSubscriptionRepository.findByProjectId(projectId);
        Map<String, FeishuChatInfoModel> chatMap = entities.stream().parallel()
                .map(FeishuSubscriptionEntity::getChatId).map(this::getChatInfo).filter(Objects::nonNull)
                .collect(Collectors.toMap(FeishuChatInfoModel::getChatId, Function.identity()));
        return userService.buildListWithUserModel(entities, FeishuSubscriptionEntity::getUserId,
                (e, u) -> FeishuSubscriptionModel.from(e, chatMap.get(e.getChatId()), u));
    }

    @Override
    @NonNull
    public FeishuNotificationConfigurationModel getVanFeishuNotificationConfiguration(Long projectId) {
        FeishuNotificationConfigurationModel model = metaService.getValue(
                MetaType.VanFeishuNotificationFilterConfiguration, projectId,
                FeishuNotificationConfigurationModel.class);
        if (model != null) return model;
        return new FeishuNotificationConfigurationModel();
    }

    @Override
    public void updateVanFeishuNotificationConfiguration(Long projectId, FeishuNotificationConfigurationModel model) {
        metaService.setValue(
                MetaType.VanFeishuNotificationFilterConfiguration,
                projectId, FeishuNotificationConfigurationModel.class, model);
    }

    public void sendVanCardByUser(String content, UserModel user) {
        CreateMessageReq req = CreateMessageReq.newBuilder()
                .receiveIdType(ReceiveIdTypeEnum.EMAIL.getValue())
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .receiveId(user.getUniqId() + "@huolala.cn")
                        .msgType(MsgTypeEnum.MSG_TYPE_INTERACTIVE.getValue())
                        .content(content)
                        .build())
                .build();

        CreateMessageResp resp;
        try {
            resp = vanClient.im().message().create(req, RequestOptions.newBuilder().build());
        } catch (Exception e) {
            throw new InternalRequestException(e);
        }
        if (resp.getCode() != 0) {
            throw BizErrorCode.CALL_API_ERR
                    .getException(String.format("code:%d,msg:%s,err:%s", resp.getCode(), resp.getMsg(),
                            Jsons.DEFAULT.toJson(resp.getError())));
        }
    }

    @Override
    public void notifyForPrdEnvLaunch(@NonNull ProjectModel project,
                                      @NonNull List<BuildTaskModel> tasks,
                                      @NonNull CanaryRecord head) {
        Region region = head.getRegion();

        // Check project notification config.
        boolean enabled = Optional.ofNullable(project.getConfig().getMessageConfig())
                .map(ProjectConfig.MessageConfig::getLaunchPrdEvent)
                .map(ProjectConfig.MessageConfig.Event::getEnable)
                .map(Boolean.TRUE::equals).orElse(false);
        if (!enabled) return;

        VanProjectConfig config = projectService.getVanProjectConfig(Region.cn, project.getId());
        boolean international = config.isInternational();

        List<FeishuSubscriptionEntity> subs = feishuSubscriptionRepository.findByProjectId(project.getId());
        if (subs.isEmpty()) return;

        Optional<PublishMetaInfo> pmi = deployService.getPublishMetaInfo(head.getId());

        Map<Long, String> tagMap = buildTaskService
                .findTag(project.getId(), tasks.stream().map(BuildTaskModel::getId).collect(toSet()));

        boolean canRollback = historyService.getPreviousId(head.getId()).isPresent();

        subs.stream().parallel().map(FeishuSubscriptionEntity::getChatId).forEach(chatId -> {
            final FeishuCard card = FeishuUtils.buildNotificationCard("生产发布", project.getName());

            DivElement el = new DivElement().appendTo(card);

            FeishuUtils.buildOperatorFields(head.getUserUniqId(), head.getCreatedAt()).forEach(el::addField);

            if (international) label("区域", region.name()).appendTo(el);
            pmi.map(o -> FeishuUtils.buildPmiLinkField(region, international, o)).ifPresent(el::addField);

            tasks.stream().flatMap(task -> Stream.concat(
                    Stream.of(new FeishuCard.Element.Field("")),
                    FeishuUtils.buildTaskFields(project, task, tagMap.get(task.getId())).stream()
            )).forEach(el::addField);

            new HrElement().appendTo(card);

            ActionElement actions = new ActionElement().appendTo(card);
            new ButtonAction(FeishuActionValue.ButtonType.DEFAULT, "查看监控")
                    .url(VanUtils.buildMonitorUrl(project))
                    .appendTo(actions);
            if (canRollback) {
                new ConfirmToRollback(project.getId(), head.getId()).chatId(chatId).sign()
                        .wrapAsButton("回滚发布").appendTo(actions);
            }

            sendCard(card, chatId);
        });
    }

    @Override
    public void notifyForTestEnvLaunch(@NonNull ProjectModel project,
                                       @NonNull BuildTaskModel task,
                                       @NonNull DeployRecord head) {
        ConfigEnv env = head.getEnv();
        if (env == null) return;

        // Check project notification config.
        boolean enabled = Optional.ofNullable(project.getConfig().getMessageConfig())
                .map(ProjectConfig.MessageConfig::getLaunchTestEvent)
                .map(ProjectConfig.MessageConfig.Event::getEnable)
                .map(Boolean.TRUE::equals).orElse(false);
        if (!enabled) return;

        long projectId = project.getId();
        long taskId = task.getId();

        // Check feishu config settings.
        FeishuNotificationConfigurationModel config = getVanFeishuNotificationConfiguration(projectId);
        if (!config.match(task.getBranch(), env)) return;

        List<FeishuSubscriptionEntity> subs = feishuSubscriptionRepository.findByProjectId(projectId);
        if (subs.isEmpty()) return;

        String tag = buildTaskService.findTag(projectId, taskId);

        subs.stream().parallel().map(FeishuSubscriptionEntity::getChatId).forEach(chatId -> {
            FeishuCard card = FeishuUtils.buildNotificationCard("测试环境发布", project.getName());

            new DivElement()
                    .addFields(FeishuUtils.buildOperatorFields(head.getUserUniqId(), head.getUpdatedAt()))
                    .addField(label("发布环境", "[%s](%s)", env, VanUtils.getProjectEnvLink(project, env)))
                    .addFields(FeishuUtils.buildTaskFields(project, task, tag))
                    .appendTo(card);

            if (env.getName() == Env.pre && task.getBranch().matches("release\\b.*")) {
                ActionElement actions = new ActionElement().appendTo(card);
                new ConfirmToRelease(projectId, taskId).chatId(chatId).sign()
                        .wrapAsButton("发布到生产环境").appendTo(actions);
            }

            sendCard(card, chatId);
        });
    }

    /**
     * @return The message ID of feishu card.
     */
    @Override
    public String sendCard(@NonNull FeishuCard card, @Nullable String chatId) {
        Objects.requireNonNull(chatId, "The chatId must not be null here");
        return sendCard(card, ReceiveIdTypeEnum.CHAT_ID, chatId);
    }

    /**
     * @return The message ID of feishu card.
     */
    @Override
    public String sendCard(@NonNull FeishuCard card, @NonNull UserBase vanUser) {
        User user = getUserInfo(vanUser.getUserUniqId());
        Objects.requireNonNull(user, "The user must not be null here");
        return sendCard(card, ReceiveIdTypeEnum.USER_ID, user.getUserId());
    }

    /**
     * @return The message ID of feishu card.
     */
    @Nullable
    private String sendCard(@NonNull FeishuCard card,
                            @NonNull ReceiveIdTypeEnum receiveIdTypeEnum,
                            @NonNull String receiveId) {
        String content;
        try {
            content = mapper.writeValueAsString(card);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }

        CreateMessageReq req = CreateMessageReq.newBuilder()
                .receiveIdType(receiveIdTypeEnum.getValue())
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .receiveId(receiveId)
                        .msgType(MsgTypeEnum.MSG_TYPE_INTERACTIVE.getValue())
                        .content(content)
                        .build())
                .build();

        return getDataFromResponse(() -> vanClient.im().message().create(req, RequestOptions.newBuilder().build()))
                .map(CreateMessageRespBody::getMessageId).orElse(null);
    }

    public void updateCard(@NonNull FeishuCard card, @Nullable String messageId) {
        Objects.requireNonNull(messageId, "The messageId must not be null here");
        String content;
        try {
            content = mapper.writeValueAsString(card);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }

        PatchMessageReq req = PatchMessageReq.newBuilder()
                .messageId(messageId)
                .patchMessageReqBody(PatchMessageReqBody.newBuilder()
                        .content(content)
                        .build())
                .build();

        getDataFromResponse(() -> vanClient.im().message().patch(req, RequestOptions.newBuilder().build()));
    }

    @Override
    public void sendTextToUser(@NonNull String text, @NonNull User user) {
        String json;
        try {
            json = mapper.writeValueAsString(Collections.singletonMap("text", text));
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
        CreateMessageReq req = CreateMessageReq.newBuilder()
                .receiveIdType(ReceiveIdTypeEnum.USER_ID.getValue())
                .createMessageReqBody(CreateMessageReqBody.newBuilder()
                        .receiveId(user.getUserId())
                        .msgType(MsgTypeEnum.MSG_TYPE_TEXT.getValue())
                        .content(json)
                        .build())
                .build();
        getDataFromResponse(() -> vanClient.im().message().create(req, null));
    }

    @NonNull
    @SuppressWarnings("SameParameterValue")
    private <T> Optional<T> rawGet(String path, Object params, Class<T> clz) {
        return getDataFromResponse(() -> {
            RawResponse rawRes = vanClient.get(
                    path,
                    params,
                    AccessTokenType.Tenant);
            return mapper.readValue(
                    rawRes.getBody(),
                    mapper.getTypeFactory().constructParametricType(BaseResponse.class, clz)
            );
        });
    }

    @NonNull
    @Override
    public List<Object> getSheetWithCache(GetSheetRangeParams params, int maxAge) {
        return localCacheForSheet.compute("fe-van-api-svc:getSheet:" + params, (key, current) -> {
            if (current != null && current.notExpired()) return current;
            long expire = System.currentTimeMillis() + maxAge * 1000L;
            List<Object> data = getSheetWithoutCache(params);
            return new CacheItem<>(expire, data);
        }).getData();
    }

    @Override
    @NonNull
    public List<Object> getSheet(String spreadsheetToken, String range, String valueRenderOption) {
        GetSheetRangeParams params = new GetSheetRangeParams(spreadsheetToken, range, valueRenderOption);
        return getSheetWithCache(params, 60);
    }

    @Override
    @NonNull
    public Sheet[] getSheets(String spreadsheetToken) {
        QuerySpreadsheetSheetReq req = QuerySpreadsheetSheetReq.newBuilder().spreadsheetToken(spreadsheetToken).build();
        return getDataFromResponse(() -> vanClient.sheets().spreadsheetSheet().query(req))
                .map(QuerySpreadsheetSheetRespBody::getSheets).orElseGet(() -> new Sheet[]{});
    }

    @Override
    @Nullable
    public Node getWikiNode(String token) {
        GetNodeSpaceReq req = GetNodeSpaceReq.newBuilder().token(token).build();
        return getDataFromResponse(() -> vanClient.wiki().space().getNode(req))
                .map(GetNodeSpaceRespBody::getNode).orElse(null);
    }

    @Override
    @Nullable
    public GetSpreadsheet getSheetMeta(String spreadsheetToken) {
        GetSpreadsheetReq req = GetSpreadsheetReq.newBuilder().spreadsheetToken(spreadsheetToken).build();
        return getDataFromResponse(() -> vanClient.sheets().spreadsheet().get(req))
                .map(GetSpreadsheetRespBody::getSpreadsheet).orElse(null);
    }

    @Override
    @Nullable
    public Sheet getSheetMeta(String spreadsheetToken, String sheetId) {
        GetSpreadsheetSheetReq req = GetSpreadsheetSheetReq.newBuilder().spreadsheetToken(spreadsheetToken).sheetId(sheetId).build();
        return getDataFromResponse(() -> vanClient.sheets().spreadsheetSheet().get(req))
                .map(GetSpreadsheetSheetRespBody::getSheet).orElse(null);
    }

    @Override
    public ListDocumentBlockRespBody getDocumentBlocks(String documentId) {
        ListDocumentBlockReq req = ListDocumentBlockReq.newBuilder().documentId(documentId).build();
        return getDataFromResponse(() -> vanClient.docx().documentBlock().list(req)).orElse(null);
    }

    @Override
    @Nullable
    public RawContentDocumentRespBody getDocumentRawContent(String documentId, RawContentDocumentLangEnum lang) {
        RawContentDocumentReq.Builder rb = RawContentDocumentReq.newBuilder().documentId(documentId);
        if (lang != null) rb.lang(lang);
        return getDataFromResponse(() -> vanClient.docx().document().rawContent(rb.build())).orElse(null);
    }

    @Override
    @NonNull
    public Optional<ListAppTableRecordRespBody> getBitableRecords(String appToken, String tableId, String viewId) {
        ListAppTableRecordReq req = ListAppTableRecordReq.newBuilder()
                .appToken(appToken)
                .tableId(tableId)
                .viewId(viewId)
                .build();
        return getDataFromResponse(() -> vanClient.bitable().appTableRecord().list(req));
    }

    @Override
    @Nullable
    public Document getDocumentMeta(String documentId) {
        GetDocumentReq req = GetDocumentReq.newBuilder().documentId(documentId).build();
        return getDataFromResponse(() -> vanClient.docx().document().get(req))
                .map(GetDocumentRespBody::getDocument).orElse(null);
    }

    @Override
    @NonNull
    public Optional<DisplayApp> getBitableApp(String appToken) {
        GetAppReq req = GetAppReq.newBuilder().appToken(appToken).build();
        return getDataFromResponse(() -> vanClient.bitable().app().get(req))
                .map(GetAppRespBody::getApp);
    }

    @Override
    @NonNull
    public Optional<ListAppTableViewRespBody> getBitableTableViews(String appToken, String tableId) {
        ListAppTableViewReq req = ListAppTableViewReq.newBuilder().appToken(appToken).tableId(tableId).build();
        return getDataFromResponse(() -> vanClient.bitable().appTableView().list(req));
    }

    @Override
    @NonNull
    public Optional<ListAppTableRespBody> getBitableTables(String appToken) {
        ListAppTableReq req = ListAppTableReq.newBuilder().appToken(appToken).build();
        return getDataFromResponse(() -> vanClient.bitable().appTable().list(req));
    }

    @Override
    @NonNull
    public Optional<ListAppDashboardRespBody> getBitableDashboard(String appToken) {
        ListAppDashboardReq req = ListAppDashboardReq.newBuilder().appToken(appToken).build();
        return getDataFromResponse(() -> vanClient.bitable().appDashboard().list(req));
    }

    @Override
    @NonNull
    public Optional<CreateImageRespBody> uploadImage(File file) {
        CreateImageReq req = CreateImageReq.newBuilder()
                .createImageReqBody(CreateImageReqBody.newBuilder().imageType("message").image(file).build())
                .build();
        return getDataFromResponse(() -> vanClient.im().image().create(req));
    }

    @Override
    public void sendCardToUser(FeishuCard card, String userUniqId) {
        User user = getUserInfo(userUniqId);
        Objects.requireNonNull(user, "The user must not be null here");
        sendCard(card, ReceiveIdTypeEnum.USER_ID, user.getUserId());
    }

    @FunctionalInterface
    private interface ThrowingSupplier<T> {
        @SuppressWarnings("java:S112")
        T get() throws Exception;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CacheItem<T> {
        private Long expire;
        private T data;

        public boolean notExpired() {
            return System.currentTimeMillis() < getExpire();
        }
    }
}
