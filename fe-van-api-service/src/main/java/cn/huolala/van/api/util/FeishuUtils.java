package cn.huolala.van.api.util;

import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.exception.BizErrorCode;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.MiniprogramDeployHistoryRecord;
import cn.huolala.van.api.model.feishu.FeishuActionValue.ButtonType;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.model.feishu.FeishuCard.Element;
import cn.huolala.van.api.model.feishu.FeishuCard.Element.*;
import cn.huolala.van.api.model.feishu.FeishuCard.TagContent;
import cn.huolala.van.api.model.feishu.FeishuCard.Template;
import cn.huolala.van.api.model.meta.PublishMetaInfo;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.service.BuildTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toMap;

public class FeishuUtils {
    public static final DateTimeFormatter CARD_DATE_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss O");

    private FeishuUtils() {
    }

    public static String sign(Map<String, String> values, String secret) {
        List<String> keys = new ArrayList<>(values.keySet());
        keys.removeIf(key -> key.equals("sign") || key.equals("debug"));
        Collections.sort(keys);

        String data = "";
        for (String key : keys) {
            data += values.get(key);
        }
        data += secret;

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] md5hash = md.digest(data.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : md5hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw BizErrorCode.SYS_FATAL_ERR.getException("加密参数错误" + e.getMessage());
        }
    }

    @NonNull
    public static Field buildPmiLinkField(Region region, boolean international, PublishMetaInfo o) {
        String path = international && Region.cn != region ? "llm-detail" : "detail-edit";
        return Field.label("关联发布计划", "[%s](https://pmis.huolala.cn/release/%s?releaseId=%s)", o.getMeta(), path,
            o.getPlanId());
    }

    @NonNull
    public static Field buildTaskLinkField(@NonNull BuildTaskModel task) {
        return new Field(String.format("**构建：**%s", buildTaskMdLink(task.getProjectId(), task.getId())));
    }

    @NonNull
    public static Field buildCommitField(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        String base = buildGitlabRepoUrl(project);
        String branch = task.getBranch();
        String branchMdLink = String.format("[%s](%s/tree/%s)", branch, base, branch);
        String hash = task.getHash();
        String commitMdLink = String.format("[%s](%s/commit/%s)", StringUtils.left(hash, 8), base, hash);
        String commitMessage = task.getCommitMessage().replaceAll("\\n+", "\n");
        return Field.label("Commit", "%s(%s) %s", branchMdLink, commitMdLink, commitMessage);
    }

    @NonNull
    public static String buildGitlabRepoUrl(@NonNull ProjectModel project) {
        return "https://gitlab.huolala.cn/" + project.getRepository();
    }

    @NonNull
    public static List<Field> buildTaskFields(@NonNull ProjectModel project,
                                              @NonNull BuildTaskModel task,
                                              @Nullable String tag) {
        List<Field> list = new ArrayList<>();
        list.add(Field.label("构建版本", buildTaskMdLink(task.getProjectId(), task.getId())).shorten());
        list.add(tag != null && !tag.isEmpty() ? Field.label("Tag", tag).shorten() : new Field().shorten());

        list.add(Field.label("构建触发", task.extractSimple().toUserString()).shorten());
        list.add(Field.label("构建时间", task.getUpdatedAt().format(CARD_DATE_TIME)).shorten());

        list.add(FeishuUtils.buildCommitField(project, task));

        return list;
    }

    @NonNull
    public static String buildTaskMdLink(long projectId, long taskId) {
        return String.format("[#%s](%s)", taskId, VanUtils.buildTaskLink(projectId, taskId));
    }

    public static List<Field> buildCanaryRecordFields(@NonNull CanaryRecord current,
                                                      @NonNull ProjectModel project,
                                                      @NonNull Map<Long, BuildTaskModel> taskMap,
                                                      @NonNull Map<Long, String> tagMap) {
        List<Field> list = new ArrayList<>();

        String at = "<at email=" + current.getUserUniqId() + "@huolala.cn></at>";
        list.add(Field.label("发布人", at).shorten());
        list.add(Field.label("发布时间", current.getCreatedAt().format(CARD_DATE_TIME)).shorten());
        list.add(Field.label("发布区域", current.getRegion().name()));

        Optional.of(current.getMessage()).filter(StringUtils::isNotBlank).map(msg -> Field.label("发布备注", msg))
            .ifPresent(list::add);

        current.collectTaskIds().stream().flatMap(id -> Stream.concat(Stream.of(new Field("")),
            buildTaskFields(project, taskMap.get(id), tagMap.get(id)).stream())).forEach(list::add);

        return list;
    }

    @NonNull
    public static List<Field> buildOperatorFields(@NonNull String userUniqId, @NonNull OffsetDateTime time) {
        return Arrays.asList(Field.label("操作人", "<at email=" + userUniqId + "@huolala.cn></at>").shorten(),
            Field.label("操作时间", time.format(CARD_DATE_TIME)).shorten());
    }

    @NonNull
    public static FeishuCard buildNotificationCard(@NonNull String eventName, @NonNull String projectName) {
        String title = String.format("【通知】%s - %s", eventName, projectName);
        return new FeishuCard(title, FeishuCard.Template.BLUE, true);
    }

    @NonNull
    public static FeishuCard buildConfirmCard(String eventName, String projectName) {
        String title = String.format("【确认】%s - %s", eventName, projectName);
        return new FeishuCard(title, FeishuCard.Template.YELLOW, true);
    }

    @NonNull
    public static FeishuCard buildMiniprogramLarkPushCard(@NonNull ProjectModel project,
                                                          @NonNull BuildTaskModel task,
                                                          @NonNull MiniprogramDeployHistoryRecord miniprogramDeployRecord,
                                                          @NonNull String status,
                                                          String imageKey,
                                                          String devPluginId) {
        boolean isOk = status.equals("ok");
        String action = task.getType() == BuildTaskType.MiniprogramDeploy ? "预览" : "上传";
        String result = isOk ? "成功🎉🎉🎉" : "失败";
        Template tpl = isOk ? Template.TURQUOISE : Template.RED;
        String title = String.format("🤖 小程序【%s】%s%s", project.getName(), action, result);
        FeishuCard card = new FeishuCard(title, tpl, true);

        List<Element<?>> list = new ArrayList<>();
        if (task.getType() == BuildTaskType.MiniprogramDeploy && !imageKey.isEmpty()) {
            // image
            Element<?> img = new Element<>("img");
            img.setImgKey(imageKey);
            TagContent alt = TagContent.txt(String.format("%s-%d-qrcode", project.getName(), task.getId()));
            img.setAlt(alt);
            list.add(img);
            // add hr if image exists
            list.add(new HrElement());
        }
        DivElement div = new DivElement();
        // 如果成功，task 为构建task，跳转可查看qrcode
        // 失败则跳转去发布操作task 详情，以查看失败日志
        long bindTaskId = isOk ? miniprogramDeployRecord.getTaskId() : task.getId();

        String taskLink = VanUtils.buildMiniprogramTaskLink(project.getId(), bindTaskId);
        div.addField(Field.label("Task",
            String.format("[%d](%s)", bindTaskId, String.format("%s&tm_source=lark-card", taskLink))));
        div.addField(Field.label("Bot", miniprogramDeployRecord.getRobot().toString()));
        if (task.getType() == BuildTaskType.MiniprogramUpload) {
            div.addField(Field.label("Version", miniprogramDeployRecord.getVersion()));

            div.addField(Field.label("Description", miniprogramDeployRecord.getDescription()));
        }
        if (!devPluginId.isEmpty()) {
            // 如果是小程序插件，推送开发版id
            div.addField(Field.label("DevPluginId", devPluginId));
        }
        list.add(div);
        card.setElements(list);
        return card;
    }

    @NonNull
    public static FeishuCard buildCreateProjectCard(@NonNull String projectName, @NonNull ProjectType projectType) {
        String title = String.format("项目 %s 创建成功", projectName);
        FeishuCard card = new FeishuCard(title, Template.GREEN, true);

        String notifyContent = String.format(
            "- 使用 [SSO](https://huolala.feishu.cn/wiki/wikcnZVECG6jaaJGJt7G14NMoEe) 进行用户身份认证\n" + "- 通过[水印](https://huolala.feishu.cn/wiki/wikcnfKq6aSrLhwbqfWBuIYX4jf)加强数据安全保护\n" + "- 在 [monitor](https://monitor-v.huolala.cn/infrastructure/van/overall?project=%s) 上查看你的项目的访问情况\n" + "- 通过 [PageSpy](https://page-spy-web-v.huolala.work) 进行远程调试项目\n" + "- 开启 vConsole 进行移动端的调试\n",
            projectName);

        List<Element<?>> list = new ArrayList<>();
        DivElement div = new DivElement();
        String startLink = projectType == ProjectType.Miniprogram ? "https://huolala.feishu.cn/wiki/wikcntNIQpg1rjN9EMgzJMsDubl" : "https://huolala.feishu.cn/wiki/wikcnIZlDK3NP7AipjIUOwPC3Zh";
        div.addField(new Field(String.format("查看[快速上手](%s)，进行构建发布", startLink))).addField(new Field(""))
            .addField(new Field("通过 Van 发布的项目，你可以")).addField(new Field(notifyContent));
        list.add(div);
        list.add(new NoteElement().addNote(Note.txt("如有疑问，可以搜索前端基础技术支持群寻求帮助")));

        card.setElements(list);
        return card;
    }

    @NonNull
    public static FeishuCard buildWindowApprovalCard(@NonNull String code,
                                                     @NonNull String projectName,
                                                     @NonNull LocalDateTime start,
                                                     @NonNull LocalDateTime end) {
        FeishuCard card = new FeishuCard("Van 发布审批创建通知", Template.BLUE, true);
        List<Element<?>> list = new ArrayList<>();
        DivElement div = new DivElement();
        div.addField(new Field("")).addField(Field.label("Van 项目", projectName)).addField(new Field(""))
            .addField(Field.label("开始时间", start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))))
            .addField(new Field(""))
            .addField(Field.label("结束时间", end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))));

        list.add(div);
        list.add(new HrElement());
        String noteTxt = String.format(
            "你创建了 Van 项目 %s 的审批工单，快去找你的上级审批吧，审批流程可以在飞书工作台-审批中查看", projectName);
        list.add(new NoteElement().addNote(Note.txt(noteTxt)));
        String actionUrl = "https://applink.feishu.cn/client/mini_program/open?mode=sidebar-semi&appId=cli_9cb844403dbb9108&path=pages%2Fdetail%2Findex%3FinstanceId%3D" + code;
        ButtonAction buttonAction = new ButtonAction(ButtonType.PRIMARY, "查看审批");
        buttonAction.setUrl(actionUrl);
        list.add(new ActionElement().addAction(buttonAction));
        card.setElements(list);
        return card;
    }

    @NonNull
    public static FeishuCard buildDomainApprovalCard(@NonNull String code, @NonNull String projectName) {
        FeishuCard card = new FeishuCard("Van 生产入口审批创建通知", Template.BLUE, true);
        List<Element<?>> list = new ArrayList<>();
        DivElement div = new DivElement();
        div.addField(new Field("")).addField(Field.label("Van 项目", projectName));
        list.add(div);
        list.add(new HrElement());
        String noteTxt = String.format(
            "你创建了 Van 项目 %s 的审批工单，快去找你的上级审批吧，审批流程可以在飞书工作台-审批中查看", projectName);
        list.add(new NoteElement().addNote(Note.txt(noteTxt)));
        String actionUrl = "https://applink.feishu.cn/client/mini_program/open?mode=sidebar-semi&appId=cli_9cb844403dbb9108&path=pages%2Fdetail%2Findex%3FinstanceId%3D" + code;
        ButtonAction buttonAction = new ButtonAction(ButtonType.PRIMARY, "查看审批");
        buttonAction.setUrl(actionUrl);
        list.add(new ActionElement().addAction(buttonAction));
        card.setElements(list);
        return card;
    }

    @NonNull
    public static FeishuCard buildHookExceptionCard(String projectName, long taskId, String hookName, String message) {
        FeishuCard card = new FeishuCard("Van Hook 异常通知", Template.BLUE, true);
        List<Element<?>> list = new ArrayList<>();
        DivElement div = new DivElement();
        div.addField(new Field("")).addField(Field.label("Van Hook", hookName));
        div.addField(new Field("")).addField(Field.label("Van Project", projectName));
        div.addField(new Field("")).addField(Field.label("Van Task", String.valueOf(taskId)));
        list.add(div);
        list.add(new HrElement());
        list.add(new NoteElement().addNote(Note.txt(message)));
        card.setElements(list);

        return card;
    }

    @NonNull
    public static FeishuCard buildFeishuReleaseConfirmCard(@NonNull ProjectModel project,
                                                           @Nullable CanaryRecord canaryRecord,
                                                           @Nullable Long srcTaskId,
                                                           @NonNull Long dstTaskId,
                                                           @NonNull BuildTaskService buildTaskService1) {
        Set<Long> taskIds = Stream.of(srcTaskId, dstTaskId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, BuildTaskModel> taskMap = buildTaskService1.batchGet(project.getId(), taskIds)
            .collect(toMap(BuildTaskModel::getId, Function.identity()));
        Map<Long, String> tagMap = buildTaskService1.findTag(project.getId(), taskIds);

        FeishuCard card = buildConfirmCard("发布到生产环境", project.getName());

        if (srcTaskId != null) {
            BuildTaskModel srcTask = taskMap.get(srcTaskId);
            if (srcTask == null) throw ResourceNotFoundException.create(BuildTaskModel.NAME, "id", srcTaskId);
            DivElement el = new DivElement().appendTo(card);
            Field.label("当前线上版本", null).appendTo(el);
            if (canaryRecord != null) el.addFields(buildCanaryRecordFields(canaryRecord, project, taskMap, tagMap));
            new HrElement().appendTo(card);
        }

        BuildTaskModel dstTask = taskMap.get(dstTaskId);
        if (dstTask == null) throw ResourceNotFoundException.create(BuildTaskModel.NAME, "id", dstTaskId);

        new DivElement().addField(Field.label("本次操作要发布的版本", null))
            .addFields(buildTaskFields(project, dstTask, tagMap.get(dstTaskId))).appendTo(card);

        new HrElement().appendTo(card);
        return card;
    }
}
