package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.BuildTaskStatus;
import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.MetaType;
import cn.lalaframework.storage.adapter.Storage;
import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.CanaryRule;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import cn.huolala.van.api.model.events.ProjectEventContent.LaunchProd;
import cn.huolala.van.api.model.events.ProjectEventContent.LaunchTest;
import cn.huolala.van.api.model.hooks.HookParamsForDev;
import cn.huolala.van.api.model.hooks.HookParamsForPrd;
import cn.huolala.van.api.model.meta.DeployHookModel;
import cn.huolala.van.api.model.meta.DeployHookModel.Timing;
import cn.huolala.van.api.model.meta.PreviousCanary;
import cn.huolala.van.api.model.meta.PublishMetaInfo;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.tasks.TaskFlagName;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.StorageHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Collections.singleton;

@Service
public class DeployServiceImpl implements DeployService {
    @Autowired
    private ProjectService projectService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private EventService eventService;
    @Autowired
    private MetaService metaService;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private PmisService pmisService;
    @Autowired
    private QampService qampService;
    @Autowired
    private FeishuService feishuService;
    @Autowired
    private PlatformTransactionManager transactionManager;

    private static void updateCanaryFile(@NonNull ProjectModel project,
                                         @NonNull Region region,
                                         @NonNull LegacyDeployConfig config) {
        region.getStorage().putValue(region.fetchLegacyCanaryPath(project.getName()), config);
    }

    private static void updateCanaryFile(@NonNull ProjectModel project,
                                         @NonNull Region region,
                                         @NonNull List<CanaryRule> config) {
        region.getStorage().putValue(region.fetchCanaryPath(project.getName()), config);
    }

    private static void updateDevVersion(@NonNull ProjectModel project,
                                         @NonNull BuildTaskModel task,
                                         @NonNull ConfigEnv env) {
        Region region = Region.defaultRegion();
        region.getStorage()
            .putValue(String.format("%s/%s.json", project.getName(), env), new LegacyDeployConfig(task.getId(), null));
        StorageHelper.touchLastModifiedFlag(region.getStorage(), env.getName());
    }

    @NonNull
    @Override
    public DeployRecord deployForDev(@NonNull DeployParams params) {
        UserModel user = params.getUser();
        LegacyDeployConfig config = params.getConfig();
        ConfigEnv env = params.getEnv();
        Long taskId = config.getDefaultTaskId();
        if (taskId == null) throw new InternalException("The default task id must not be null");
        if (config.getCanary() != null) {
            throw new InternalException("The DEV environment does not support the canary rules");
        }
        ProjectModel project = params.getProject();
        BuildTaskModel task = buildTaskService.getNeverNull(project.getId(), taskId);

        // Assert task status
        if (task.getStatus() != BuildTaskStatus.Done) {
            throw new VanBadRequestException("The task status is not Done");
        }

        // Assert purged flag
        if (StorageHelper.hasFlag(Region.defaultStorage(), project.getName(), task.getId(), TaskFlagName.Purged)) {
            throw new VanBadRequestException("The task has been purged");
        }

        // Assert task metas
        buildTaskService.assertTaskMetas(task);

        DeployHookModel hook = projectService.getDeployHookConfig(project.getId());
        HookParamsForDev hookParams = new HookParamsForDev(project, user, env, task);

        // Attempt to call the "before" deploy hooks
        hook.execute(Timing.before, hookParams);

        DeployRecord dr = runInTransaction(() -> {
            // Write the deployment database record and the storage file in one transaction.
            // NOTE: If the storage is written timeout but actually succeeds, the database record will be rolled back.
            // TODO: This case causes dirty data. Needs a solution to asynchronously compare and fix the data.
            historyService.insertOrUpdateDeployLog(user, task, env);
            updateDevVersion(project, task, env);
            return historyService.getDevHead(task, env);
        });

        // Now, deploying success.

        Stream.<Runnable>of(
            // Asynchronously add a project event, this action broadcasts this event to all listeners.
            // NOTE: This action will fail. But anyway, it's not important.
            () -> eventService.add(user, project, new LaunchTest(dr.getId(), task.getId())),

            // Attempt to call the "after" deploy hooks
            // NOTE: The "after" hook is a notification, run it asynchronous ignore result.
            () -> hook.execute(Timing.after, hookParams),

            () -> qampService.notify(user, project, singleton(task), env.getName(), Region.cn, dr.getId()),
            () -> feishuService.notifyForTestEnvLaunch(project, task, dr)).forEach(CompletableFuture::runAsync);

        return dr;
    }

    /**
     * @param params    Basic deployment parameters.
     * @param backPoint Set a back point to indicate which version will be used when rolling back.
     *                  Normally, this value is current head version on same region.
     *                  In the scene of rolling back, this value is manually specified by the caller.
     * @param headPoint An optimistic lock value, that is used to assert the current head version matched.
     */
    @NonNull
    @Override
    public CanaryRecord releaseForProd(@NonNull DeployParams params,
                                       @Nullable Long backPoint,
                                       @Nullable Long headPoint) {
        ProjectModel project = params.getProject();
        LegacyDeployConfig config = params.getConfig();
        UserModel user = params.getUser();
        Region region = params.getRegion();

        List<BuildTaskModel> tasks = getAndCheckTasks(project, config, region);

        DeployHookModel hook = projectService.getDeployHookConfig(project.getId());
        HookParamsForPrd hookParams = new HookParamsForPrd(project, user, region, tasks);

        // Attempt to call the "before" deploy hooks
        hook.execute(Timing.before, hookParams);

        // Set release flag for each task.
        // NOTE: A task resource can be accessed from the internet only if the guard file exists.
        final Storage storage = region.getStorage();
        tasks.stream().parallel()
            .forEach(task -> StorageHelper.setFlag(storage, project.getName(), task.getId(), TaskFlagName.Released));

        String message = params.getMessage();
        PdmInfo pdmInfo = params.getPdmInfo();

        CanaryRecord after = runInTransaction(() -> releaseForProdTransaction(params, backPoint, headPoint));


        // Now, deploying success.

        Stream.<Runnable>of(
            // Asynchronously add a project event, this action broadcasts this event to all listeners.
            // NOTE: This action will fail. But anyway, it's not important.
            () -> eventService.add(user, project, new LaunchProd(after, pdmInfo, message)),

            // Attempt to call the "after" deploy hooks
            // NOTE: The "after" hook is a notification, run it asynchronous ignore result.
            () -> hook.execute(Timing.after, hookParams),

            () -> pmisService.notify(user, project, tasks, after.getId()),
            () -> qampService.notify(user, project, tasks, Env.prd, region, after.getId()),
            () -> feishuService.notifyForPrdEnvLaunch(project, tasks, after)).forEach(CompletableFuture::runAsync);

        return after;
    }

    /**
     * @param params    Basic deployment parameters.
     * @param backPoint Set a back point to indicate which version will be used when rolling back.
     *                  Normally, this value is current head version on same region.
     *                  In the scene of rolling back, this value is manually specified by the caller.
     * @param headPoint An optimistic lock value, that is used to assert the current head version matched.
     */
    private CanaryRecord releaseForProdTransaction(@NonNull DeployParams params,
                                                   @Nullable Long backPoint,
                                                   @Nullable Long headPoint) {
        ProjectModel project = params.getProject();
        Region region = params.getRegion();
        PdmInfo pdmInfo = params.getPdmInfo();
        LegacyDeployConfig config = params.getConfig();
        Optional<CanaryRecord> before = historyService.getHead(project.getId(), region);

        before.ifPresent(o -> {
            if (headPoint != null && o.getId() != headPoint) {
                throw new InternalException("The current release version has been changed outside this transaction");
            }
        });

        List<CanaryRule> canaryRules = config.toCanaryRules();

        historyService.insertCanaryLog(params);

        CanaryRecord after = historyService.getHeadNeverNull(project.getId(), region);

        updateCanaryFile(project, region, config);
        updateCanaryFile(project, region, canaryRules);
        StorageHelper.touchLastModifiedFlag(region.getStorage(), Env.prd);

        if (backPoint == null) {
            backPoint = before.map(CanaryRecord::getId).orElse(null);
        }
        if (backPoint != null) {
            historyService.setPreviousCanary(after.getId(), new PreviousCanary(backPoint, region.name()));
        }

        // Update VanPublishMetaInfo
        if (pdmInfo != null) {
            putPublishMetaInfo(after.getId(), new PublishMetaInfo(pdmInfo.getId(), pdmInfo.getTitle()));
        }

        return after;
    }

    private <T> T runInTransaction(@NonNull Supplier<T> supplier) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus ts = transactionManager.getTransaction(def);
        try {
            T res = supplier.get();
            transactionManager.commit(ts);
            return res;
        } catch (Exception e) {
            transactionManager.rollback(ts);
            throw e;
        }
    }

    private void putPublishMetaInfo(long launchId, @NonNull PublishMetaInfo pmi) {
        metaService.setValue(MetaType.VanPublishMetaInfo, launchId, PublishMetaInfo.class, pmi);
    }

    @NonNull
    @Override
    public Optional<PublishMetaInfo> getPublishMetaInfo(long launchId) {
        return metaService.getOptional(MetaType.VanPublishMetaInfo, launchId, PublishMetaInfo.class);
    }

    @NonNull
    private List<BuildTaskModel> getAndCheckTasks(@NonNull ProjectModel project,
                                                  @NonNull LegacyDeployConfig config,
                                                  @NonNull Region region) {
        Set<Long> taskIds = config.collectTaskIds();

        Map<Long, BuildTaskModel> map = buildTaskService.batchGet(project.getId(), taskIds)
            .collect(Collectors.toMap(BuildTaskModel::getId, Function.identity()));

        final Storage storage = region.getStorage();
        Map<Long, Boolean> purgedFlagMap = map.values().stream().parallel().collect(
            Collectors.toMap(BuildTaskModel::getId,
                task -> StorageHelper.hasFlag(storage, project.getName(), task.getId(), TaskFlagName.Purged)));

        return taskIds.stream().map(id -> {
            BuildTaskModel task = map.get(id);
            if (task == null) {
                throw new VanBadRequestException(
                    "The task #" + id + " is not found (from project #" + project.getId() + ")");
            }
            if (task.getStatus() != BuildTaskStatus.Done) {
                throw new VanBadRequestException("The status of the task #" + id + " is not Done");
            }
            if (Boolean.TRUE.equals(purgedFlagMap.get(id))) {
                throw new VanBadRequestException(
                    "The task #" + id + " has been purged and cannot execute the release action");
            }
            return task;
        }).collect(Collectors.toList());
    }

    @Override
    @Deprecated
    public void releaseForProd(@NonNull UserModel user,
                               @NonNull ProjectModel project,
                               @NonNull Region region,
                               @NonNull LegacyDeployConfig config,
                               @Nullable String message) {
        List<BuildTaskModel> tasks = getAndCheckTasks(project, config, region);

        final Storage storage = region.getStorage();

        // Set release flag for each task.
        // NOTE: A task resource can be accessed from the internet only if the guard file exists.
        tasks.stream().parallel()
            .forEach(task -> StorageHelper.setFlag(storage, project.getName(), task.getId(), TaskFlagName.Released));

        updateCanaryFile(project, region, config.toCanaryRules());

        // TODO: This dirty logic is only used temporarily to keep compatibility, and should be removed after UD Migration project is complete.
        if (region == Region.bom) {
            updateCanaryFile(project, region, config);
        }

        StorageHelper.touchLastModifiedFlag(storage, Env.prd);

        // TODO: Do not add to the legacy canary history table. Creating a new table is a better solution.
        // historyService.insertCanaryLog(user, projectId, region, config, message);
    }

    @Override
    @NonNull
    public Optional<CanaryRule[]> getCurrentReleased(long projectId, @NonNull Region region) {
        String projectName = projectService.getNameById(projectId);
        return region.getStorage().getOptionalValue(region.fetchCanaryPath(projectName), CanaryRule[].class);
    }

    @NonNull
    @Override
    public Optional<LegacyDeployConfig> getCurrentLegacyReleased(long projectId, @NonNull Region region) {
        String projectName = projectService.getNameById(projectId);
        return region.getStorage()
            .getOptionalValue(region.fetchLegacyCanaryPath(projectName), LegacyDeployConfig.class);
    }

    @Override
    @NonNull
    public String fixPageServerCanaryFile(@NonNull Long projectId, @NonNull Region region, boolean overwrite) {
        String projectName = projectService.getNameById(projectId);
        String legacyPath = region.fetchLegacyCanaryPath(projectName);

        final Storage storage = region.getStorage();
        if (!storage.exist(legacyPath)) return "The legacy config file is not existing";

        LegacyDeployConfig config = storage.getOptionalValue(legacyPath, LegacyDeployConfig.class)
            .orElseThrow(() -> ResourceNotFoundException.create("legacyConfig", "projectId", projectId));

        List<CanaryRule> canaryRules = config.toCanaryRules();

        String pageServerConfigPath = region.fetchCanaryPath(projectName);

        if (storage.exist(pageServerConfigPath)) {
            HttpEntity<InputStream> obj = storage.getNeverNull(pageServerConfigPath);
            try {
                String oldJson = StreamUtils.copyToString(obj.getBody(), StandardCharsets.UTF_8);
                String newJson = mapper.writeValueAsString(canaryRules);
                if (oldJson.equals(newJson)) return "No changes";
                if (overwrite) {
                    storage.putValue(pageServerConfigPath, canaryRules);
                    return "Overwrote";
                } else {
                    return "A dirty config occurred on project (id=" + projectId + ")";
                }
            } catch (IOException e) {
                throw new InternalException(e);
            }
        } else {
            storage.putValue(pageServerConfigPath, canaryRules);
            return "OK";
        }
    }
}
