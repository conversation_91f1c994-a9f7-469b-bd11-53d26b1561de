package cn.huolala.van.api.implementation;

import cn.huolala.van.api.dao.entity.MiniprogramDeployHistoryEntity;
import cn.huolala.van.api.dao.enums.MiniprogramDeployType;
import cn.huolala.van.api.dao.repository.BuildTaskRepository;
import cn.huolala.van.api.dao.repository.MetaRepository;
import cn.huolala.van.api.dao.repository.MiniprogramDeployHistoryRepository;
import cn.huolala.van.api.dao.repository.UserRepository;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.InternalRequestException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.MiniprogramDeployParams;
import cn.huolala.van.api.model.deploy.MiniprogramDeployHistoryRecord;
import cn.huolala.van.api.service.MiniprogramService;
import cn.lalaframework.logging.LoggerFactory;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
public class MiniprogramServiceImpl implements MiniprogramService {

    @Value("${van.go.api.url}")
    private String goApiUrl;

    private static final Logger LOGGER = LoggerFactory.getLogger();

    @Autowired
    private CloseableHttpClient httpClient;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private MiniprogramDeployHistoryRepository miniprogramDeployHistoryRepository;

    @Autowired
    private MetaRepository metaRepository;

    @Autowired
    private BuildTaskRepository buildTaskRepository;

    @Autowired
    private UserRepository userRepository;

    @Override
    public Optional<MiniprogramDeployHistoryRecord> getLatestUploadDeploy(@NonNull long projectId, @NonNull long taskId) {
        return miniprogramDeployHistoryRepository
            .findLatestUploadDeploy(projectId, taskId, MiniprogramDeployType.Upload.getType())
            .map(MiniprogramDeployHistoryRecord::new);
    }

    @Override
    public List<MiniprogramDeployHistoryRecord> getLatestDeployByTypeOfEachRobot(@NonNull long projectId, @NonNull long taskId, @NonNull MiniprogramDeployType type) {
        return miniprogramDeployHistoryRepository
            .findLatestDeployByTypeOfEachRobot(projectId, taskId, type.getType())
            .stream()
            .map(MiniprogramDeployHistoryRecord::new)
            .collect(Collectors.toList());
    }

    @Override
    public Optional<MiniprogramDeployHistoryRecord> getByProjectIdAndDeployTaskId(@NonNull long projectId, @NonNull long deployTaskId) {
        return miniprogramDeployHistoryRepository
            .findByProjectIdAndDeployTaskId(projectId, deployTaskId)
            .map(MiniprogramDeployHistoryRecord::new);
    }

    /**
     * 小程序发布操作(预览或者上传)
     *
     * @param projectId   projectId
     * @param taskId      taskId
     * @param creatorId   creatorId
     * @param deployType  Preview/Upload
     * @param robot       robot 1-30
     * @param version     versions
     * @param description description
     */
    @Override
    @NonNull
    public long deployMiniprogram(@NonNull long projectId, @NonNull long taskId, @NonNull long creatorId, @NonNull MiniprogramDeployType deployType, @NonNull Integer robot, @Nullable String version, @Nullable String description) {
        if (deployType == MiniprogramDeployType.Upload && (version == null || version.isEmpty())) {
            throw new VanBadRequestException("version cannot be empty when uploading miniprogram");
        }
        if (robot < 1 || robot > 30) {
            throw new VanBadRequestException("robot should be between 1-30");
        }
        String ver = (version == null) ? "" : version;
        String desc = (description == null) ? "" : description;


        String url = String.format("%s/project/%d/task/%d/deploy_miniprogram", goApiUrl, projectId, taskId);
        HttpPost httpPost = new HttpPost(url);

        MiniprogramDeployParams deployParams = new MiniprogramDeployParams(deployType, creatorId, robot, ver, desc);
        try {
            httpPost.setHeader("Content-Type", "application/json");
            String jsonString = mapper.writeValueAsString(deployParams);
            httpPost.setEntity(new StringEntity(jsonString, ContentType.APPLICATION_JSON));
        } catch (IOException e) {
            throw new InternalMappingException(e);
        }

        CloseableHttpResponse response;
        try {
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity == null) {
                throw new InternalRequestException("failed to release miniprogram, entity is null");
            }
            String body = mapper.readValue(entity.getContent(), String.class);
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new InternalRequestException("failed to release miniprogram, " + body);
            }
            try {
                long deployTaskId = Long.parseLong(body);
                LOGGER.info("new miniprogram deploy taskId: {}", deployTaskId);
                MiniprogramDeployHistoryEntity ent = new MiniprogramDeployHistoryEntity(projectId, taskId, deployTaskId, creatorId, deployType, robot, ver, desc);
                miniprogramDeployHistoryRepository.save(ent);
                return deployTaskId;
            } catch (NumberFormatException e) {
                throw new InternalRequestException(e);
            }
        } catch (IOException e) {
            throw new InternalRequestException(e);
        }
    }


    @Override
    public long createMiniprogramDeployHistory(Long projectId, Long taskId, Long deployTaskId, Long creatorId, int type, int robot, String version, String description) {
        MiniprogramDeployType deployType = MiniprogramDeployType.fromInt(type);
        if (deployType == MiniprogramDeployType.Unknown) {
            throw new VanBadRequestException("deploy type is invalid");
        }
        MiniprogramDeployHistoryEntity entity = new MiniprogramDeployHistoryEntity(projectId, taskId, deployTaskId, creatorId, deployType, robot, version, description);
        return miniprogramDeployHistoryRepository.save(entity).getId();
    }
}
