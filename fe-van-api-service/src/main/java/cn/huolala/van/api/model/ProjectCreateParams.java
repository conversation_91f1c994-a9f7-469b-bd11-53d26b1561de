package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.model.meta.MiniprogramInfoModel;
import cn.huolala.van.api.model.project.ProjectConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;
import org.springframework.lang.NonNull;
import com.fasterxml.jackson.annotation.JsonProperty;

@Getter
@Setter
public class ProjectCreateParams {

    @NonNull
    private String name;

    @Nullable
    private String description;

    @Nullable
    private String repository;

    @NonNull
    private ProjectType type;

    @NonNull
    private ProjectConfig config;

    @Nullable
    @JsonProperty("repository_url")
    private String repositoryUrl;

    @Nullable
    private String[] tags;

    @Nullable
    @JsonProperty("miniprogram_meta")
    private MiniprogramInfoModel miniprogramMeta;
}
