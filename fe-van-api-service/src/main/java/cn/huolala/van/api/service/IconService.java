package cn.huolala.van.api.service;

import cn.huolala.van.api.model.IconResultModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface IconService {

    @NonNull
    List<IconResultModel> getCachedItems();

    @NonNull
    List<String> cleanCache();

    @NonNull
    IconResultModel getIcon(long projectId, @Nullable Long taskId) throws ExecutionException;
}
