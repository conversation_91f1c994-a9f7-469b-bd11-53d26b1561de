package cn.huolala.van.api.model.deploy;

import cn.huolala.van.api.dao.entity.CanaryHistoryEntity;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.lalaframework.spring.ApplicationContextUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mchange.util.AssertException;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public class CanaryRecord extends UserBase {
    public static final String NAME = "canary record";

    private final long id;

    @NonNull
    private final Long projectId;

    @NonNull
    private final String message;

    @NonNull
    private final Region region;

    @NonNull
    private final LegacyDeployConfig canary;

    @NonNull
    private final OffsetDateTime createdAt;

    public CanaryRecord(@NonNull CanaryHistoryEntity entity, @NonNull UserModel user) {
        super(user.getUniqId(), user.getName());

        id = entity.getId();
        projectId = entity.getProjectId();
        message = StringUtils.defaultString(entity.getMessage());
        region = Arrays.stream(Region.values()).filter(i -> i.name().equals(entity.getRegion())).findFirst().orElse(Region.cn);
        canary = parseConfig(entity.getCanary());
        createdAt = entity.getCreatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
    }

    @Nullable
    public static CanaryRecord create(@Nullable CanaryHistoryEntity entity, @Nullable UserModel user) {
        if (entity == null || user == null) return null;
        return new CanaryRecord(entity, user);
    }

    @NonNull
    private static LegacyDeployConfig parseConfig(String canary) {
        if (canary == null) return new LegacyDeployConfig();
        ObjectMapper mapper = ApplicationContextUtil.getBean(ObjectMapper.class);
        try {
            return mapper.readValue(canary, LegacyDeployConfig.class);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
    }

    @NonNull
    public List<CanaryRule> getRules() {
        return canary.toCanaryRules(true);
    }

    public Set<Long> collectTaskIds() {
        return canary.collectTaskIds();
    }

    public String toSummary() {
        return collectTaskIds().stream().map(i -> String.format("#%d", i)).collect(Collectors.joining(", "));
    }

    public void assertProjectId(long projectId) {
        if (this.projectId == projectId) return;
        String msg = String.format("The canary record #%d is owned by project #%d, " +
                "and it does not match the project #%d", id, this.projectId, projectId);
        throw new AssertException(msg);
    }
}
