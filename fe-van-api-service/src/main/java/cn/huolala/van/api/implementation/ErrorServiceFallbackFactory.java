package cn.huolala.van.api.implementation;

import cn.lalaweb.tools.facade.services.ErrorService;
import cn.lalaweb.tools.params.ErrorTimeRangeName;
import cn.lalaweb.tools.params.IssueStatus;
import cn.lalaweb.tools.facade.params.GroupProjectErrorStatus;
import cn.lalaframework.soa.annotation.Fallback;
import cn.lalaframework.soa.annotation.FallbackFactory;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;

@Service
@Fallback
public class ErrorServiceFallbackFactory implements FallbackFactory<ErrorService> {

	@Override
	public ErrorService create() {
		return new ErrorService() {

			@Override
			public List<GroupProjectErrorStatus> groupProjectErrorDetailByStatus(List<IssueStatus> status,
					List<String> projects, List<Long> taskIds, String env, List<String> level,
					ErrorTimeRangeName timeRangeName, Date from, Date to) throws Exception {
				// TODO Auto-generated method stub
				throw new UnsupportedOperationException("Unimplemented method 'groupProjectErrorDetailByStatus'");
			}

		};
	}

}
