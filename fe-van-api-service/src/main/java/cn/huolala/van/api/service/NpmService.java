
package cn.huolala.van.api.service;

import org.springframework.lang.NonNull;

import cn.huolala.van.api.dao.enums.ComponentPublishStatus;
import cn.huolala.van.api.model.npm.*;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.UserModel;

public interface NpmService {

    NpmPackageRemoteInfo getPackageRemoteInfo(@NonNull String packageName);

    NpmVersion publishToRegistry(@NonNull ProjectModel project, @NonNull BuildTaskModel task);

    void publish(@NonNull ProjectModel project, @NonNull BuildTaskModel task, @NonNull UserModel user, String message);

    NpmInfo getInfo(@NonNull ProjectModel project, @NonNull BuildTaskModel task);

    @NonNull
    String getReadMe(@NonNull ProjectModel project, @NonNull BuildTaskModel task);

    @NonNull
    NpmPackage getPackageJsonInfo(@NonNull ProjectModel project, @NonNull BuildTaskModel task);

    void createPublishCommit(
            @NonNull ProjectModel project,
            @NonNull BuildTaskModel task,
            @NonNull String name,
            @NonNull String version);

    void createPublishHistoryRecord(
            @NonNull String version,
            @NonNull long creatorId,
            @NonNull long projectId,
            @NonNull long taskId,
            @NonNull ComponentPublishStatus status,
            @NonNull String message);

    NpmVersions listNpmVersions(@NonNull ProjectModel project, @NonNull BuildTaskModel task);

}
