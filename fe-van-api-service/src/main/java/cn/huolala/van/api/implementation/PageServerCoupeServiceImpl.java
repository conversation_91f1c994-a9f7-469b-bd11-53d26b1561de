package cn.huolala.van.api.implementation;

import cn.huolala.van.api.model.PageServerPayload;
import cn.huolala.van.api.service.PageServerCoupeService;
import cn.lalaframework.exception.BussinessException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class PageServerCoupeServiceImpl implements PageServerCoupeService {
    private static final String TOKEN_TEMPLATE = "van:%s";

    @Autowired
    private CloseableHttpClient httpClient;

    private Cache<String, Map<String, Object>> cache;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${coupe.token}")
    private String coupeToken;

    @Value("${coupe.address}")
    private String coupeAddress;

    @PostConstruct
    private void init() {
        cache = CacheBuilder.newBuilder()
                .expireAfterWrite(60, TimeUnit.SECONDS)
                .build();
    }

    @Override
    public Map<String, Object> getCoupeInject(@Nonnull PageServerPayload payload) throws Exception {
        String fileContent = payload.getCoupeFileContent();
        Map<String, Object> map = cache.getIfPresent(fileContent);
        if (map != null) {
            return map;
        }
        payload.insertHeader("x-idc", "cn");
        map = sendCoupeRequest(payload);
        cache.put(fileContent, map);
        return map;
    }

    private Map<String, Object> sendCoupeRequest(@NotNull PageServerPayload payload) throws Exception {
        String prefix = StringUtils.trimTrailingCharacter(coupeAddress, '/');
        HttpPost post = new HttpPost(prefix + "/schema/pageServer");
        String body = objectMapper.writeValueAsString(payload);
        post.setEntity(new StringEntity(body));
        post.setHeader("Accept", "application/json");
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Authorization", String.format(TOKEN_TEMPLATE, coupeToken));
        CloseableHttpResponse response;
        try {
            response = httpClient.execute(post);
        } catch (IOException e) {
            throw new BussinessException(e);
        }
        int code = response.getStatusLine().getStatusCode();
        if (code != 200) {
            throw new RuntimeException(String.format("coupe request failed, status code: %d, url: %s", code, payload.getCoupeFileContent()));
        }
        return objectMapper.readValue(response.getEntity().getContent(), new TypeReference<Map<String, Object>>() {
        });
    }
}
