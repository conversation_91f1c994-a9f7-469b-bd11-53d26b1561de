package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SsoUserForSearch {
    private String id;

    private String account;

    private String userName;

    private String departmentPath;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    @JsonProperty("userName")
    public String getUserName() {
        return userName;
    }

    @JsonProperty("username")
    public void setUserName(String userName) {
        this.userName = userName;
    }

    @JsonProperty("departmentPath")
    public String getDepartmentPath() {
        return departmentPath;
    }

    @JsonProperty("department_path")
    public void setDepartmentPath(String departmentPath) {
        this.departmentPath = departmentPath;
    }
}