package cn.huolala.van.api.model.events;

import lombok.Getter;
import org.springframework.lang.NonNull;

import java.util.concurrent.ConcurrentHashMap;

public class ProjectId {
    private static final ConcurrentHashMap<Long, ProjectId> hashMap = new ConcurrentHashMap<>();
    @Getter
    private final long value;
    private byte incremental;

    private ProjectId(long value) {
        this.incremental = 0;
        this.value = value;
    }

    @NonNull
    public static ProjectId singleInstance(long value) {
        return hashMap.computeIfAbsent(value, ProjectId::new);
    }

    public synchronized void signalAll() {
        incremental++;
        notifyAll();
    }

    public synchronized boolean await(long millis) throws InterruptedException {
        long timestamp = System.currentTimeMillis();
        byte stamp = incremental;
        while (stamp == incremental) {
            wait(millis);
            if (System.currentTimeMillis() - timestamp > millis) return false;
        }
        return true;
    }
}
