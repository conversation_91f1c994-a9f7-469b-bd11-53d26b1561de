package cn.huolala.van.api.service.feishu;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import cn.huolala.van.api.model.ApprovalForm;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface HuolalaFeishuEnhanceService {
    @Nullable
    String getUniqNameByLarkUserId(String larkUserId);

    Map<String, String> uniqNamesInLarkUserId(List<String> larkUserIds);

    @NonNull
    Map<String, String> larkUserIdInUniqNames(@Nullable Collection<String> uniqNames);

    @NonNull
    String createApproval(@NonNull String approvalCode, @NonNull String userId, ApprovalForm[] forms);

}
