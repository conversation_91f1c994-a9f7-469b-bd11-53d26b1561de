package cn.huolala.van.api.service;

import cn.huolala.van.api.model.UserBase;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.deploy.CanaryRecord;
import cn.huolala.van.api.model.deploy.DeployRecord;
import cn.huolala.van.api.model.feishu.FeishuCard;
import cn.huolala.van.api.model.feishu.FeishuSubscriptionModel;
import cn.huolala.van.api.model.feishu.GetSheetRangeParams;
import cn.huolala.van.api.model.meta.FeishuNotificationConfigurationModel;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.lark.oapi.service.bitable.v1.model.*;
import com.lark.oapi.service.contact.v3.model.User;
import com.lark.oapi.service.docx.v1.enums.RawContentDocumentLangEnum;
import com.lark.oapi.service.docx.v1.model.Document;
import com.lark.oapi.service.docx.v1.model.ListDocumentBlockRespBody;
import com.lark.oapi.service.docx.v1.model.RawContentDocumentRespBody;
import com.lark.oapi.service.im.v1.model.CreateImageRespBody;
import com.lark.oapi.service.sheets.v3.model.GetSpreadsheet;
import com.lark.oapi.service.sheets.v3.model.Sheet;
import com.lark.oapi.service.wiki.v2.model.Node;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.File;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface FeishuService {
    @Nullable
    User getUserInfo(@Nullable String uniqId);

    @NonNull
    List<User> batchGetUserInfo(@Nullable Collection<String> uniqIds);

    @NonNull
    List<FeishuSubscriptionModel> findSubscriptions(long projectId);

    @Nullable
    FeishuNotificationConfigurationModel getVanFeishuNotificationConfiguration(Long projectId);

    void updateVanFeishuNotificationConfiguration(Long projectId, FeishuNotificationConfigurationModel model);

    void sendVanCardByUser(String content, UserModel user);

    void notifyForPrdEnvLaunch(@NonNull ProjectModel project,
                               @NonNull List<BuildTaskModel> tasks,
                               @NonNull CanaryRecord canaryRecord);

    void notifyForTestEnvLaunch(@NonNull ProjectModel project,
                                @NonNull BuildTaskModel task,
                                @NonNull DeployRecord deployRecord);

    @Nullable
    @CanIgnoreReturnValue
    String sendCard(@NonNull FeishuCard card, @Nullable String chatId);

    @Nullable
    @CanIgnoreReturnValue
    String sendCard(@NonNull FeishuCard card, @NonNull UserBase vanUser);

    void updateCard(@NonNull FeishuCard card, @Nullable String messageId);

    void sendTextToUser(@NonNull String text, @NonNull User user);

    @NonNull
    List<Object> getSheetWithCache(GetSheetRangeParams params, int maxAge);

    @NonNull
    List<Object> getSheet(String spreadsheetToken, String range, String valueRenderOption);

    @Nullable
    Node getWikiNode(String token);

    @Nullable
    GetSpreadsheet getSheetMeta(String spreadsheetToken);

    @Nullable
    Sheet getSheetMeta(String spreadsheetToken, String sheetId);

    @NonNull
    Sheet[] getSheets(String spreadsheetToken);

    @Nullable
    ListDocumentBlockRespBody getDocumentBlocks(String documentId);

    @Nullable
    RawContentDocumentRespBody getDocumentRawContent(String documentId, RawContentDocumentLangEnum lang);

    @Nullable
    Document getDocumentMeta(String documentId);

    @NonNull
    Optional<DisplayApp> getBitableApp(String appToken);

    @NonNull
    Optional<ListAppTableRecordRespBody> getBitableRecords(String appToken, String tableId, String viewId);

    @NonNull
    Optional<ListAppTableViewRespBody> getBitableTableViews(String appToken, String tableId);

    @NonNull
    Optional<ListAppTableRespBody> getBitableTables(String appToken);

    @NonNull
    Optional<ListAppDashboardRespBody> getBitableDashboard(String appToken);

    @NonNull
    Optional<CreateImageRespBody> uploadImage(File file);

    @NonNull
    void sendCardToUser(FeishuCard card, String userUniqId);

}
