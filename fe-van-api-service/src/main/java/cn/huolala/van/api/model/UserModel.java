package cn.huolala.van.api.model;

import cn.huolala.van.api.dao.entity.UserEntity;
import cn.huolala.van.api.exception.DirtyDataException;

import java.time.LocalDateTime;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public class UserModel {
    @NonNull
    public static final UserModel unknown = new UserModel(new UserEntity("unknown"));
    @NonNull
    public static final UserModel vanBot = new UserModel(new UserEntity("van-bot"));

    @NonNull
    private final UserEntity entity;

    public UserModel(@NonNull UserEntity entity) {
        this.entity = entity;
    }

    @NonNull
    public static UserModel wrap(@Nullable UserEntity entity) {
        if (entity == null) throw new DirtyDataException("The UserEntity is required here");
        // These fields are marked as NOT NULL in MySQL.
        return new UserModel(entity);
    }

    @NonNull
    public UserEntity unwrap() {
        return this.entity;
    }

    @Override
    public String toString() {
        if (getName().isEmpty()) return getUniqId();
        return String.format("%s(%s)", getName(), getUniqId());
    }

    /**
     * The user ID of Van, not the SSO ID.
     */
    @NonNull
    public Long getId() {
        return entity.getId();
    }

    /**
     * The SSO username, is also the user real name.
     */
    @NonNull
    public String getName() {
        return entity.getName();
    }

    /**
     * The SSO account, is also the prefix of the user email.
     */
    @NonNull
    public String getUniqId() {
        return entity.getUniqId();
    }

    /**
     * user email
     */
    @NonNull
    public String getEmail() {
        return entity.getEmail();
    }


    public LocalDateTime getCreatedAt() {
        return entity.getCreatedAt();
    }

    public LocalDateTime getUpdatedAt() {
        return entity.getUpdatedAt();
    }
}
