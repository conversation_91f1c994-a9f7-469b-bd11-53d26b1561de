package cn.huolala.van.api.model;

import lombok.Data;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.LocalDate;

@Data
public class ImportantProjectsParams {
    @NonNull
    private LocalDate date;
    @NonNull
    private Integer limit;

    public ImportantProjectsParams() {
        this(null, null);
    }

    public ImportantProjectsParams(@Nullable LocalDate date, @Nullable Integer limit) {
        this.date = date == null ? LocalDate.now().withDayOfMonth(1) : date;
        this.limit = limit == null ? 40 : limit;
    }
}
