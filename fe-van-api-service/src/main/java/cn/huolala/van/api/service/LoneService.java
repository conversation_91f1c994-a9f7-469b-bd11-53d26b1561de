package cn.huolala.van.api.service;

import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.lone.*;
import cn.huolala.van.api.model.roles.RegionalInfoMap;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

public interface LoneService {

    List<LoneApp> getBusListByUser(LoneRegion region, LoneEnv env, String userUniq);

    @Nullable
    LoneAppDetail getOneAppidInfo(@NonNull LoneRegion region, @NonNull LoneEnv env, @NonNull String appId);

    @Nullable
    LoneAppSummary getOneAppidInfoSummary(@NonNull LoneRegion region, @NonNull LoneEnv env, @NonNull String appId);

    @NonNull
    List<LoneAppSummary> getAllAppid(@Nullable String keyword);

    @NonNull
    Map<String, RegionalInfoMap> loadRegionalRoleMap(@NonNull String appId);

    default boolean isParticipant(String appId, UserModel currentUser) {
        return LoneRegion.supportedRegions()
                .parallel().map(i -> getOneAppidInfo(i, LoneEnv.prd, appId))

                // Merge and flatten the `leaderList`, `developList`, and `testerList` into a stream.
                .filter(Objects::nonNull).map(LoneAppDetail::getBusUser)
                .filter(Objects::nonNull)
                .flatMap(i -> Stream.of(i.getLeaderList(), i.getDevelopList(), i.getTesterList()))
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                // Try to match the user.
                .anyMatch(currentUser.getUniqId()::equals);
    }

    @Nullable
    LoneAppWindow getAppWindow(@NonNull String appId, @NonNull LoneRegion region);

}
