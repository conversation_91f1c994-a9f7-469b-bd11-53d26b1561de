package cn.huolala.van.api.model.tasks;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

public interface TaskPath {
    @Nullable
    default Pair<Integer, String> numPairToString(int size) {
        if (this instanceof TaskDirName) {
            TaskDirName key = (TaskDirName) this;
            return Pair.of(size, size == 1 ? key.getSingle() : key.getPlural());
        }
        if (this instanceof TaskFileName) {
            TaskFileName key = (TaskFileName) this;
            if (size == 0) return null;
            return Pair.of(1, key.getName());
        }
        return null;
    }

    @NonNull
    String getTemplate();

    default String build(@NonNull String projectName, @NonNull Long taskId) {
        return String.format(getTemplate(), projectName, taskId);
    }
}
