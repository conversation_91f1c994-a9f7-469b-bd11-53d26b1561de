package cn.huolala.van.api.util;

import cn.huolala.van.api.model.ConfigEnv;
import cn.huolala.van.api.model.project.ProjectModel;
import com.mchange.util.AssertException;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Set;
import java.util.function.BiFunction;

public class VanUtils {
    private VanUtils() {
    }

    @NonNull
    public static String buildProjectLink(long projectId) {
        return String.format("https://van.huolala.work/projects/%d", projectId);
    }

    @NonNull
    public static String buildTaskLink(long projectId, long taskId) {
        return String.format("%s/task?id=%d", buildProjectLink(projectId), taskId);
    }

    @NonNull
    public static String buildTestingUrl(@NonNull String projectName, @NonNull ConfigEnv ce) {
        StringBuilder sb = new StringBuilder("https://");
        sb.append(projectName);
        if (ce.getNumber() > 0) sb.append("--stable-").append(ce.getNumber());
        sb.append("-v-");
        sb.append(ce.getName().name());
        sb.append(".huolala.work");
        return sb.toString();
    }

    @NonNull
    public static String getProjectEnvLink(@NonNull ProjectModel project, @NonNull ConfigEnv env) {
        StringBuilder sb = new StringBuilder();
        sb.append("https://");
        sb.append(project.getName());
        String devDomain = project.getConfig().getDevDomain();
        if ("lalamove.com".equals(devDomain)) {
            sb.append(".sg-van-");
            sb.append(env);
        } else {
            if (env.getNumber() > 0) {
                sb.append("--");
                sb.append(env);
            }
            sb.append("-v-");
            sb.append(env.getName());
        }
        sb.append(".");
        sb.append(devDomain);
        return sb.toString();
    }

    public static String buildMonitorUrl(@NonNull ProjectModel project) {
        String monitorBase = "https://monitor-h5-v.huolala.cn";
        return String.format("%s/board/view/14?globalId=van-overview&&refresh=30s&project=%s", monitorBase,
            project.getName());
    }

    public static void assertOwnership(long projectId,
                                       @Nullable Set<Long> ids,
                                       @NonNull BiFunction<Set<Long>, Long, Long[]> findOutOfProjectId,
                                       @NonNull String single,
                                       @NonNull String plural) {
        if (ids == null || ids.isEmpty()) return;
        Long[] res = findOutOfProjectId.apply(ids, projectId);
        if (res.length == 0) return;
        StringBuilder sb = new StringBuilder("The ");
        sb.append(res.length == 1 ? single : plural);
        sb.append(" ");
        for (int i = 0; i < res.length; i++) {
            if (i > 0) sb.append(", ");
            sb.append("#");
            sb.append(res[i]);
        }
        sb.append(" ");
        sb.append(res.length == 1 ? "is" : "are");
        sb.append(" outside of the project #");
        sb.append(projectId);
        throw new AssertException(sb.toString());
    }

    public static String buildMiniprogramTaskLink(long projectId, long taskId) {
        return String.format("https://van.huolala.work/miniprograms/%d/task?id=%d", projectId, taskId);
    }

    public static String buildGrayPageUrl(Long projectId) {
        return MdUtil.link("项目灰度页", buildProjectLink(projectId) + "/gray");
    }
}
