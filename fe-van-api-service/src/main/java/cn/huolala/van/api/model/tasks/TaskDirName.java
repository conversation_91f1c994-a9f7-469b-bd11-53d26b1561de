package cn.huolala.van.api.model.tasks;

import lombok.Getter;
import org.springframework.lang.NonNull;

@Getter
public enum TaskDirName implements TaskPath {
    Meta("%s/tasks_meta/%d/", "meta", "metas"),
    Resources("%s/%d/", "resource", "resources");

    @NonNull
    private final String template;
    @NonNull
    private final String single;
    @NonNull
    private final String plural;

    TaskDirName(@NonNull String template, @NonNull String single, @NonNull String plural) {
        this.template = template;
        this.single = single;
        this.plural = plural;
    }
}
