package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.util.Set;

@Getter
@Setter
public class HistorySearchBase {
    @Nullable
    private Set<String> userUniqIds;

    @Nullable
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime startDate;

    @Nullable
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private OffsetDateTime endDate;

    @ApiModelProperty("A zero-based page number")
    private int page;

    private int size;

    @JsonIgnore
    public int getLimit() {
        return size;
    }

    @JsonIgnore
    public int getOffset() {
        return page * size;
    }
}
