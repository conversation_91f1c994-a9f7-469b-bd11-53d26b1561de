package cn.huolala.van.api.model;

import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.model.deploy.LegacyDeployConfig;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type", include = JsonTypeInfo.As.EXISTING_PROPERTY, defaultImpl = Snippet.Static.class)
@JsonSubTypes({
        @JsonSubTypes.Type(value = Snippet.Static.class, name = "static"),
        @JsonSubTypes.Type(value = Snippet.Internal.class, name = "internal"),
        @JsonSubTypes.Type(value = Snippet.Dynamic.class, name = "dynamic")
})
public interface Snippet {
    static String getContent(Snippet snippet) {
        if (snippet instanceof Static) {
            return ((Static) snippet).getContent();
        } else if (snippet instanceof Dynamic || snippet instanceof Internal) {
            return "";
        }
        throw new InternalMappingException(String.format("unknown snippet type: %s", snippet.getType()));
    }

    String getType();

    String getName();

    Boolean getDisabled();

    Snippet.Place getPlace();

    Integer getPlaceOrder();

    Integer getPriority();

    String getMeta();

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    enum ErrorHandling {
        @JsonEnumDefaultValue
        ignore,
        ignoreWithNotification,
        abort,
    }

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    enum Place {
        @JsonEnumDefaultValue
        unknownPlace,
        HeadBefore,
        HeadAfter,
        BodyBefore,
        BodyAfter
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    abstract class Base implements Snippet {
        @NonNull
        private String name = "";
        @NonNull
        private Snippet.Place place = Snippet.Place.unknownPlace;

        @Nullable
        private Integer placeOrder = 0;
        @Nullable
        private Integer priority = 0;

        @Nullable
        private String meta = "";

        @Nullable
        private Boolean disabled = false;

        public Integer getPlaceOrder() {
            return placeOrder == null ? 0 : placeOrder;
        }

        public Integer getPriority() {
            return priority == null ? 0 : priority;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Static extends Base {
        @NonNull
        private String content = "";

        @Nullable
        @Deprecated
        @SuppressWarnings({"java:S1123", "java:S1133"})
        private Boolean isTemplate = false;

        @Nullable
        @Deprecated
        @SuppressWarnings({"java:S1123", "java:S1133"})
        private List<LegacyDeployConfig.Operator> canary;

        @NonNull
        @Override
        @ApiModelProperty(allowableValues = "static")
        public String getType() {
            return "static";
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Internal extends Base {
        private String project;
        private String path;
        private Integer timeout = 0;
        private ErrorHandling errorHandling = ErrorHandling.ignore;

        @Nullable
        @Deprecated
        @SuppressWarnings({"java:S1123", "java:S1133"})
        private List<LegacyDeployConfig.Operator> canary;

        @Nullable
        @Deprecated
        @SuppressWarnings({"java:S1123", "java:S1133"})
        private String content = "";

        @NonNull
        @Override
        @ApiModelProperty(allowableValues = "internal")
        public String getType() {
            return "internal";
        }

        public Integer getTimeout() {
            return timeout == null ? 0 : timeout;
        }
    }

    @Getter
    @Setter
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Dynamic extends Base {
        @NonNull
        private String url = "";
        @Nullable
        private Object cache;
        @Nullable
        private List<String> versionFiles;
        private Integer timeout = 0;
        private ErrorHandling errorHandling = ErrorHandling.ignore;

        @NonNull
        @Override
        @ApiModelProperty(allowableValues = "dynamic")
        public String getType() {
            return "dynamic";
        }

        public Integer getTimeout() {
            return timeout == null ? 0 : timeout;
        }
    }

    @Getter
    @Setter
    class Update {
        private Boolean remove = false;

        @NonNull
        private Snippet snippet;

        public String getName() {
            return snippet.getName();
        }

        public Boolean getDisabled() {
            return snippet.getDisabled();
        }

        public Place getPlace() {
            return snippet.getPlace();
        }

        public String getMeta() {
            return snippet.getMeta();
        }
    }
}
