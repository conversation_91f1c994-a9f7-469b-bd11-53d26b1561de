package cn.huolala.van.api.configuration;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.lalaframework.config.core.PropertyConfigurer;

@Configuration
public class ElasticsearchConfig {

	@Bean
	public ElasticsearchClient elasticsearchClient() {
		String HOST = PropertyConfigurer.getString("es.host");
		int PORT = PropertyConfigurer.getInteger("es.port", 80);
		String SCHEME = PropertyConfigurer.getString("es.schema", "http");
		String USERNAME = PropertyConfigurer.getString("es.name");
		String PASSWORD = PropertyConfigurer.getString("es.password");
		final BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
		credentialsProvider.setCredentials(
				AuthScope.ANY,
				new UsernamePasswordCredentials(USERNAME, PASSWORD));

		RestClient restClient = RestClient.builder(
				new HttpHost(HOST, PORT, SCHEME))
				.setHttpClientConfigCallback(new RestClientBuilder.HttpClientConfigCallback() {
					@Override
					public HttpAsyncClientBuilder customizeHttpClient(HttpAsyncClientBuilder httpClientBuilder) {
						return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
					}
				})
				.setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
						.setConnectTimeout(120000)
						.setSocketTimeout(120000)
						.setConnectionRequestTimeout(120000))
				.build();

		ElasticsearchTransport transport = new RestClientTransport(
				restClient,
				new JacksonJsonpMapper());

		return new ElasticsearchClient(transport);
	}
}