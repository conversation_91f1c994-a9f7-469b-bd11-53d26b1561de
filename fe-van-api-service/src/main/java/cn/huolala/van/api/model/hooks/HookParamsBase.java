package cn.huolala.van.api.model.hooks;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.project.ProjectModel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import org.springframework.lang.NonNull;

@Getter
public abstract class HookParamsBase {
    @NonNull
    private final HookParamsProject project;

    /**
     * The userUniqId not "username".
     */
    @NonNull
    private final String username;

    /**
     * Env name without stable name.
     */
    @NonNull
    @JsonSerialize(using = Env.LongNameSerializer.class)
    private final Env env;

    protected HookParamsBase(@NonNull ProjectModel project, @NonNull UserModel user, @NonNull Env env) {
        this.project = new HookParamsProject(project);
        this.username = user.getUniqId();
        this.env = env;
    }
}

