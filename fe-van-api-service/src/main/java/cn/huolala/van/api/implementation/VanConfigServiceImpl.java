package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.api.constants.model.WatchDogAllConfigView;
import cn.huolala.api.constants.model.WatchDogGlobalConfig;
import cn.huolala.api.constants.model.WatchDogProjectConfig;
import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.InternalMappingException;
import cn.huolala.van.api.exception.ResourceNotFoundException;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.config.VanConfig;
import cn.huolala.van.api.model.config.VanConfigFile;
import cn.huolala.van.api.model.config.VanConfigGroup;
import cn.huolala.van.api.model.events.ProjectEvent;
import cn.huolala.van.api.model.events.ProjectEventType;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.service.EventService;
import cn.huolala.van.api.service.ProjectService;
import cn.huolala.van.api.service.VanConfigService;
import cn.lalaframework.storage.adapter.Storage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static java.util.concurrent.CompletableFuture.supplyAsync;


@Service
public class VanConfigServiceImpl implements VanConfigService {
    private static final String prefix = ".van";
    private static final String groupName = "vanConfig";
    private static final ConcurrentHashMap<Env, WatchDogAllConfigView> watchDogAllConfigViewCache = new ConcurrentHashMap<>();
    @Autowired
    private EventService eventService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ObjectMapper mapper;

    @NonNull
    private String buildPath(Env env, String projectName, String fileName) {
        return String.format("%s/project/%s/%s", buildPath(env), projectName, fileName);
    }

    @NonNull
    private String buildPath(Env env) {
        return String.format("%s/%s/%s", prefix, groupName, env.name());
    }

    private List<VanConfigFile> listFile(String filePath) {
        return Region.defaultStorage().listAll(filePath, null).map(obj -> {
            VanConfigFile file = new VanConfigFile();
            Path path = Paths.get(obj.getKey());
            String name = path.getFileName().toString();
            file.setName(name);
            file.setUpdatedAt(obj.getLastModified());
            return file;
        }).collect(Collectors.toList());
    }

    @Nullable
    private OffsetDateTime getLastModified(Env env) {
        return Region.defaultStorage().getOptionalValue(buildPath(env) + "/groupInfo.json", VanConfigGroup.class)
            .map(VanConfigGroup::getLastModified)
            .map(n -> n.toInstant().atZone(ZoneId.systemDefault()).toOffsetDateTime()).orElse(null);
    }

    public void refreshUpdatedAt(Env env) {
        VanConfigGroup config = new VanConfigGroup();
        config.setUpdatedAt(new Date());
        config.setLastModified(new Date());
        Region.defaultStorage().putValue(this.buildPath(env) + "/groupInfo.json", config);
    }

    @NonNull
    public List<ProjectEvent> getProjectFileEvent(@NonNull ProjectModel project) {
        return eventService.findEvents(project.getId(), ProjectEventType.EditProjectConfigEvent);
    }

    @Override
    public void createConfig(@NonNull ProjectModel project, @NonNull VanConfig config) {
        String path = buildPath(config.getEnv(), config.getProjectName(), config.getName());

        final Storage storage = Region.defaultStorage();

        if (storage.exist(path)) {
            throw new InternalException("The config (name=" + config.getName() + ") already exists");
        }

        config.setLatestCommitId(config.getUpdatedAt());
        storage.putValue(path, config);
        refreshUpdatedAt(config.getEnv());
    }

    /**
     * @return The old config.
     */
    @Override
    @NonNull
    public VanConfig updateConfig(@NonNull ProjectModel project, @NonNull VanConfig config) {
        String path = buildPath(config.getEnv(), config.getProjectName(), config.getName());

        final Storage storage = Region.defaultStorage();

        if (!storage.exist(path)) {
            throw ResourceNotFoundException.create("config", "name", config.getName());
        }

        config.setGroupName(groupName);
        VanConfig oldConfig = getAndAssertConfig(config.getEnv(), project.getName(), config.getName());
        config.setLatestCommitId(config.getUpdatedAt());

        storage.putValue(path, config);
        refreshUpdatedAt(config.getEnv());
        return oldConfig;
    }

    @Override
    @NonNull
    public VanConfig getAndAssertConfig(@NonNull Env env, @NonNull String projectName, @NonNull String fileName) {
        String path = buildPath(env, projectName, fileName);
        return Region.defaultStorage().getValueNeverNull(path, VanConfig.class);
    }

    @Override
    @Nullable
    public VanConfig getConfig(@NonNull Env env, @NonNull String projectName, @NonNull String fileName) {
        String path = buildPath(env, projectName, fileName);
        return Region.defaultStorage().getOptionalValue(path, VanConfig.class).orElse(null);
    }

    @Nullable
    private <T> T getAndParseConfig(@NonNull Env env,
                                    @NonNull String projectName,
                                    @NonNull String fileName,
                                    @NonNull Class<T> type) {
        VanConfig vanConfig = getConfig(env, projectName, fileName);
        if (vanConfig == null) return null;
        String content = vanConfig.getContent();
        if (content == null || content.isEmpty()) return null;
        try {
            return mapper.readValue(content, type);
        } catch (JsonProcessingException e) {
            throw new InternalMappingException(e);
        }
    }

    @Override
    @NonNull
    public List<VanConfigFile> listConfigName(@NonNull Env env, @NonNull ProjectModel project) {
        String filePath = buildPath(env, project.getName(), "");
        return this.listFile(filePath);
    }

    @Override
    @NonNull
    public WatchDogAllConfigView getWatchDogAllConfigView(@NonNull Env env) {
        return watchDogAllConfigViewCache.compute(env, (key, value) -> {
            // Get lastModified from groupInfo.json (This file is updated with each config file update).
            OffsetDateTime lastModified = getLastModified(key);

            // Compare lastModified between cached value and groupInfo.json.
            if (value != null && lastModified != null) {
                OffsetDateTime valueDate = value.getLastModified();
                if (valueDate != null && !lastModified.isAfter(valueDate)) {
                    return value;
                }
            }

            return getDogAllConfigViewWithoutCache(key, lastModified);
        });
    }

    @NonNull
    private WatchDogAllConfigView getDogAllConfigViewWithoutCache(@NonNull Env env,
                                                                  @Nullable OffsetDateTime lastModified) {
        CompletableFuture<WatchDogGlobalConfig> tGlobalConfig = supplyAsync(
            () -> getAndParseConfig(env, "watch-dog", "global.json", WatchDogGlobalConfig.class));

        Map<String, WatchDogProjectConfig> projectConfigMap = projectService.findNamesByTypes(ProjectType.Web,
            ProjectType.Miniprogram).parallel().map(name -> {
            WatchDogProjectConfig config = getAndParseConfig(env, name, "setting.json", WatchDogProjectConfig.class);
            if (config == null) return null;
            return Pair.of(name, config);
        }).filter(Objects::nonNull).collect(Collectors.toMap(Pair::getKey, Pair::getValue));

        ZoneId zone = ZoneId.systemDefault();
        return new WatchDogAllConfigView(
            lastModified == null ? null : lastModified.toInstant().atZone(zone).toOffsetDateTime(),
            tGlobalConfig.join(), projectConfigMap);
    }
}
