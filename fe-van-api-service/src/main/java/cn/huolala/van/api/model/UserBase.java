package cn.huolala.van.api.model;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Getter
public abstract class UserBase {
    public static final Pattern userNameParser = Pattern.compile("^(.*?)(?:\\((.*?)\\))?$");
    public static final Pattern uniqIdDetector = Pattern.compile("^[a-z0-9]+(\\.[a-z0-9]+){0,3}$");

    @NonNull
    protected final String userName;
    @NonNull
    protected final String userUniqId;

    protected UserBase(@Nullable String userUniqId, @Nullable String userName) {
        this.userUniqId = StringUtils.defaultString(userUniqId);
        this.userName = StringUtils.defaultIfBlank(userName, this.userUniqId);
    }

    @Nullable
    public static Simple create(@Nullable String userUniqId, @Nullable String userName) {
        if (userUniqId == null || userUniqId.isEmpty()) return null;
        return new Simple(userUniqId, userName);
    }

    /**
     * Attempt to get the User object by a terrible username.
     * For example, the {@param mixed} may be one of following formats:
     * - 张三(san.zhang)
     * - 张三
     * - san.zhang
     * - van-bot
     *
     * @return A null if the User object is not found by the terrible username, otherwise return User object.
     */
    @Nullable
    public static Simple parse(@Nullable String mixed) {
        if (mixed == null) return null;
        Matcher m = userNameParser.matcher(mixed);

        // Return a null if the terribleUserName mismatch the pattern.
        if (!m.find()) return null;

        // The group(1) is never null.
        String first = m.group(1).trim();

        // The group(2) is strictly a uniqId if it exists and not empty.
        String second = m.group(2);
        if (second != null) {
            second = second.trim();
            if (!second.isEmpty()) {
                if (first.equals(second)) first = "";
                return new Simple(second, first);
            }
        }

        // If the group(1) looks like a uniqId.
        if (uniqIdDetector.matcher(first).matches()) {
            return new Simple(first, "");
        }

        return new Simple("", first);
    }

    @NonNull
    public Simple extractSimple() {
        if (this instanceof Simple) return (Simple) this;
        return new Simple(getUserUniqId(), getUserName());
    }

    @NonNull
    public String toUserString() {
        return userName.isEmpty() || userName.equals(userUniqId)
                ? userUniqId
                : String.format("%s(%s)", userName, userUniqId);
    }

    public static final class Simple extends UserBase {
        public Simple(@Nullable String userUniqId, @Nullable String userName) {
            super(userUniqId, userName);
        }
    }
}
