package cn.huolala.van.api.configuration;

import com.lark.oapi.Client;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeishuClientConfiguration {

    @Value("${feishu.appid}")
    private String feishuAppId;

    @Value("${feishu.secret}")
    private String feishuSecret;

    @Value("${van.feishu.appid}")
    private String vanFeishuAppId;

    @Value("${van.feishu.secret}")
    private String vanFeishuSecret;

    @Bean(name = "van")
    public Client vanFeishuClient() {
        return Client.newBuilder(vanFeishuAppId, vanFeishuSecret).build();
    }

    @Bean(name = "doc")
    public Client feishuClient() {
        return Client.newBuilder(feishuAppId, feishuSecret).build();
    }
}
