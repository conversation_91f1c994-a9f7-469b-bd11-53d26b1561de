package cn.huolala.van.api.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import org.apache.logging.log4j.util.Strings;
import org.springframework.lang.NonNull;

import java.util.ArrayList;
import java.util.Arrays;

public class CsvRow extends ArrayList<String> {
    @NonNull
    @JsonCreator
    public static CsvRow create(Object raw) {
        CsvRow row = new CsvRow();
        if (raw instanceof String) {
            Arrays.stream(((String) raw).split(",")).filter(Strings::isNotBlank).forEach(row::add);
        }
        return row;
    }
}
