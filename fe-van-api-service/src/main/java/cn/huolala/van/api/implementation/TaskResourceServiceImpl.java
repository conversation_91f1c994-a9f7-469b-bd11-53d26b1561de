package cn.huolala.van.api.implementation;

import cn.huolala.api.constants.enums.BuildTaskType;
import cn.huolala.api.constants.enums.MetaType;
import cn.huolala.api.constants.enums.ProjectType;
import cn.lalaframework.storage.adapter.Storage;
import cn.huolala.van.api.exception.InternalException;
import cn.huolala.van.api.exception.VanBadRequestException;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.meta.TaskSyncInfo;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.tasks.TaskDirName;
import cn.huolala.van.api.model.tasks.TaskFileName;
import cn.huolala.van.api.model.tasks.TaskFlagName;
import cn.huolala.van.api.service.*;
import cn.huolala.van.api.util.EnglishListBuilder;
import cn.huolala.van.api.util.StorageHelper;
import cn.lalaframework.soa.exception.BusinessException;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.groovy.util.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.nio.file.Paths;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class TaskResourceServiceImpl implements TaskResourceService {
    @Autowired
    private ProjectService projectService;
    @Autowired
    private BuildTaskService buildTaskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private MetaService metaService;

    private Cache<Long, Set<Long>> last30taskIdscache;

    private static VanBadRequestException youCannotPurgeTheTaskAs(String reason) {
        return new VanBadRequestException("Cannot purge the task as " + reason);
    }

    @Override
    @NonNull
    public List<VanResourceSummary> listTaskResources(@NonNull BuildTaskModel task, @Nullable String tail) {
        final String path = buildPath(task, TaskDirName.Resources, tail);

        return Region.defaultStorage().listAll(path, null).map(i -> {
            String key = i.getKey().substring(path.length());
            return new VanResourceSummary(key, i.getSize());
        }).collect(Collectors.toList());
    }

    @Override
    @Nullable
    public HttpEntity<InputStream> getTaskResource(@NonNull BuildTaskModel task, @Nullable String path) {
        String absPath = buildPath(task, TaskDirName.Resources, path);
        final Storage storage = Region.defaultStorage();
        return storage.exist(absPath) ? storage.get(absPath) : null;
    }

    @Override
    @NonNull
    public Stream<VanResourceSummary> listTaskMeta(@NonNull BuildTaskModel task, String tail) {
        final String path = buildPath(task, TaskDirName.Meta, tail);
        return Region.defaultStorage().listAll(path, null).map(i -> {
            String key = i.getKey().substring(path.length());
            return new VanResourceSummary(key, i.getSize());
        });
    }

    @Override
    @Nullable
    public HttpEntity<InputStream> getTaskMeta(@NonNull BuildTaskModel task, String tail) {
        final String path = buildPath(task, TaskDirName.Meta, tail);
        return Region.defaultStorage().get(path);
    }

    @NonNull
    private String buildPath(@NonNull BuildTaskModel task, TaskDirName meta, @Nullable String tail) {
        String name = projectService.getNameById(task.getProjectId());
        String base = meta.build(name, task.getId());
        String path = tail == null ? base : Paths.get(base, tail).normalize().toString();
        if (!path.startsWith(base)) {
            throw new BusinessException(String.format("Unsafe path '%s'", path));
        }
        return path;
    }

    @Override
    public Map<String, String> purgeBuildTaskResources(@NonNull ProjectModel project, @NonNull BuildTaskModel task) {
        String projectName = project.getName();
        long taskId = task.getId();

        if (task.getType() != BuildTaskType.NormalBuild) {
            throw youCannotPurgeTheTaskAs("it is not a NormalBuild");
        }

        if (task.getUpdatedAt().isAfter(OffsetDateTime.now().minusMonths(3))) {
            throw youCannotPurgeTheTaskAs("it was updated in the past 3 months");
        }

        if (project.getType() == ProjectType.Miniprogram) {
            throw youCannotPurgeTheTaskAs("it is owned by a miniprogram project");
        }

        Stream.<Runnable>of(() -> assertDoesNotInLast30Records(task), () -> assertDoesNotUsingInDev(task),
            () -> assertNoReleaseHistory(task), () -> assertNoReleaseFlag(projectName, taskId),
            () -> assertNoLlmReleaseFlag(projectName, taskId), () -> assertComponentNeverPublished(project, taskId),
            () -> assertWorkersSdkNeverPublished(project, taskId)

        ).parallel().forEach(Runnable::run);

        final Storage storage = Region.defaultStorage();
        StorageHelper.setFlag(storage, projectName, taskId, TaskFlagName.Purged);

        String message = Stream.concat(Arrays.stream(TaskDirName.values()).parallel()
                    .map(dir -> Pair.of(dir, StorageHelper.purge(storage, projectName, taskId, dir))),
                Arrays.stream(TaskFileName.values()).parallel()
                    .map(file -> Pair.of(file, StorageHelper.purge(storage, projectName, taskId, file))))
            .map(i -> i.getKey().numPairToString(i.getValue().size())).filter(Objects::nonNull)
            .map(i -> String.format("%d %s", i.getKey(), i.getValue()))
            .collect(EnglishListBuilder.collect("Successfully purged ", ".")).toString();

        return Maps.of("message", message);
    }

    private void assertWorkersSdkNeverPublished(ProjectModel project, long taskId) {
        if (project.getType() != ProjectType.Workers) return;
        if (metaService.hasValue(MetaType.WorkersSDKPublished, taskId)) {
            throw youCannotPurgeTheTaskAs("its Workers SDK was published");
        }
    }

    private void assertComponentNeverPublished(@NonNull ProjectModel project, long taskId) {
        if (project.getType() != ProjectType.Component) return;
        List<NpmPublishRecord> list = historyService.searchNpmPublishHistory(
            new SearchNpmPublishHistoryParams(project.getId(), taskId).limit(1));
        if (!list.isEmpty()) {
            throw youCannotPurgeTheTaskAs("it was published to NPM");
        }
    }

    private void assertDoesNotUsingInDev(@NonNull BuildTaskModel task) {
        // If the task is currently being used in any env environment, it cannot be purged.
        if (historyService.isBeingUsedInTestEnv(task.getProjectId(), task.getId())) {
            throw youCannotPurgeTheTaskAs("it is being used in testing environment");
        }
    }

    private void assertDoesNotInLast30Records(@NonNull BuildTaskModel task) {
        try {
            Set<Long> recentIds = last30taskIdscache.get(task.getProjectId(),
                // This operation is very slow for a project that has too many tasks, so the cache is needed here.
                () -> buildTaskService.search(task.getProjectId(), 0, 30, Collections.emptyMap()).stream()
                    .map(BuildTaskModel::getId).collect(Collectors.toSet()));
            if (recentIds.contains(task.getId())) {
                throw youCannotPurgeTheTaskAs("it is contained within the latest 30 records");
            }
        } catch (ExecutionException e) {
            throw new InternalException(e);
        }
    }

    private void assertNoReleaseFlag(@NonNull String projectName, long taskId) {
        // If the guard file has been set, it indicates that the task has been released in the region.
        // IMPORTANT: The resources of a released task must not be purged.
        if (StorageHelper.hasFlag(Region.defaultStorage(), projectName, taskId, TaskFlagName.Released)) {
            throw youCannotPurgeTheTaskAs("it has a released flag");
        }
    }

    private void assertNoLlmReleaseFlag(@NonNull String projectName, long taskId) {
        TaskSyncInfo info = buildTaskService.findLlmSyncInfo(taskId);
        // Null sync info means the resource is not copied to llm.
        // This indicates that the task has never been deployed in the dev environment, let alone release production.
        if (info == null) return;

        // The sync status is running, which means the resources are being read, so the task cannot be purged now.
        if (info.getStatus() == TaskSyncInfo.Status.PENDING) {
            throw youCannotPurgeTheTaskAs("its llm sync status is running");
        }

        // If the resources have been synced to llm, the released flag should be checked.
        // Even if the sync is completed, as long as it has never been released in production, the task can also be purged.
        // Only the released flag has been set in llm, the task cannot be purged, throw a reason here.
        if (StorageHelper.hasFlag(RegionStorage.oversea.getService(), projectName, taskId, TaskFlagName.Released)) {
            throw youCannotPurgeTheTaskAs("it has a released flag in llm");
        }
    }

    private void assertNoReleaseHistory(@NonNull BuildTaskModel task) {
        // IMPORTANT: The resources of a released task must not be purged.
        if (historyService.wasReleased(task.getProjectId(), task.getId())) {
            throw youCannotPurgeTheTaskAs("it was released");
        }
    }

    @PostConstruct
    private void init() {
        last30taskIdscache = CacheBuilder.newBuilder().expireAfterWrite(30, TimeUnit.MINUTES).build();
    }
}
