package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.UserLogType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.util.Set;

@Getter
@Setter
public class UserLogSearchParamsWithoutUser {
    @Nullable
    private Set<Long> projectIds;
    @Nullable
    private Set<UserLogType> types;

    @Nullable
    private String keyword;

    @Nullable
    private OffsetDateTime startTime;
    @Nullable
    private OffsetDateTime endTime;

    @JsonProperty(defaultValue = "0")
    private int page;
    @JsonProperty(defaultValue = "10")
    private int size;
}
