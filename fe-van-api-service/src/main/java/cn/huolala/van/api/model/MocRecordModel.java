package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.api.constants.enums.MocRegionEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

@Getter
@Setter
public class MocRecordModel {
    private String title;
    @JsonProperty("service_name")
    private String serviceName;
    private String source;
    private String action;
    private String env;
    @JsonFormat(shape = JsonFormat.Shape.NUMBER_INT)
    private MocRegionEnum region;
    @JsonProperty("deploy_user")
    private String deployUser;
    private String status;
    @JsonProperty("start_time")
    private Long startTime;
    @JsonProperty("end_time")
    private Long endTime;
    private String message;

    @NonNull
    private static MocRegionEnum toMocRegionEnum(Region region) {
        switch (region) {
            case cn:
                return MocRegionEnum.CN;
            case sin:
                return MocRegionEnum.SG;
            case sao:
                return MocRegionEnum.BR;
            case bom:
                return MocRegionEnum.IN;
            default:
                return MocRegionEnum.UNKNOWN;
        }
    }

    @NonNull
    public MocRecordModel region(MocRegionEnum region) {
        this.region = region;
        return this;
    }

    @NonNull
    public MocRecordModel region(Region region) {
        this.region = toMocRegionEnum(region);
        return this;
    }

    @NonNull
    public MocRecordModel status(String status) {
        this.status = status;
        return this;
    }

    @NonNull
    public MocRecordModel env(String env) {
        this.env = env;
        return this;
    }

    @NonNull
    public MocRecordModel env(Env env) {
        this.env = env.name();
        return this;
    }

    @NonNull
    public MocRecordModel action(String action) {
        this.action = action;
        return this;
    }

    @NonNull
    public MocRecordModel title(String title) {
        this.title = title;
        return this;
    }

    @NonNull
    public MocRecordModel message(String message) {
        this.message = message;
        return this;
    }
}
