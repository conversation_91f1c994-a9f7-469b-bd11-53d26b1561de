package cn.huolala.van.api.util;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import static java.lang.String.format;

public class RegExpToLikeConverter {
    @NonNull
    private final StringBuilder builder;
    @NonNull
    private final int[] points;
    @Nullable
    private final String pattern;
    private final int length;
    private int index;

    public RegExpToLikeConverter(@Nullable String pattern) {
        this.pattern = pattern;
        this.builder = new StringBuilder();

        if (pattern == null || pattern.isEmpty()) {
            points = new int[0];
        } else {
            points = pattern.codePoints().toArray();
            if (points[0] == '^') {
                index = 1;
            } else {
                builder.append('%');
            }
        }

        if (points[points.length - 1] == '$') {
            length = points.length - 1;
        } else {
            length = points.length;
        }

        while (!eof()) digestNext();

        // Append the '%' as the last position, if the end of pattern is not '$'.
        if (length == points.length) builder.append('%');
    }

    private int h2i(int p) {
        if (p >= '0' && p <= '9') return p - '0';
        if (p >= 'a' && p <= 'f') return p - 'a' + 10;
        if (p >= 'A' && p <= 'F') return p - 'A' + 10;
        throw buildException(format("bad hex character '%c'", p));
    }

    private void digestNext() {
        int p = read();

        if (p == '.') {
            builder.append(readWildcard());
            return;
        }

        if ("+*?^|$()[]{}".indexOf(p) > -1) throw buildException(format("'%c'", p));

        if (p == '\\') {
            p = read();
            Integer v = readEscapedValue(p);
            if (v != null) {
                builder.appendCodePoint(v);
                return;
            }
            if ("DdWwSsPpBb".indexOf(p) > -1) throw buildException(format("'\\%c'", p));
        }

        if (p == '%' || p == '_' || p == '\\') builder.append('\\');
        builder.appendCodePoint(p);
    }

    private UnsupportedRegExp buildException(String msg) {
        return new UnsupportedRegExp(format("The RegExp /%s/ contains %s, which is not supported", pattern, msg));
    }

    private String readWildcard() {
        if (!eof()) {
            if (head() == '*') {
                read();
                return "%";
            }
            if (head() == '+') {
                read();
                return "_%";
            }
        }
        return "_";
    }

    private Integer readEscapedValue(int p) {
        if (p == 'x') return h2i(read()) * 16 + h2i(read());
        if (p == 'u') return h2i(read()) * 4096 + h2i(read()) * 256 + h2i(read()) * 16 + h2i(read());
        if (p == 'r') return (int) '\r';
        if (p == 'n') return (int) '\n';
        if (p == 't') return (int) '\t';
        if (p == '0') return (int) '\0';
        return null;
    }

    private boolean eof() {
        return index >= length;
    }

    private int head() {
        if (eof()) throw buildException("unexpected EOF");
        return points[index];
    }

    private int read() {
        if (eof()) throw buildException("unexpected EOF");
        return points[index++];
    }

    @Override
    public String toString() {
        return builder.toString();
    }

    public static class UnsupportedRegExp extends RuntimeException {
        public UnsupportedRegExp(String message) {
            super(message);
        }
    }
}
