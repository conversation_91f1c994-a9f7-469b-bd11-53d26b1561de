package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.UserLogType;
import cn.huolala.van.api.dao.entity.UserLogEntity;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Optional;

@Getter
@Setter
public class UserLogModel extends UserBase {
    private long id;
    private long projectId;

    @NonNull
    private UserLogType type;
    @NonNull
    private String description;
    @NonNull
    private OffsetDateTime createdAt;
    @NonNull
    private OffsetDateTime updatedAt;
    @NonNull
    private String meta;

    public UserLogModel(@NonNull UserModel user, @NonNull UserLogEntity entity) {
        super(user.getUniqId(), user.getName());
        id = entity.getId();
        projectId = entity.getProjectId();
        type = entity.getType();
        description = Optional.ofNullable(entity.getDescription()).orElse("");
        createdAt = entity.getCreatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
        updatedAt = entity.getUpdatedAt().atZone(ZoneId.systemDefault()).toOffsetDateTime();
        meta = Optional.ofNullable(entity.getMeta()).orElse("");
    }

    public static UserLogModel create(@Nullable UserLogEntity entity, @Nullable UserModel user) {
        if (entity == null || user == null) return null;
        return new UserLogModel(user, entity);
    }
}
