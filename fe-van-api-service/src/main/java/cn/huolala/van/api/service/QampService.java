package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.model.tasks.BuildTaskModel;
import cn.huolala.van.api.model.Region;
import cn.huolala.van.api.model.UserModel;
import cn.huolala.van.api.model.project.ProjectModel;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Collection;

public interface QampService {
    void notify(@NonNull UserModel user,
                @NonNull ProjectModel project,
                @NonNull Collection<BuildTaskModel> tasks,
                @NonNull Env env,
                @NonNull Region region,
                @Nullable Long launchId);
}
