package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.Env;
import cn.huolala.van.api.exception.DirtyDataException;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.Objects;

public class ConfigEnv {
    private static final String stablePrefix = "stable";

    @Nullable
    private Env name;
    private int number;

    public ConfigEnv() {
        number = 0;
    }

    public ConfigEnv(@NonNull Env name, int number) {
        this.name = name;
        this.number = number;
    }

    @NonNull
    public static ConfigEnv from(@NonNull Env env, @Nullable String stableName) {
        return new ConfigEnv(env, parseStableName(stableName));
    }

    @NonNull
    public static ConfigEnv from(@NonNull String name, @Nullable String stableName) {
        return new ConfigEnv(EnumUtils.getEnum(Env.class, name), parseStableName(stableName));
    }

    /**
     * @param stableName `stable` or `stable-${n}`
     * @return The ${n}
     */
    private static int parseStableName(@Nullable String stableName) {
        if (stableName != null) {
            if (stableName.equals(stablePrefix)) return 0;
            if (stableName.startsWith(stablePrefix + "-")) {
                try {
                    return Integer.parseInt(stableName.substring(stablePrefix.length() + 1));
                } catch (NumberFormatException ignored) {
                    // noop
                }
            }
        }
        throw DirtyDataException.create(ConfigEnv.class.getSimpleName(), "stableName", stableName);
    }

    @Nullable
    public static ConfigEnv fromString(@Nullable String raw) {
        String name = null;
        int number = 0;
        if (raw != null) {
            String[] matches = raw.split("[_-]");
            if (matches.length > 0) name = matches[0];
            if (matches.length == 2) {
                try {
                    number = Integer.parseInt(matches[1]);
                } catch (NumberFormatException ignored) {
                    // TODO: print error log?
                }
            }
        }
        try {
            return new ConfigEnv(Env.valueOf(name), number);
        } catch (Exception e) {
            return null;
        }
    }

    @NonNull
    public static String buildStableName(int n) {
        if (n == 0) return stablePrefix;
        return stablePrefix + "-" + n;
    }

    @Override
    public String toString() {
        Objects.requireNonNull(name);
        if (number == 0) return name.name();
        return name.name() + "-" + number;
    }

    @NonNull
    public Env getName() {
        Objects.requireNonNull(name);
        return name;
    }

    public void setName(@NonNull Env name) {
        this.name = name;
    }

    @NonNull
    public String getStableName() {
        return buildStableName(number);
    }

    public void setStableName(@NonNull String stableName) {
        number = parseStableName(stableName);
    }

    @JsonIgnore
    public int getNumber() {
        return number;
    }
}
