package cn.huolala.van.api.model;

import cn.huolala.api.constants.enums.Env;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.NonNull;

@Getter
@Setter
public class EnvRegion {
    @NonNull
    private Env env;
    @NonNull
    private Region region;

    public EnvRegion(@NonNull Env env, @NonNull Region region) {
        this.env = env;
        this.region = region;
    }

    public EnvRegion(@NonNull ObjectNode node) {
        this(
                Env.valueOf(node.get("env").asText()),
                Region.valueOf(node.get("region").asText())
        );
    }
}

