package cn.huolala.van.api.service;

import cn.huolala.api.constants.enums.ProjectType;
import cn.huolala.van.api.facade.model.IdOrName;
import cn.huolala.van.api.model.*;
import cn.huolala.van.api.model.meta.BuildCacheInfo;
import cn.huolala.van.api.model.meta.DeployHookModel;
import cn.huolala.van.api.model.meta.MiniprogramInfoModel;
import cn.huolala.van.api.model.project.ProjectBoilerplate;
import cn.huolala.van.api.model.project.ProjectConfig;
import cn.huolala.van.api.model.project.ProjectModel;
import cn.huolala.van.api.model.project.ProjectSummary;

import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.stream.Stream;

public interface ProjectService {

    @NonNull
    Long create(@NonNull ProjectCreateParams params, @NonNull UserModel user);

    @NonNull
    Optional<ProjectModel> get(@Nullable Long id);

    @NonNull
    ProjectModel getNeverNull(@Nullable Long id);

    @NonNull
    ProjectModel getNeverNull(@Nullable IdOrName ion);

    @NonNull
    PageResult<ProjectSummary> searchForUser(@NonNull UserModel user, @NonNull ProjectSearchParams params);

    @NonNull
    List<ProjectSummary> findForUser(@NonNull UserModel user, Set<Long> ids);

    @NonNull
    List<ProjectModel> find(Set<Long> ids);

    @NonNull
    Stream<String> findNamesByTypes(ProjectType... types);

    @NonNull
    String getNameById(Long id);

    @NonNull
    Long getIdByName(String name);

    @NonNull
    Map<String, Long> findIdByNames(Collection<String> names);

    @NonNull
    Map<Long, String> findNameByIds(Collection<Long> ids);

    @NonNull
    VanProjectConfig getVanProjectConfig(@NonNull Region region, long projectId);

    @NonNull
    Change<VanProjectConfig> partialUpdateVanProjectConfig(long projectId,
                                                           @NonNull Region region,
                                                           @NonNull NonNullConsumer<VanProjectConfig> handler);

    @NonNull
    Change<ProjectConfig> partialUpdateConfig(long projectId, @NonNull NonNullConsumer<ProjectConfig> handler);

    @NonNull
    Map<ProjectType, Integer> countEachTypes();

    @Nullable
    Map<String, BuildCacheInfo> getBuildCacheStatus(long projectId);

    Change<String> updateDescription(long projectId, @NonNull String description);

    @NonNull
    DeployHookModel getDeployHookConfig(long projectId);

    @NonNull
    Change<DeployHookModel> updateDeployHookConfig(long projectId, @NonNull DeployHookModel data);

    @NonNull
    Map<String, Long> findAllProjects();

    @NonNull
    int pullUsersFromLone(@NonNull long projectId, @NonNull String appId);

    @NonNull
    long countByName(@NonNull String name);

    boolean validateName(@NonNull String name, @NonNull UserModel user);

    void validateAppId(@NonNull String appId, @NonNull UserModel user);

    boolean validateRepository(@NonNull String link, @NonNull UserModel user);


    @NonNull
    List<ProjectBoilerplate> getBoilerplateList(@NonNull String type, @NonNull int groupId);

    @NonNull
    void createMiniprogramMeta(@NonNull Long metaId, @NonNull MiniprogramInfoModel miniprogramInfoModel);

    @NonNull
    int archive(@NonNull long projectId);

    @NonNull
    ResponseEntity<String> getRawFile(@NonNull long projectId, @NonNull String path, @NonNull String ref);

    @NonNull
    String applyDomain(@NonNull ProjectModel project, @NonNull UserModel user, @NonNull String domainValue,
            @NonNull String domainType, @Nullable String remark);

    @NonNull
    String mergeRequest(@NonNull ProjectModel project, @NonNull UserModel user, @NonNull long iid, @Nullable MergeMrParams params);

    @NonNull
    MetaModel getWorkerPublishInfo(@NonNull long projectId, @NonNull long taskId);

    @NonNull
    long publishWorker(@NonNull long projectId, @NonNull long taskId, @NonNull UserModel user);

    Change<String> updateRepository(@NonNull long projectId, @NonNull String repository);

}
