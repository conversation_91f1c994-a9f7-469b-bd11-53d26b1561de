package cn.huolala.van.api.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(code = HttpStatus.BAD_REQUEST)
public class VanBadRequestException extends RuntimeException {
    public VanBadRequestException(Exception exception) {
        super(exception);
    }

    public VanBadRequestException(String message) {
        super(message);
    }

    public VanBadRequestException(String message, Exception reason) {
        super(message, reason);
    }
}
