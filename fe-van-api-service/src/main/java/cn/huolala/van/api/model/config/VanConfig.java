package cn.huolala.van.api.model.config;

import cn.huolala.api.constants.enums.Env;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class VanConfig {
    private String content;
    private String groupName;
    private String name;
    private Env env;
    private String projectName;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date latestCommitId;
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date updatedAt;

    public Env getEnv() {
        if (env == null) {
            return Env.prd;
        }
        return env;
    }
}
