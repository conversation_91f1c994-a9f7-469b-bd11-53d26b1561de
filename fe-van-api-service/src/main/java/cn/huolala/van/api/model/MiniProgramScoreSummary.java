package cn.huolala.van.api.model;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class MiniProgramScoreSummary {
	private double score;
	private FieldSummary apiDurationUpperTime;
	private FieldSummary apiSuccessRate;
	private FieldSummary loadPackageDurationUpperTime;
	private FieldSummary appLaunchDurationUpperTime;
	private FieldSummary pageLoadDurationUpperTime;
	private FieldSummary evaluateScriptDurationUpperTime;
	private FieldSummary fcpDurationUpperTime;
	private FieldSummary lcpDurationUpperTime;
	private FieldSummary firstInteractionDurationUpperTime;
	private FieldSummary pageFirstRenderDurationUpperTime;
	private FieldSummary firstPaintDurationUpperTime;
	private FieldSummary notResolveError;

	public List<FieldSummary> toList() {
		List<FieldSummary> fieldSummaryList = new ArrayList<>();
		fieldSummaryList.add(apiDurationUpperTime);
		fieldSummaryList.add(apiSuccessRate);
		fieldSummaryList.add(loadPackageDurationUpperTime);
		fieldSummaryList.add(appLaunchDurationUpperTime);
		fieldSummaryList.add(pageLoadDurationUpperTime);
		fieldSummaryList.add(evaluateScriptDurationUpperTime);
		fieldSummaryList.add(fcpDurationUpperTime);
		fieldSummaryList.add(lcpDurationUpperTime);
		fieldSummaryList.add(firstInteractionDurationUpperTime);
		fieldSummaryList.add(pageFirstRenderDurationUpperTime);
		fieldSummaryList.add(firstPaintDurationUpperTime);
		fieldSummaryList.add(notResolveError);
		return fieldSummaryList;
	}

	public void caculate() {
		List<FieldSummary> fieldSummaryList = this.toList();
		double score = 0;
		double totalScore = 0;
		for (FieldSummary fieldSummary : fieldSummaryList) {
			if (fieldSummary.enable) {
				totalScore = totalScore + fieldSummary.totalScore;
				score = score + fieldSummary.score;
			}
		}

		this.score = (score / totalScore) * 100;
	}

	public MiniProgramScoreSummary clone() {
		MiniProgramScoreSummary miniProgramScoreSummary = new MiniProgramScoreSummary();
		miniProgramScoreSummary.setApiDurationUpperTime(this.apiDurationUpperTime.clone());
		miniProgramScoreSummary.setApiSuccessRate(this.apiSuccessRate.clone());
		miniProgramScoreSummary.setLoadPackageDurationUpperTime(this.loadPackageDurationUpperTime.clone());
		miniProgramScoreSummary.setAppLaunchDurationUpperTime(this.appLaunchDurationUpperTime.clone());
		miniProgramScoreSummary.setPageLoadDurationUpperTime(this.pageLoadDurationUpperTime.clone());
		miniProgramScoreSummary.setEvaluateScriptDurationUpperTime(this.evaluateScriptDurationUpperTime.clone());
		miniProgramScoreSummary.setFcpDurationUpperTime(this.fcpDurationUpperTime.clone());
		miniProgramScoreSummary.setLcpDurationUpperTime(this.lcpDurationUpperTime.clone());
		miniProgramScoreSummary.setFirstInteractionDurationUpperTime(this.firstInteractionDurationUpperTime.clone());
		miniProgramScoreSummary.setPageFirstRenderDurationUpperTime(this.pageFirstRenderDurationUpperTime.clone());
		miniProgramScoreSummary.setFirstPaintDurationUpperTime(this.firstPaintDurationUpperTime.clone());
		miniProgramScoreSummary.setNotResolveError(this.notResolveError.clone());
		return miniProgramScoreSummary;
	}

	@Data
	public static class FieldSummary {
		private boolean enable;
		private double totalScore;
		private String title;
		private String unit;
		private String description;
		// list value score 0 -> 100 range 长度至少为 2
		// 为 (x, y) => (value, scorePercent) 映射函数, y -> (0,1)
		private List<FieldScoreRange> vitals;
		private List<FieldScoreRange> percentile;

		private double score;
		private double value;

		public FieldSummary clone() {
			FieldSummary fieldSummary = new FieldSummary();
			fieldSummary.enable = this.enable;
			fieldSummary.title = this.title;
			fieldSummary.unit = this.unit;
			fieldSummary.totalScore = this.totalScore;
			fieldSummary.description = this.description;
			fieldSummary.vitals = this.vitals;
			fieldSummary.percentile = this.percentile;
			return fieldSummary;
		}

		public void addValue(Double value) {
			if (value == null) {
				this.enable = false;
				this.score = 0;
				return;
			}

			this.value = value;
			if (vitals == null || vitals.size() < 2) {
				this.enable = false;
				this.score = 0;
				return;
			}

			FieldScoreRange first = vitals.get(0);
			FieldScoreRange end = vitals.get(vitals.size() - 1);
			if (first.value > end.value) {
				if (value >= first.value) {
					this.score = first.scorePercent * totalScore;
					return;
				}

				if (value <= end.value) {
					this.score = end.scorePercent * totalScore;
					return;
				}

				FieldScoreRange left = first;
				FieldScoreRange right = vitals.get(1);

				for (FieldScoreRange range : vitals) {
					if (value > range.value) {
						right = range;
						break;
					}
					left = range;
				}

				this.score = (left.scorePercent
						+ ((right.scorePercent - left.scorePercent) / (right.value - left.value))
								* (value - left.value))
						* totalScore;
				return;
			}

			if (value <= first.value) {
				this.score = first.scorePercent * totalScore;
				return;
			}

			if (value >= end.value) {
				this.score = end.scorePercent * totalScore;
				return;
			}

			FieldScoreRange left = first;
			FieldScoreRange right = vitals.get(1);

			for (FieldScoreRange range : vitals) {
				if (value < range.value) {
					right = range;
					break;
				}
				left = range;
			}

			this.score = (((right.scorePercent - left.scorePercent) / (right.value - left.value))
					* (value - left.value)
					+ left.scorePercent) * totalScore;

		}
	}

	@Data
	public static class FieldScoreRange {
		private double value;
		private double scorePercent;
	}
}
