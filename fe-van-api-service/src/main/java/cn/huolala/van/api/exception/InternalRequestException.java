package cn.huolala.van.api.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.net.URI;

@ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
@Getter
public class InternalRequestException extends RuntimeException {
    private final int status;

    public InternalRequestException(Throwable exception) {
        super(exception);
        status = 0;
    }

    public InternalRequestException(String message) {
        super(message);
        status = 0;
    }

    public InternalRequestException(String message, Exception reason) {
        super(message, reason);
        status = 0;
    }

    public InternalRequestException(int status, String message) {
        super(message);
        this.status = status;
    }

    public InternalRequestException(int status, @NonNull URI uri) {
        this(status, String.format("Got a %d status code from %s", status, uri));
    }

    public InternalRequestException(int status, @NonNull URI uri, @NonNull String message) {
        this(status, String.format("Got a %d status code from %s, and the message is '%s'", status, uri, message));
    }
}
