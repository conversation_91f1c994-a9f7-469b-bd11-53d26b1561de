<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>cn.huolala</groupId>
    <artifactId>fe-van-api</artifactId>
    <version>1.0.1-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>fe-van-api-service</artifactId>


  <dependencies>

    <dependency>
      <groupId>cn.huolala</groupId>
      <artifactId>fe-van-api-dao</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-config</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.lalaframework</groupId>
      <artifactId>lala-jaf-monitor-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-cache-lock</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.lalaframework</groupId>
      <artifactId>storage-adapter</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>cn.lalaweb</groupId>
      <artifactId>alidns</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.15.3</version>
    </dependency>

    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-job</artifactId>
    </dependency>

    <dependency>
      <groupId>com.larksuite.oapi</groupId>
      <artifactId>oapi-sdk</artifactId>
      <version>2.4.17</version>
    </dependency>
    <dependency>
      <groupId>cn.lalaweb</groupId>
      <artifactId>tools-facade</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <!-- 标准soa demo测试依赖接口 -->
    <!-- jaf soa dependency stop -->

    <!-- jaf ehcache dependency start -->
    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-cache-ehcache</artifactId>
    </dependency>
    <!-- jaf ehcache dependency stop -->

    <!-- jaf spring redis dependency start -->
    <!-- !!!!!!It's not compatible with lala-easyopen as the asm library that has a version
    conflict!!!!! -->
    <!--        <dependency>-->
    <!--            <groupId>cn.lalaframework.boot</groupId>-->
    <!--            <artifactId>lala-boot-starter-cache-springredis</artifactId>-->
    <!--        </dependency>-->
    <!-- jaf spring redis dependency stop -->

    <!-- lala spring kafka dependency start -->
    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-jms-springkafka</artifactId>
    </dependency>
    <!-- lala spring kafka dependency stop -->

    <!-- lala spring rabbitmq dependency start -->
    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-jms-rabbitmq</artifactId>
    </dependency>
    <!-- lala spring rabbitmq dependency stop -->

    <!-- jaf dynamic dependency start -->
    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-dynamic</artifactId>
    </dependency>
    <!-- jaf dynamic dependency stop -->
    <!-- jaf elasticsearch dependency start -->
    <dependency>
      <groupId>co.elastic.clients</groupId>
      <artifactId>elasticsearch-java</artifactId>
      <version>7.16.3</version>
    </dependency>

    <dependency>
      <groupId>com.unfbx</groupId>
      <artifactId>chatgpt-java</artifactId>
      <version>1.0.5</version>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version>
    </dependency>
    <dependency>
      <groupId>cn.lalaweb</groupId>
      <artifactId>coupe</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-cache-springredis</artifactId>
      <exclusions>
        <exclusion>
          <groupId>asm</groupId>
          <artifactId>asm</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- 项目添加如下依赖-->
    <dependency>
      <groupId>cn.lalaframework.boot</groupId>
      <artifactId>lala-boot-starter-message-all</artifactId>
    </dependency>
  </dependencies>
</project>
